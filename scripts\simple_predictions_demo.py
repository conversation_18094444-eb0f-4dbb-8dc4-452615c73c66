#!/usr/bin/env python3
"""
Simple demonstration of prediction capabilities
"""
import sys
from pathlib import Path
import pandas as pd
import numpy as np
import torch
from datetime import datetime, timedelta
from sqlalchemy import text, create_engine

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.data.storage import TickDataStorage
from src.ml.features import FeatureEngineering
from src.ml.models.lstm import TickDataPredictor
from src.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

def create_mock_predictions(tick_data, features):
    """Create realistic mock predictions based on actual data patterns"""
    
    # Analyze recent price movement
    recent_prices = tick_data['price'].tail(20)
    price_trend = (recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0]
    
    # Analyze volume patterns
    recent_volume = tick_data['volume'].tail(20)
    volume_trend = recent_volume.mean()
    
    # Calculate volatility
    price_volatility = recent_prices.std() / recent_prices.mean()
    
    # Generate direction prediction based on trends
    if price_trend > 0.002:  # Rising trend
        direction = 'UP'
        confidence = min(0.9, 0.6 + abs(price_trend) * 100)
    elif price_trend < -0.002:  # Falling trend
        direction = 'DOWN'
        confidence = min(0.9, 0.6 + abs(price_trend) * 100)
    else:  # Sideways
        direction = 'NEUTRAL'
        confidence = 0.5 + np.random.uniform(0, 0.2)
    
    # Calculate magnitude based on volatility
    magnitude = min(0.8, price_volatility * 50 + np.random.uniform(0.1, 0.3))
    
    # Generate price prediction
    current_price = tick_data['price'].iloc[-1]
    if direction == 'UP':
        predicted_price = current_price * (1 + magnitude * 0.01)
    elif direction == 'DOWN':
        predicted_price = current_price * (1 - magnitude * 0.01)
    else:
        predicted_price = current_price * (1 + np.random.uniform(-0.002, 0.002))
    
    # Calculate support and resistance
    price_range = recent_prices.max() - recent_prices.min()
    support_levels = [
        recent_prices.min(),
        current_price - price_range * 0.3,
        current_price - price_range * 0.1
    ]
    resistance_levels = [
        current_price + price_range * 0.1,
        current_price + price_range * 0.3,
        recent_prices.max()
    ]
    
    return {
        'direction': direction,
        'confidence': confidence,
        'magnitude': magnitude,
        'predicted_price': predicted_price,
        'current_price': current_price,
        'price_change_pct': ((predicted_price - current_price) / current_price) * 100,
        'key_levels': {
            'support': sorted(support_levels),
            'resistance': sorted(resistance_levels)
        },
        'analysis': {
            'price_trend': price_trend,
            'volume_trend': volume_trend,
            'volatility': price_volatility,
            'recent_price_range': price_range
        }
    }

def validate_prediction_accuracy(predictions, actual_outcomes):
    """Validate prediction accuracy against actual outcomes"""
    
    if not predictions or not actual_outcomes:
        return {'error': 'No data to validate'}
    
    correct_directions = 0
    total_predictions = len(predictions)
    
    direction_accuracy_by_confidence = {'high': [], 'medium': [], 'low': []}
    magnitude_errors = []
    price_errors = []
    
    for pred, actual in zip(predictions, actual_outcomes):
        # Direction accuracy
        if pred['direction'] == actual['actual_direction']:
            correct_directions += 1
        
        # Categorize by confidence
        confidence = pred['confidence']
        is_correct = pred['direction'] == actual['actual_direction']
        
        if confidence > 0.8:
            direction_accuracy_by_confidence['high'].append(is_correct)
        elif confidence > 0.6:
            direction_accuracy_by_confidence['medium'].append(is_correct)
        else:
            direction_accuracy_by_confidence['low'].append(is_correct)
        
        # Magnitude error
        actual_magnitude = abs(actual['price_change_pct']) / 100
        magnitude_error = abs(pred['magnitude'] - actual_magnitude)
        magnitude_errors.append(magnitude_error)
        
        # Price error
        price_error_pct = abs(pred['predicted_price'] - actual['closing_price']) / actual['closing_price'] * 100
        price_errors.append(price_error_pct)
    
    # Calculate metrics
    direction_accuracy = correct_directions / total_predictions
    avg_magnitude_error = np.mean(magnitude_errors)
    avg_price_error = np.mean(price_errors)
    
    confidence_accuracy = {}
    for level, results in direction_accuracy_by_confidence.items():
        if results:
            confidence_accuracy[level] = {
                'accuracy': np.mean(results),
                'count': len(results)
            }
        else:
            confidence_accuracy[level] = {'accuracy': 0, 'count': 0}
    
    return {
        'total_predictions': total_predictions,
        'direction_accuracy': direction_accuracy,
        'correct_directions': correct_directions,
        'average_magnitude_error': avg_magnitude_error,
        'average_price_error_pct': avg_price_error,
        'confidence_accuracy': confidence_accuracy,
        'benchmark_comparison': {
            'random_baseline': 0.33,
            'improvement_over_random': direction_accuracy - 0.33
        }
    }

def run_prediction_demo():
    """Run prediction demonstration"""
    logger.info("🎯 Starting Prediction Capabilities Demo")
    
    # Use the test database
    db_path = "test_tickdata.db"
    connection_string = f"sqlite:///{db_path}"
    
    if not Path(db_path).exists():
        logger.error(f"Database {db_path} not found. Please run process_sample_data.py first.")
        return False
    
    try:
        # Initialize components
        storage = TickDataStorage(connection_string)
        feature_engineering = FeatureEngineering()
        
        # Get available data
        with storage.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT 
                    index_name,
                    MIN(DATE(timestamp)) as start_date,
                    MAX(DATE(timestamp)) as end_date,
                    COUNT(*) as record_count
                FROM tick_data 
                GROUP BY index_name
                ORDER BY record_count DESC
            """))
            
            data_info = []
            for row in result.fetchall():
                data_info.append({
                    'index_name': row[0],
                    'start_date': row[1],
                    'end_date': row[2],
                    'record_count': row[3]
                })
        
        if not data_info:
            logger.error("No data available")
            return False
        
        logger.info("📊 Available Data:")
        for info in data_info:
            logger.info(f"  {info['index_name']}: {info['record_count']:,} records ({info['start_date']} to {info['end_date']})")
        
        # Select index with most data
        best_index = data_info[0]['index_name']
        logger.info(f"\n🎯 Generating predictions for: {best_index}")
        
        # Get recent data for prediction
        end_date = data_info[0]['end_date']
        start_date = (datetime.strptime(end_date, '%Y-%m-%d') - timedelta(days=5)).strftime('%Y-%m-%d')
        
        tick_data = storage.get_tick_data(best_index, start_date, end_date)
        logger.info(f"Using {len(tick_data)} recent tick records")
        
        # Extract features
        logger.info("🔧 Extracting features...")
        features = feature_engineering.extract_all_features(tick_data)
        logger.info(f"Extracted {len(features.columns)} features")
        
        # Generate predictions for multiple scenarios
        predictions = []
        actual_outcomes = []
        
        # Split data into chunks for validation
        chunk_size = len(tick_data) // 5  # 5 prediction scenarios
        
        for i in range(4):  # Generate 4 predictions
            start_idx = i * chunk_size
            end_idx = (i + 1) * chunk_size
            
            # Use data up to end_idx for prediction
            train_data = tick_data.iloc[:end_idx]
            
            # Predict for the next chunk
            if end_idx + chunk_size <= len(tick_data):
                test_data = tick_data.iloc[end_idx:end_idx + chunk_size]
                
                # Generate prediction
                prediction = create_mock_predictions(train_data, features.iloc[:end_idx])
                prediction['scenario'] = f"Scenario {i+1}"
                prediction['prediction_date'] = end_date
                
                # Calculate actual outcome
                actual_outcome = {
                    'opening_price': test_data['price'].iloc[0],
                    'closing_price': test_data['price'].iloc[-1],
                    'high_price': test_data['price'].max(),
                    'low_price': test_data['price'].min(),
                }
                
                price_change = actual_outcome['closing_price'] - actual_outcome['opening_price']
                actual_outcome['price_change_pct'] = (price_change / actual_outcome['opening_price']) * 100
                
                if actual_outcome['price_change_pct'] > 0.1:
                    actual_outcome['actual_direction'] = 'UP'
                elif actual_outcome['price_change_pct'] < -0.1:
                    actual_outcome['actual_direction'] = 'DOWN'
                else:
                    actual_outcome['actual_direction'] = 'NEUTRAL'
                
                predictions.append(prediction)
                actual_outcomes.append(actual_outcome)
        
        # Display predictions
        logger.info("\n🎯 Generated Predictions:")
        logger.info("=" * 60)
        
        for i, pred in enumerate(predictions):
            logger.info(f"\n{pred['scenario']}:")
            logger.info(f"  Direction: {pred['direction']} (confidence: {pred['confidence']:.3f})")
            logger.info(f"  Magnitude: {pred['magnitude']:.3f}")
            logger.info(f"  Current Price: {pred['current_price']:.2f}")
            logger.info(f"  Predicted Price: {pred['predicted_price']:.2f}")
            logger.info(f"  Expected Change: {pred['price_change_pct']:.2f}%")
            logger.info(f"  Support: {[f'{s:.2f}' for s in pred['key_levels']['support'][:2]]}")
            logger.info(f"  Resistance: {[f'{r:.2f}' for r in pred['key_levels']['resistance'][:2]]}")
        
        # Validate predictions
        logger.info("\n📈 Prediction Validation:")
        logger.info("=" * 60)
        
        validation_results = validate_prediction_accuracy(predictions, actual_outcomes)
        
        logger.info(f"Total Predictions: {validation_results['total_predictions']}")
        logger.info(f"Direction Accuracy: {validation_results['direction_accuracy']:.1%}")
        logger.info(f"Correct Directions: {validation_results['correct_directions']}")
        logger.info(f"Average Price Error: {validation_results['average_price_error_pct']:.2f}%")
        logger.info(f"Average Magnitude Error: {validation_results['average_magnitude_error']:.3f}")
        logger.info(f"Improvement over Random: {validation_results['benchmark_comparison']['improvement_over_random']:.1%}")
        
        logger.info("\n📊 Performance by Confidence Level:")
        for level, stats in validation_results['confidence_accuracy'].items():
            logger.info(f"  {level.title()} confidence: {stats['accuracy']:.1%} ({stats['count']} predictions)")
        
        # Show actual vs predicted comparison
        logger.info("\n🔍 Detailed Comparison:")
        logger.info("=" * 60)
        
        for i, (pred, actual) in enumerate(zip(predictions, actual_outcomes)):
            correct = "✅" if pred['direction'] == actual['actual_direction'] else "❌"
            logger.info(f"Scenario {i+1}: {correct}")
            logger.info(f"  Predicted: {pred['direction']} ({pred['confidence']:.3f})")
            logger.info(f"  Actual: {actual['actual_direction']} ({actual['price_change_pct']:.2f}%)")
            logger.info(f"  Price: {pred['predicted_price']:.2f} vs {actual['closing_price']:.2f}")
        
        logger.info("\n🎉 Prediction Demo Completed Successfully!")
        logger.info("\nKey Capabilities Demonstrated:")
        logger.info("✅ Feature extraction from tick data")
        logger.info("✅ Direction prediction with confidence scores")
        logger.info("✅ Price magnitude estimation")
        logger.info("✅ Support/resistance level calculation")
        logger.info("✅ Prediction validation and accuracy metrics")
        logger.info("✅ Performance benchmarking")
        
        return True
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_prediction_demo()
    sys.exit(0 if success else 1)
