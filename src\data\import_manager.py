#!/usr/bin/env python3
"""
Data Import Management System
Handles daily imports, bulk imports, file monitoring, and automated processing workflows
"""
import os
import json
import time
import shutil
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import pandas as pd

from .ingestion import TickDataIngestion
from .storage import TickDataStorage
from ..utils.logging import get_logger

logger = get_logger(__name__)

@dataclass
class ImportJob:
    """Represents a data import job"""
    job_id: str
    job_type: str  # 'daily', 'bulk', 'single'
    status: str    # 'pending', 'running', 'completed', 'failed'
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    files_total: int = 0
    files_processed: int = 0
    files_successful: int = 0
    files_failed: int = 0
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None

@dataclass
class FileInfo:
    """Information about a data file"""
    path: Path
    index_name: str
    date: str
    size_bytes: int
    modified_time: datetime
    processed: bool = False
    error: Optional[str] = None

class DataImportManager:
    """Manages all data import operations"""
    
    def __init__(self, storage: TickDataStorage, config=None):
        """
        Initialize the import manager
        
        Args:
            storage: TickDataStorage instance
            config: Configuration object
        """
        self.storage = storage
        self.config = config
        self.ingestion = TickDataIngestion(storage, config)
        
        # Job tracking
        self.jobs: Dict[str, ImportJob] = {}
        self.job_history_file = Path('import_jobs.json')
        
        # File patterns for different indices
        self.index_patterns = {
            'nifty': r'Nifty Ticklist (\d{8})\.csv',
            'bank_nifty': r'Bank Nifty Ticklist (\d{8})\.csv',
            'fin_nifty': r'Fin Nifty Ticklist (\d{8})\.csv',
            'midcap_nifty': r'Midcap Nifty Ticklist (\d{8})\.csv',
            'nifty_next': r'Nifty Nxt Ticklist (\d{8})\.csv'
        }
        
        # Load job history
        self._load_job_history()
    
    def _load_job_history(self):
        """Load job history from file"""
        if self.job_history_file.exists():
            try:
                with open(self.job_history_file, 'r') as f:
                    data = json.load(f)
                    for job_data in data:
                        job = ImportJob(**job_data)
                        # Convert string dates back to datetime
                        job.created_at = datetime.fromisoformat(job_data['created_at'])
                        if job_data.get('started_at'):
                            job.started_at = datetime.fromisoformat(job_data['started_at'])
                        if job_data.get('completed_at'):
                            job.completed_at = datetime.fromisoformat(job_data['completed_at'])
                        self.jobs[job.job_id] = job
            except Exception as e:
                logger.warning(f"Failed to load job history: {e}")
    
    def _save_job_history(self):
        """Save job history to file"""
        try:
            job_data = []
            for job in self.jobs.values():
                data = {
                    'job_id': job.job_id,
                    'job_type': job.job_type,
                    'status': job.status,
                    'created_at': job.created_at.isoformat(),
                    'started_at': job.started_at.isoformat() if job.started_at else None,
                    'completed_at': job.completed_at.isoformat() if job.completed_at else None,
                    'files_total': job.files_total,
                    'files_processed': job.files_processed,
                    'files_successful': job.files_successful,
                    'files_failed': job.files_failed,
                    'error_message': job.error_message,
                    'metadata': job.metadata or {}
                }
                job_data.append(data)
            
            with open(self.job_history_file, 'w') as f:
                json.dump(job_data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save job history: {e}")
    
    def scan_directory(self, directory: Path) -> List[FileInfo]:
        """
        Scan directory for data files and extract metadata
        
        Args:
            directory: Directory to scan
            
        Returns:
            List of FileInfo objects
        """
        files = []
        
        if not directory.exists():
            logger.error(f"Directory not found: {directory}")
            return files
        
        import re
        
        for file_path in directory.glob('*.csv'):
            # Skip hidden files and non-data files
            if file_path.name.startswith('.') or file_path.name == 'desktop.ini':
                continue
            
            # Try to match against known patterns
            index_name = None
            date_str = None
            
            for idx, pattern in self.index_patterns.items():
                match = re.match(pattern, file_path.name)
                if match:
                    index_name = idx
                    date_str = match.group(1)
                    break
            
            if not index_name:
                logger.warning(f"Unknown file pattern: {file_path.name}")
                continue
            
            # Get file metadata
            stat = file_path.stat()
            
            file_info = FileInfo(
                path=file_path,
                index_name=index_name,
                date=date_str,
                size_bytes=stat.st_size,
                modified_time=datetime.fromtimestamp(stat.st_mtime)
            )
            
            files.append(file_info)
        
        # Sort by date and index
        files.sort(key=lambda f: (f.date, f.index_name))
        
        logger.info(f"Scanned {directory}: found {len(files)} data files")
        return files
    
    def create_daily_import_job(self, date: str, source_dir: Path, 
                              indices: Optional[List[str]] = None) -> str:
        """
        Create a daily import job
        
        Args:
            date: Date in YYYY-MM-DD format
            source_dir: Source directory containing data files
            indices: List of indices to import (None for all)
            
        Returns:
            Job ID
        """
        job_id = f"daily_{date}_{datetime.now().strftime('%H%M%S')}"
        
        # Convert date format for file matching
        date_obj = datetime.strptime(date, '%Y-%m-%d')
        file_date = date_obj.strftime('%d%m%Y')
        
        # Find files for the specific date
        all_files = self.scan_directory(source_dir)
        target_files = [f for f in all_files if f.date == file_date]
        
        # Filter by indices if specified
        if indices:
            target_files = [f for f in target_files if f.index_name in indices]
        
        job = ImportJob(
            job_id=job_id,
            job_type='daily',
            status='pending',
            created_at=datetime.now(),
            files_total=len(target_files),
            metadata={
                'date': date,
                'source_dir': str(source_dir),
                'indices': indices,
                'files': [{'path': str(f.path), 'index': f.index_name} for f in target_files]
            }
        )
        
        self.jobs[job_id] = job
        self._save_job_history()
        
        logger.info(f"Created daily import job {job_id} for {date} with {len(target_files)} files")
        return job_id
    
    def create_bulk_import_job(self, source_dir: Path, 
                             date_range: Optional[Tuple[str, str]] = None,
                             indices: Optional[List[str]] = None,
                             clean_database: bool = False) -> str:
        """
        Create a bulk import job
        
        Args:
            source_dir: Source directory containing data files
            date_range: Optional tuple of (start_date, end_date) in YYYY-MM-DD format
            indices: List of indices to import (None for all)
            clean_database: Whether to clean database before import
            
        Returns:
            Job ID
        """
        job_id = f"bulk_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Find all files
        all_files = self.scan_directory(source_dir)
        target_files = all_files
        
        # Filter by date range if specified
        if date_range:
            start_date, end_date = date_range
            start_obj = datetime.strptime(start_date, '%Y-%m-%d')
            end_obj = datetime.strptime(end_date, '%Y-%m-%d')
            
            target_files = []
            for f in all_files:
                file_date_obj = datetime.strptime(f.date, '%d%m%Y')
                if start_obj <= file_date_obj <= end_obj:
                    target_files.append(f)
        
        # Filter by indices if specified
        if indices:
            target_files = [f for f in target_files if f.index_name in indices]
        
        job = ImportJob(
            job_id=job_id,
            job_type='bulk',
            status='pending',
            created_at=datetime.now(),
            files_total=len(target_files),
            metadata={
                'source_dir': str(source_dir),
                'date_range': date_range,
                'indices': indices,
                'clean_database': clean_database,
                'files': [{'path': str(f.path), 'index': f.index_name, 'date': f.date} for f in target_files]
            }
        )
        
        self.jobs[job_id] = job
        self._save_job_history()
        
        logger.info(f"Created bulk import job {job_id} with {len(target_files)} files")
        return job_id
    
    def execute_job(self, job_id: str, max_workers: int = 4) -> bool:
        """
        Execute an import job
        
        Args:
            job_id: Job ID to execute
            max_workers: Maximum number of parallel workers
            
        Returns:
            True if job completed successfully
        """
        if job_id not in self.jobs:
            logger.error(f"Job not found: {job_id}")
            return False
        
        job = self.jobs[job_id]
        
        if job.status != 'pending':
            logger.error(f"Job {job_id} is not in pending state: {job.status}")
            return False
        
        logger.info(f"Starting job {job_id} ({job.job_type})")
        
        job.status = 'running'
        job.started_at = datetime.now()
        self._save_job_history()
        
        try:
            # Clean database if requested
            if job.metadata and job.metadata.get('clean_database'):
                logger.info("Cleaning database before import...")
                self.storage.clear_all_data()
            
            # Get files to process
            files_to_process = []
            for file_info in job.metadata.get('files', []):
                file_path = Path(file_info['path'])
                if file_path.exists():
                    files_to_process.append(file_path)
                else:
                    logger.warning(f"File not found: {file_path}")
            
            # Process files
            if max_workers == 1:
                # Sequential processing
                for file_path in files_to_process:
                    self._process_single_file(job, file_path)
            else:
                # Parallel processing
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    futures = {executor.submit(self._process_single_file, job, file_path): file_path 
                             for file_path in files_to_process}
                    
                    for future in as_completed(futures):
                        file_path = futures[future]
                        try:
                            future.result()
                        except Exception as e:
                            logger.error(f"Error processing {file_path}: {e}")
                            job.files_failed += 1
            
            # Update job status
            job.status = 'completed' if job.files_failed == 0 else 'completed_with_errors'
            job.completed_at = datetime.now()
            
            logger.info(f"Job {job_id} completed: {job.files_successful}/{job.files_total} successful")
            
        except Exception as e:
            job.status = 'failed'
            job.error_message = str(e)
            job.completed_at = datetime.now()
            logger.error(f"Job {job_id} failed: {e}")
        
        finally:
            self._save_job_history()
        
        return job.status in ['completed', 'completed_with_errors']
    
    def _process_single_file(self, job: ImportJob, file_path: Path):
        """Process a single file within a job"""
        try:
            logger.info(f"Processing: {file_path.name}")
            
            if self.ingestion.process_file(str(file_path)):
                job.files_successful += 1
                logger.info(f"✅ {file_path.name} imported successfully")
            else:
                job.files_failed += 1
                logger.error(f"❌ {file_path.name} import failed")
            
            job.files_processed += 1
            
        except Exception as e:
            job.files_failed += 1
            job.files_processed += 1
            logger.error(f"Error processing {file_path.name}: {e}")
    
    def get_job_status(self, job_id: str) -> Optional[ImportJob]:
        """Get status of a specific job"""
        return self.jobs.get(job_id)
    
    def list_jobs(self, status_filter: Optional[str] = None, 
                  limit: int = 50) -> List[ImportJob]:
        """
        List import jobs
        
        Args:
            status_filter: Filter by status (pending, running, completed, failed)
            limit: Maximum number of jobs to return
            
        Returns:
            List of ImportJob objects
        """
        jobs = list(self.jobs.values())
        
        if status_filter:
            jobs = [job for job in jobs if job.status == status_filter]
        
        # Sort by creation time (newest first)
        jobs.sort(key=lambda j: j.created_at, reverse=True)
        
        return jobs[:limit]
    
    def cancel_job(self, job_id: str) -> bool:
        """Cancel a pending job"""
        if job_id not in self.jobs:
            return False
        
        job = self.jobs[job_id]
        if job.status == 'pending':
            job.status = 'cancelled'
            job.completed_at = datetime.now()
            self._save_job_history()
            logger.info(f"Job {job_id} cancelled")
            return True
        
        return False
    
    def cleanup_old_jobs(self, days: int = 30):
        """Remove job history older than specified days"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        jobs_to_remove = []
        for job_id, job in self.jobs.items():
            if job.created_at < cutoff_date and job.status in ['completed', 'failed', 'cancelled']:
                jobs_to_remove.append(job_id)
        
        for job_id in jobs_to_remove:
            del self.jobs[job_id]
        
        if jobs_to_remove:
            self._save_job_history()
            logger.info(f"Cleaned up {len(jobs_to_remove)} old jobs")
