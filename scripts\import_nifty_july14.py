#!/usr/bin/env python3
"""
Import the specific Nifty July 14 file that's missing
"""
import sys
from pathlib import Path
import pandas as pd

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.data.storage import TickDataStorage
from src.data.ingestion import TickDataIngestion
from src.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

def main():
    """Import the missing Nifty July 14 file"""
    logger.info("🎯 Importing Missing Nifty July 14 Data")
    
    # Use the existing database
    db_path = "clean_tickdata.db"
    connection_string = f"sqlite:///{db_path}"
    
    if not Path(db_path).exists():
        logger.error(f"Database {db_path} not found")
        return False
    
    try:
        # Initialize components
        storage = TickDataStorage(connection_string)
        ingestion = TickDataIngestion(storage)
        
        # Target file
        nifty_file = Path("sampledata/Nifty Ticklist 14072025.csv")
        
        if not nifty_file.exists():
            logger.error(f"File not found: {nifty_file}")
            return False
        
        logger.info(f"📁 Processing: {nifty_file.name}")
        
        # Check file content first
        df = pd.read_csv(nifty_file)
        logger.info(f"📊 File contains {len(df)} records")
        logger.info(f"   Columns: {list(df.columns)}")
        logger.info(f"   First record: {df.iloc[0]['Time']} - ₹{df.iloc[0]['Last Rate']}")
        logger.info(f"   Last record: {df.iloc[-1]['Time']} - ₹{df.iloc[-1]['Last Rate']}")
        
        # Process the file
        result = ingestion.process_file(str(nifty_file))
        
        if result:
            logger.info("✅ Successfully imported Nifty July 14 data")
            
            # Verify the import
            from sqlalchemy import text
            with storage.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count, MIN(timestamp) as first_time, MAX(timestamp) as last_time,
                           MIN(price) as min_price, MAX(price) as max_price
                    FROM tick_data 
                    WHERE index_name = 'nifty' AND DATE(timestamp) = '2025-07-14'
                """))
                
                row = result.fetchone()
                if row and row[0] > 0:
                    logger.info(f"✅ Verification successful:")
                    logger.info(f"   Records imported: {row[0]:,}")
                    logger.info(f"   Time range: {row[1]} to {row[2]}")
                    logger.info(f"   Price range: ₹{row[3]:,.2f} to ₹{row[4]:,.2f}")
                    
                    # Get the last price specifically
                    result2 = conn.execute(text("""
                        SELECT timestamp, price, volume
                        FROM tick_data 
                        WHERE index_name = 'nifty' AND DATE(timestamp) = '2025-07-14'
                        ORDER BY timestamp DESC
                        LIMIT 1
                    """))
                    
                    last_row = result2.fetchone()
                    if last_row:
                        logger.info(f"   Last price: ₹{last_row[1]:,.2f} at {last_row[0]} (Volume: {last_row[2]:,})")
                else:
                    logger.error("❌ Verification failed - no data found after import")
                    return False
            
            return True
        else:
            logger.error("❌ Import failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Import failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
