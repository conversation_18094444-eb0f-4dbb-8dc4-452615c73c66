#!/usr/bin/env python3
"""
REST API Endpoints for the Tick Data Analysis Platform
External integration and programmatic access to all platform features
"""
from flask import Flask, request, jsonify, Blueprint
from flask_restx import Api, Resource, fields, Namespace
from flask_cors import CORS
import json
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Dict, List, Any, Optional
import pandas as pd

# Add project root to path
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.data.storage import TickDataStorage
from src.data.import_manager import DataImportManager
from src.strategy.professional_trader import ProfessionalTradingStrategy
from src.ml.features import FeatureEngineering
from src.utils.logging import get_logger
from config import get_config

logger = get_logger(__name__)

def create_api_app(config=None):
    """Create Flask app with API endpoints"""
    app = Flask(__name__)
    CORS(app)  # Enable CORS for all routes
    
    # Configuration
    config = config or get_config()
    app.config['SECRET_KEY'] = 'api-secret-key'
    
    # Initialize components
    storage = TickDataStorage(config.database.connection_string)
    import_manager = DataImportManager(storage, config)
    strategy = ProfessionalTradingStrategy(storage)
    feature_engine = FeatureEngineering()
    
    # Create API with Swagger documentation
    api = Api(app, 
              title='Indian Market Tick Data Analysis API',
              version='1.0',
              description='Professional trading platform API for tick data analysis',
              doc='/docs/')
    
    # Define API models for documentation
    data_import_model = api.model('DataImport', {
        'source_dir': fields.String(required=True, description='Source directory path'),
        'date': fields.String(description='Date in YYYY-MM-DD format'),
        'indices': fields.List(fields.String, description='List of indices to import'),
        'clean_database': fields.Boolean(description='Clean database before import')
    })
    
    analysis_model = api.model('Analysis', {
        'index_name': fields.String(required=True, description='Index name'),
        'date': fields.String(description='Analysis date in YYYY-MM-DD format'),
        'analysis_type': fields.String(description='Type of analysis to perform')
    })
    
    signal_model = api.model('TradingSignal', {
        'direction': fields.String(description='Signal direction (long/short)'),
        'entry_price': fields.Float(description='Entry price level'),
        'target_price': fields.Float(description='Target price level'),
        'stop_loss': fields.Float(description='Stop loss level'),
        'confidence': fields.Float(description='Signal confidence (0-1)'),
        'volume_requirement': fields.Integer(description='Required volume for confirmation')
    })
    
    # Create namespaces
    data_ns = Namespace('data', description='Data management operations')
    analysis_ns = Namespace('analysis', description='Market analysis operations')
    signals_ns = Namespace('signals', description='Trading signal operations')
    system_ns = Namespace('system', description='System monitoring operations')
    
    api.add_namespace(data_ns, path='/api/data')
    api.add_namespace(analysis_ns, path='/api/analysis')
    api.add_namespace(signals_ns, path='/api/signals')
    api.add_namespace(system_ns, path='/api/system')
    
    # Data Management Endpoints
    @data_ns.route('/import/daily')
    class DailyImport(Resource):
        @data_ns.expect(data_import_model)
        @data_ns.doc('daily_import')
        def post(self):
            """Create a daily data import job"""
            try:
                data = request.get_json()
                date = data.get('date', datetime.now().strftime('%Y-%m-%d'))
                source_dir = Path(data.get('source_dir', 'sampledata'))
                indices = data.get('indices')
                
                job_id = import_manager.create_daily_import_job(
                    date=date,
                    source_dir=source_dir,
                    indices=indices
                )
                
                # Execute job
                success = import_manager.execute_job(job_id)
                job = import_manager.get_job_status(job_id)
                
                return {
                    'job_id': job_id,
                    'status': job.status,
                    'success': success,
                    'files_processed': job.files_processed,
                    'files_successful': job.files_successful,
                    'files_failed': job.files_failed,
                    'message': f'Daily import for {date} completed'
                }
            except Exception as e:
                logger.error(f"Daily import error: {e}")
                return {'error': str(e)}, 500
    
    @data_ns.route('/import/bulk')
    class BulkImport(Resource):
        @data_ns.expect(data_import_model)
        @data_ns.doc('bulk_import')
        def post(self):
            """Create a bulk data import job"""
            try:
                data = request.get_json()
                source_dir = Path(data.get('source_dir', 'sampledata'))
                date_range = data.get('date_range')
                indices = data.get('indices')
                clean_database = data.get('clean_database', False)
                
                job_id = import_manager.create_bulk_import_job(
                    source_dir=source_dir,
                    date_range=date_range,
                    indices=indices,
                    clean_database=clean_database
                )
                
                # Execute job
                success = import_manager.execute_job(job_id)
                job = import_manager.get_job_status(job_id)
                
                return {
                    'job_id': job_id,
                    'status': job.status,
                    'success': success,
                    'files_processed': job.files_processed,
                    'files_successful': job.files_successful,
                    'files_failed': job.files_failed,
                    'message': 'Bulk import completed'
                }
            except Exception as e:
                logger.error(f"Bulk import error: {e}")
                return {'error': str(e)}, 500
    
    @data_ns.route('/jobs')
    class ImportJobs(Resource):
        @data_ns.doc('list_jobs')
        def get(self):
            """List import jobs"""
            try:
                status_filter = request.args.get('status')
                limit = int(request.args.get('limit', 50))
                
                jobs = import_manager.list_jobs(status_filter, limit)
                
                job_data = []
                for job in jobs:
                    job_data.append({
                        'job_id': job.job_id,
                        'job_type': job.job_type,
                        'status': job.status,
                        'created_at': job.created_at.isoformat(),
                        'files_total': job.files_total,
                        'files_processed': job.files_processed,
                        'files_successful': job.files_successful,
                        'files_failed': job.files_failed,
                        'progress': (job.files_processed / job.files_total * 100) if job.files_total > 0 else 0
                    })
                
                return {'jobs': job_data}
            except Exception as e:
                logger.error(f"List jobs error: {e}")
                return {'error': str(e)}, 500
    
    @data_ns.route('/jobs/<string:job_id>')
    class ImportJob(Resource):
        @data_ns.doc('get_job')
        def get(self, job_id):
            """Get specific job status"""
            try:
                job = import_manager.get_job_status(job_id)
                if not job:
                    return {'error': 'Job not found'}, 404
                
                return {
                    'job_id': job.job_id,
                    'job_type': job.job_type,
                    'status': job.status,
                    'created_at': job.created_at.isoformat(),
                    'started_at': job.started_at.isoformat() if job.started_at else None,
                    'completed_at': job.completed_at.isoformat() if job.completed_at else None,
                    'files_total': job.files_total,
                    'files_processed': job.files_processed,
                    'files_successful': job.files_successful,
                    'files_failed': job.files_failed,
                    'error_message': job.error_message,
                    'metadata': job.metadata
                }
            except Exception as e:
                logger.error(f"Get job error: {e}")
                return {'error': str(e)}, 500
    
    @data_ns.route('/export')
    class DataExport(Resource):
        @data_ns.doc('export_data')
        def get(self):
            """Export tick data"""
            try:
                index_name = request.args.get('index', required=True)
                start_date = request.args.get('start_date', required=True)
                end_date = request.args.get('end_date', required=True)
                format_type = request.args.get('format', 'json')
                
                data = storage.get_tick_data(index_name, start_date, end_date)
                
                if data.empty:
                    return {'error': 'No data found for the specified criteria'}, 404
                
                if format_type.lower() == 'csv':
                    # Return CSV data
                    csv_data = data.to_csv(index=False)
                    return csv_data, 200, {'Content-Type': 'text/csv'}
                else:
                    # Return JSON data
                    return {
                        'index_name': index_name,
                        'start_date': start_date,
                        'end_date': end_date,
                        'record_count': len(data),
                        'data': data.to_dict('records')
                    }
            except Exception as e:
                logger.error(f"Export data error: {e}")
                return {'error': str(e)}, 500
    
    # Analysis Endpoints
    @analysis_ns.route('/<string:index_name>')
    class MarketAnalysis(Resource):
        @analysis_ns.doc('market_analysis')
        def get(self, index_name):
            """Get market analysis for an index"""
            try:
                date = request.args.get('date')
                if not date:
                    # Get latest date
                    latest_data = storage.get_latest_data()
                    if latest_data.empty:
                        return {'error': 'No data available'}, 404
                    date = latest_data['timestamp'].dt.date.iloc[0].strftime('%Y-%m-%d')
                
                analysis = strategy.analyze_market_structure(index_name, date)
                
                if not analysis:
                    return {'error': f'No analysis data for {index_name} on {date}'}, 404
                
                return {
                    'index_name': index_name,
                    'date': date,
                    'analysis': analysis,
                    'timestamp': datetime.now().isoformat()
                }
            except Exception as e:
                logger.error(f"Market analysis error: {e}")
                return {'error': str(e)}, 500
    
    @analysis_ns.route('/features/<string:index_name>')
    class FeatureExtraction(Resource):
        @analysis_ns.doc('feature_extraction')
        def get(self, index_name):
            """Extract features for an index"""
            try:
                start_date = request.args.get('start_date', required=True)
                end_date = request.args.get('end_date', required=True)
                
                # Get tick data
                tick_data = storage.get_tick_data(index_name, start_date, end_date)
                
                if tick_data.empty:
                    return {'error': 'No tick data found'}, 404
                
                # Extract features
                features = feature_engine.extract_all_features(tick_data)
                
                return {
                    'index_name': index_name,
                    'start_date': start_date,
                    'end_date': end_date,
                    'feature_count': len(features.columns),
                    'record_count': len(features),
                    'features': features.to_dict('records')
                }
            except Exception as e:
                logger.error(f"Feature extraction error: {e}")
                return {'error': str(e)}, 500
    
    # Trading Signals Endpoints
    @signals_ns.route('/')
    class TradingSignals(Resource):
        @signals_ns.doc('trading_signals')
        def get(self):
            """Get trading signals for all indices"""
            try:
                date = request.args.get('date')
                indices = request.args.get('indices', '').split(',') if request.args.get('indices') else None
                confidence = float(request.args.get('confidence', 0.7))
                
                if not date:
                    # Get latest date
                    latest_data = storage.get_latest_data()
                    if latest_data.empty:
                        return {'error': 'No data available'}, 404
                    date = latest_data['timestamp'].dt.date.iloc[0].strftime('%Y-%m-%d')
                
                if not indices:
                    indices = ['nifty', 'bank_nifty', 'fin_nifty', 'midcap_nifty', 'nifty_next']
                
                all_signals = {}
                
                for index_name in indices:
                    try:
                        analysis = strategy.analyze_market_structure(index_name, date)
                        if analysis:
                            signals = strategy.generate_trading_signals(
                                analysis, confidence_threshold=confidence
                            )
                            if signals:
                                all_signals[index_name] = signals
                    except Exception as e:
                        logger.error(f"Error generating signals for {index_name}: {e}")
                
                return {
                    'date': date,
                    'confidence_threshold': confidence,
                    'signal_count': sum(len(signals) for signals in all_signals.values()),
                    'signals': all_signals,
                    'timestamp': datetime.now().isoformat()
                }
            except Exception as e:
                logger.error(f"Trading signals error: {e}")
                return {'error': str(e)}, 500
    
    @signals_ns.route('/<string:index_name>')
    class IndexSignals(Resource):
        @signals_ns.doc('index_signals')
        def get(self, index_name):
            """Get trading signals for a specific index"""
            try:
                date = request.args.get('date')
                confidence = float(request.args.get('confidence', 0.7))
                
                if not date:
                    # Get latest date
                    latest_data = storage.get_latest_data()
                    if latest_data.empty:
                        return {'error': 'No data available'}, 404
                    date = latest_data['timestamp'].dt.date.iloc[0].strftime('%Y-%m-%d')
                
                analysis = strategy.analyze_market_structure(index_name, date)
                if not analysis:
                    return {'error': f'No analysis data for {index_name} on {date}'}, 404
                
                signals = strategy.generate_trading_signals(
                    analysis, confidence_threshold=confidence
                )
                
                return {
                    'index_name': index_name,
                    'date': date,
                    'confidence_threshold': confidence,
                    'signal_count': len(signals) if signals else 0,
                    'signals': signals or [],
                    'timestamp': datetime.now().isoformat()
                }
            except Exception as e:
                logger.error(f"Index signals error: {e}")
                return {'error': str(e)}, 500
    
    # System Monitoring Endpoints
    @system_ns.route('/status')
    class SystemStatus(Resource):
        @system_ns.doc('system_status')
        def get(self):
            """Get system status and statistics"""
            try:
                stats = storage.get_database_stats()
                quality_stats = storage.get_data_quality_stats()
                
                return {
                    'status': 'ok',
                    'database': {
                        'total_records': stats.get('total_records', 0),
                        'date_range': stats.get('date_range', 'N/A'),
                        'indices_count': stats.get('indices_count', 0)
                    },
                    'data_quality': {
                        'records_per_index': quality_stats.get('records_per_index', {}),
                        'latest_timestamp': quality_stats.get('latest_timestamp', 'N/A'),
                        'data_gaps': quality_stats.get('data_gaps', 0)
                    },
                    'timestamp': datetime.now().isoformat()
                }
            except Exception as e:
                logger.error(f"System status error: {e}")
                return {'error': str(e)}, 500
    
    @system_ns.route('/health')
    class HealthCheck(Resource):
        @system_ns.doc('health_check')
        def get(self):
            """Health check endpoint"""
            try:
                # Test database connection
                storage.get_database_stats()
                
                return {
                    'status': 'healthy',
                    'timestamp': datetime.now().isoformat(),
                    'version': '1.0.0'
                }
            except Exception as e:
                logger.error(f"Health check error: {e}")
                return {
                    'status': 'unhealthy',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }, 500
    
    return app

if __name__ == '__main__':
    app = create_api_app()
    app.run(host='0.0.0.0', port=8000, debug=True)
