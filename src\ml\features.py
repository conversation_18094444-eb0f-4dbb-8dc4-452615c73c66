import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import logging

logger = logging.getLogger(__name__)

class FeatureEngineering:
    """Extract features from tick data for machine learning models"""
    
    def __init__(self):
        self.feature_registry = {}
        self._register_features()
    
    def _register_features(self):
        """Register all feature extraction functions"""
        self.feature_registry = {
            # Volume-based features
            'volume_delta': self.calculate_volume_delta,
            'relative_volume': self.calculate_relative_volume,
            'volume_imbalance': self.calculate_volume_imbalance,
            'volume_acceleration': self.calculate_volume_acceleration,
            
            # Price-based features
            'price_momentum': self.calculate_price_momentum,
            'price_reversal': self.detect_price_reversal,
            'price_volatility': self.calculate_price_volatility,
            'tick_direction': self.calculate_tick_direction,
            
            # Time-based features
            'time_of_day': self.extract_time_of_day,
            'time_to_expiry': self.calculate_time_to_expiry,
            
            # Advanced features
            'tick_pattern': self.extract_tick_pattern,
            'support_resistance': self.identify_support_resistance
        }
    
    def extract_all_features(self, tick_data: pd.DataFrame) -> pd.DataFrame:
        """
        Extract all registered features from tick data
        
        Args:
            tick_data: DataFrame containing tick data
            
        Returns:
            DataFrame with extracted features
        """
        features = pd.DataFrame(index=tick_data.index)
        
        for feature_name, feature_func in self.feature_registry.items():
            try:
                feature_values = feature_func(tick_data)
                
                if isinstance(feature_values, pd.Series):
                    features[feature_name] = feature_values
                elif isinstance(feature_values, pd.DataFrame):
                    # For multi-column features, prefix with feature name
                    for col in feature_values.columns:
                        features[f"{feature_name}_{col}"] = feature_values[col]
                else:
                    logger.warning(f"Feature {feature_name} returned unexpected type")
            
            except Exception as e:
                logger.error(f"Error extracting feature {feature_name}: {str(e)}")
        
        return features
    
    def calculate_volume_delta(self, tick_data: pd.DataFrame, window: int = 10) -> pd.Series:
        """
        Calculate the change in volume over a window of ticks
        
        Args:
            tick_data: DataFrame containing tick data
            window: Number of ticks to consider
            
        Returns:
            Series with volume delta values
        """
        return tick_data['volume'].diff(window)
    
    def calculate_relative_volume(self, tick_data: pd.DataFrame, window: int = 100) -> pd.Series:
        """
        Calculate volume relative to moving average
        
        Args:
            tick_data: DataFrame containing tick data
            window: Window size for moving average
            
        Returns:
            Series with relative volume values
        """
        volume_ma = tick_data['volume'].rolling(window=window).mean()
        return tick_data['volume'] / volume_ma
    
    def calculate_volume_imbalance(self, tick_data: pd.DataFrame, window: int = 20) -> pd.Series:
        """
        Calculate imbalance between up-tick and down-tick volume
        
        Args:
            tick_data: DataFrame containing tick data
            window: Window size for calculation
            
        Returns:
            Series with volume imbalance values
        """
        # Create price change series
        price_change = tick_data['price'].diff()
        
        # Separate volume for up and down ticks
        up_volume = tick_data['volume'].where(price_change > 0, 0)
        down_volume = tick_data['volume'].where(price_change < 0, 0)
        
        # Calculate rolling sums
        up_volume_sum = up_volume.rolling(window=window).sum()
        down_volume_sum = down_volume.rolling(window=window).sum()
        
        # Calculate imbalance ratio
        return (up_volume_sum - down_volume_sum) / (up_volume_sum + down_volume_sum)
    
    def calculate_volume_acceleration(self, tick_data: pd.DataFrame, window: int = 50) -> pd.Series:
        """
        Calculate the acceleration of volume
        
        Args:
            tick_data: DataFrame containing tick data
            window: Window size for calculation
            
        Returns:
            Series with volume acceleration values
        """
        volume_ma = tick_data['volume'].rolling(window=window).mean()
        volume_ma_change = volume_ma.diff()
        return volume_ma_change.diff()
    
    def calculate_price_momentum(self, tick_data: pd.DataFrame, window: int = 30) -> pd.Series:
        """
        Calculate price momentum
        
        Args:
            tick_data: DataFrame containing tick data
            window: Window size for calculation
            
        Returns:
            Series with price momentum values
        """
        return tick_data['price'].diff(window)
    
    def detect_price_reversal(self, tick_data: pd.DataFrame, window: int = 10) -> pd.Series:
        """
        Detect potential price reversals
        
        Args:
            tick_data: DataFrame containing tick data
            window: Window size for calculation
            
        Returns:
            Series with reversal signals (1 for up reversal, -1 for down reversal, 0 otherwise)
        """
        # Calculate short and long moving averages
        short_ma = tick_data['price'].rolling(window=window).mean()
        long_ma = tick_data['price'].rolling(window=window*3).mean()
        
        # Calculate moving average crossovers
        prev_diff = short_ma.shift(1) - long_ma.shift(1)
        curr_diff = short_ma - long_ma
        
        # Identify crossovers
        up_reversal = (prev_diff < 0) & (curr_diff > 0)
        down_reversal = (prev_diff > 0) & (curr_diff < 0)
        
        # Create reversal signal
        reversal = pd.Series(0, index=tick_data.index)
        reversal[up_reversal] = 1
        reversal[down_reversal] = -1
        
        return reversal
    
    def calculate_price_volatility(self, tick_data: pd.DataFrame, window: int = 50) -> pd.Series:
        """
        Calculate price volatility
        
        Args:
            tick_data: DataFrame containing tick data
            window: Window size for calculation
            
        Returns:
            Series with price volatility values
        """
        return tick_data['price'].rolling(window=window).std()
    
    def calculate_tick_direction(self, tick_data: pd.DataFrame, window: int = 20) -> pd.Series:
        """
        Calculate the dominant direction of ticks
        
        Args:
            tick_data: DataFrame containing tick data
            window: Window size for calculation
            
        Returns:
            Series with tick direction values (-1 to 1)
        """
        price_change = tick_data['price'].diff()
        up_ticks = (price_change > 0).astype(int)
        down_ticks = (price_change < 0).astype(int)
        
        up_sum = up_ticks.rolling(window=window).sum()
        down_sum = down_ticks.rolling(window=window).sum()
        
        return (up_sum - down_sum) / window
    
    def extract_time_of_day(self, tick_data: pd.DataFrame) -> pd.DataFrame:
        """
        Extract time of day features
        
        Args:
            tick_data: DataFrame containing tick data
            
        Returns:
            DataFrame with time of day features
        """
        # Extract hour and minute
        hour = tick_data['timestamp'].dt.hour
        minute = tick_data['timestamp'].dt.minute
        
        # Convert to seconds from market open (9:15 AM)
        seconds_from_open = (hour - 9) * 3600 + (minute - 15) * 60
        
        # Normalize to 0-1 range (market hours: 9:15 AM to 3:30 PM)
        market_duration_seconds = 6 * 3600 + 15 * 60
        normalized_time = seconds_from_open / market_duration_seconds
        
        # Create time segments for different market phases
        opening_phase = (normalized_time <= 0.1).astype(int)
        lunch_phase = ((normalized_time > 0.4) & (normalized_time <= 0.6)).astype(int)
        closing_phase = (normalized_time > 0.9).astype(int)
        
        return pd.DataFrame({
            'normalized_time': normalized_time,
            'opening_phase': opening_phase,
            'lunch_phase': lunch_phase,
            'closing_phase': closing_phase
        })
    
    def calculate_time_to_expiry(self, tick_data: pd.DataFrame) -> pd.Series:
        """
        Calculate time to expiry for derivatives
        
        Args:
            tick_data: DataFrame containing tick data
            
        Returns:
            Series with time to expiry values
        """
        # Extract date from timestamp
        date = tick_data['timestamp'].dt.date
        
        # Determine expiry date (last Thursday of month for monthly, Thursday for weekly)
        # This is a simplified version - would need to be customized for actual expiry calendar
        
        # For demonstration, we'll just use days to end of month as a proxy
        days_in_month = tick_data['timestamp'].dt.daysinmonth
        day_of_month = tick_data['timestamp'].dt.day
        days_to_month_end = days_in_month - day_of_month
        
        # Normalize to 0-1 range (0 = expiry day, 1 = furthest from expiry)
        normalized_time_to_expiry = days_to_month_end / 30
        
        return normalized_time_to_expiry
    
    def extract_tick_pattern(self, tick_data: pd.DataFrame, window: int = 5) -> pd.DataFrame:
        """
        Extract patterns in tick data
        
        Args:
            tick_data: DataFrame containing tick data
            window: Pattern window size
            
        Returns:
            DataFrame with pattern features
        """
        # Calculate consecutive up/down ticks
        price_change = tick_data['price'].diff()
        
        # Count consecutive moves in same direction
        direction = np.sign(price_change)
        direction_change = direction.diff().fillna(0) != 0
        
        # Initialize counter
        consecutive_count = pd.Series(0, index=tick_data.index)
        current_count = 0
        
        # Calculate consecutive counts
        for i, change in enumerate(direction_change):
            if change:
                current_count = 1
            else:
                current_count += 1
            consecutive_count.iloc[i] = current_count
        
        # Calculate pattern features
        consecutive_up = consecutive_count.where(direction > 0, 0)
        consecutive_down = consecutive_count.where(direction < 0, 0)
        
        # Identify potential exhaustion patterns
        up_exhaustion = (consecutive_up >= window) & (price_change < 0)
        down_exhaustion = (consecutive_down >= window) & (price_change > 0)
        
        return pd.DataFrame({
            'consecutive_up': consecutive_up,
            'consecutive_down': consecutive_down,
            'up_exhaustion': up_exhaustion.astype(int),
            'down_exhaustion': down_exhaustion.astype(int)
        })
    
    def identify_support_resistance(self, tick_data: pd.DataFrame, window: int = 100) -> pd.DataFrame:
        """
        Identify potential support and resistance levels
        
        Args:
            tick_data: DataFrame containing tick data
            window: Window size for calculation
            
        Returns:
            DataFrame with support/resistance features
        """
        # Calculate rolling high and low
        rolling_high = tick_data['price'].rolling(window=window).max()
        rolling_low = tick_data['price'].rolling(window=window).min()
        
        # Calculate distance to support/resistance
        distance_to_resistance = (rolling_high - tick_data['price']) / tick_data['price']
        distance_to_support = (tick_data['price'] - rolling_low) / tick_data['price']
        
        # Identify tests of support/resistance
        near_resistance = distance_to_resistance < 0.001
        near_support = distance_to_support < 0.001
        
        # Identify breaks of support/resistance
        resistance_break = tick_data['price'] > rolling_high.shift(1)
        support_break = tick_data['price'] < rolling_low.shift(1)
        
        return pd.DataFrame({
            'distance_to_resistance': distance_to_resistance,
            'distance_to_support': distance_to_support,
            'near_resistance': near_resistance.astype(int),
            'near_support': near_support.astype(int),
            'resistance_break': resistance_break.astype(int),
            'support_break': support_break.astype(int)
        })