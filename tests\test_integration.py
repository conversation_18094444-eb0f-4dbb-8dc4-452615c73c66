#!/usr/bin/env python3
"""
Integration Test Suite
End-to-end testing of complete workflows and system integration
"""
import pytest
import tempfile
import shutil
import json
import subprocess
import sys
import time
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from unittest.mock import patch

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.data.storage import TickDataStorage
from src.data.import_manager import DataImportManager
from src.strategy.professional_trader import ProfessionalTradingStrategy
from src.ml.features import FeatureEngineering
from config import get_config

class TestCompleteWorkflows:
    """Test complete end-to-end workflows"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for integration tests"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def test_storage(self, temp_dir):
        """Create test database storage"""
        db_path = temp_dir / "integration_test.db"
        storage = TickDataStorage(f"sqlite:///{db_path}")
        storage.init_database()
        return storage
    
    @pytest.fixture
    def sample_data_files(self, temp_dir):
        """Create comprehensive sample data files"""
        files = {}
        
        # Create realistic sample data for multiple days and indices
        dates = ['********', '********', '********']
        indices = ['Nifty', 'Bank Nifty', 'Fin Nifty', 'Midcap Nifty', 'Nifty Nxt']
        
        for date in dates:
            for index in indices:
                # Generate realistic tick data
                base_time = datetime.strptime(f"{date[:2]}-{date[2:4]}-{date[4:]}", "%d-%m-%Y")
                base_time = base_time.replace(hour=9, minute=15, second=0)
                
                # Generate 500 ticks for each file
                timestamps = [base_time + timedelta(seconds=i*2) for i in range(500)]
                
                # Different base prices for different indices
                base_prices = {
                    'Nifty': 25170.0,
                    'Bank Nifty': 57000.0,
                    'Fin Nifty': 23500.0,
                    'Midcap Nifty': 13200.0,
                    'Nifty Nxt': 45800.0
                }
                
                base_price = base_prices[index]
                prices = []
                volumes = []
                
                current_price = base_price
                for i in range(500):
                    # Add realistic price movement
                    change = np.random.normal(0, base_price * 0.0001)
                    current_price += change
                    prices.append(round(current_price, 2))
                    
                    # Generate volume with occasional spikes
                    if i % 50 == 0:  # Volume spike every 50 ticks
                        volume = np.random.randint(500, 2000)
                    else:
                        volume = np.random.randint(50, 300)
                    volumes.append(volume)
                
                # Create DataFrame
                df = pd.DataFrame({
                    'Time': [ts.strftime('%d-%m-%Y %H:%M:%S') for ts in timestamps],
                    'Last Rate': prices,
                    'Volume': volumes
                })
                
                # Save to file
                filename = f"{index} Ticklist {date}.csv"
                file_path = temp_dir / filename
                df.to_csv(file_path, index=False)
                
                files[f"{index}_{date}"] = file_path
        
        return files
    
    def test_complete_daily_workflow(self, test_storage, sample_data_files, temp_dir):
        """Test complete daily trading workflow"""
        config = get_config()
        
        # Step 1: Import daily data
        import_manager = DataImportManager(test_storage, config)
        
        job_id = import_manager.create_daily_import_job(
            date='2025-07-14',
            source_dir=temp_dir,
            indices=['nifty', 'bank_nifty']
        )
        
        success = import_manager.execute_job(job_id)
        assert success is True
        
        job = import_manager.get_job_status(job_id)
        assert job.status in ['completed', 'completed_with_errors']
        assert job.files_successful > 0
        
        # Step 2: Verify data was imported
        stats = test_storage.get_database_stats()
        assert stats['total_records'] > 0
        
        # Step 3: Run trading analysis
        strategy = ProfessionalTradingStrategy(test_storage)
        
        nifty_analysis = strategy.analyze_market_structure('nifty', '2025-07-14')
        assert nifty_analysis is not None
        assert 'volume_analysis' in nifty_analysis
        assert 'price_levels' in nifty_analysis
        
        bank_nifty_analysis = strategy.analyze_market_structure('bank_nifty', '2025-07-14')
        assert bank_nifty_analysis is not None
        
        # Step 4: Generate trading signals
        nifty_signals = strategy.generate_trading_signals(nifty_analysis, confidence_threshold=0.6)
        bank_nifty_signals = strategy.generate_trading_signals(bank_nifty_analysis, confidence_threshold=0.6)
        
        # Signals may or may not be generated depending on market conditions
        assert isinstance(nifty_signals, list)
        assert isinstance(bank_nifty_signals, list)
        
        # Step 5: Extract features for ML
        feature_engine = FeatureEngineering()
        
        nifty_data = test_storage.get_tick_data('nifty', '2025-07-14', '2025-07-14')
        if not nifty_data.empty:
            features = feature_engine.extract_all_features(nifty_data)
            assert len(features) > 0
            assert len(features.columns) > 10
    
    def test_bulk_import_and_analysis_workflow(self, test_storage, sample_data_files, temp_dir):
        """Test bulk import followed by multi-day analysis"""
        config = get_config()
        
        # Step 1: Bulk import all data
        import_manager = DataImportManager(test_storage, config)
        
        job_id = import_manager.create_bulk_import_job(
            source_dir=temp_dir,
            clean_database=True
        )
        
        success = import_manager.execute_job(job_id)
        assert success is True
        
        job = import_manager.get_job_status(job_id)
        assert job.status in ['completed', 'completed_with_errors']
        assert job.files_successful > 0
        
        # Step 2: Verify comprehensive data import
        stats = test_storage.get_database_stats()
        assert stats['total_records'] > 1000  # Should have substantial data
        assert stats['indices_count'] >= 3  # Multiple indices
        
        # Step 3: Multi-day analysis
        strategy = ProfessionalTradingStrategy(test_storage)
        
        dates = ['2025-07-13', '2025-07-14', '2025-07-15']
        analysis_results = {}
        
        for date in dates:
            analysis = strategy.analyze_market_structure('nifty', date)
            if analysis:
                analysis_results[date] = analysis
        
        # Should have analysis for at least one date
        assert len(analysis_results) > 0
        
        # Step 4: Compare analysis across dates
        if len(analysis_results) > 1:
            dates_with_data = list(analysis_results.keys())
            
            # Compare volume patterns
            vol1 = analysis_results[dates_with_data[0]]['volume_analysis']
            vol2 = analysis_results[dates_with_data[1]]['volume_analysis']
            
            # Volume thresholds should be positive
            assert vol1['percentile_75'] > 0
            assert vol2['percentile_75'] > 0
    
    def test_error_recovery_workflow(self, test_storage, temp_dir):
        """Test system behavior with errors and recovery"""
        config = get_config()
        
        # Step 1: Create some valid and some invalid files
        valid_data = pd.DataFrame({
            'Time': ['14-07-2025 09:15:00', '14-07-2025 09:15:02'],
            'Last Rate': [25170.0, 25171.5],
            'Volume': [100, 150]
        })
        
        valid_file = temp_dir / "Nifty Ticklist ********.csv"
        valid_data.to_csv(valid_file, index=False)
        
        # Create invalid file
        invalid_file = temp_dir / "Bank Nifty Ticklist ********.csv"
        with open(invalid_file, 'w') as f:
            f.write("invalid,csv,content\nwith,wrong,format\n")
        
        # Step 2: Run import job
        import_manager = DataImportManager(test_storage, config)
        
        job_id = import_manager.create_daily_import_job(
            date='2025-07-14',
            source_dir=temp_dir
        )
        
        success = import_manager.execute_job(job_id)
        
        # Job should complete but with some failures
        job = import_manager.get_job_status(job_id)
        assert job.status in ['completed', 'completed_with_errors', 'failed']
        assert job.files_processed > 0
        
        # Step 3: Verify partial success
        if job.files_successful > 0:
            # Should have some data imported
            stats = test_storage.get_database_stats()
            assert stats['total_records'] > 0
            
            # Should be able to run analysis on successfully imported data
            strategy = ProfessionalTradingStrategy(test_storage)
            analysis = strategy.analyze_market_structure('nifty', '2025-07-14')
            # Analysis may or may not succeed depending on data quality
    
    def test_performance_workflow(self, test_storage, temp_dir):
        """Test system performance with larger datasets"""
        config = get_config()
        
        # Create larger dataset (2000 ticks)
        base_time = datetime(2025, 7, 14, 9, 15, 0)
        timestamps = [base_time + timedelta(seconds=i*2) for i in range(2000)]
        
        large_data = pd.DataFrame({
            'Time': [ts.strftime('%d-%m-%Y %H:%M:%S') for ts in timestamps],
            'Last Rate': 25170 + np.cumsum(np.random.normal(0, 0.1, 2000)),
            'Volume': np.random.randint(50, 500, 2000)
        })
        
        large_file = temp_dir / "Nifty Ticklist ********.csv"
        large_data.to_csv(large_file, index=False)
        
        # Measure import performance
        import_manager = DataImportManager(test_storage, config)
        
        start_time = time.time()
        job_id = import_manager.create_daily_import_job('2025-07-14', temp_dir)
        success = import_manager.execute_job(job_id)
        import_time = time.time() - start_time
        
        assert success is True
        assert import_time < 30  # Should complete within 30 seconds
        
        # Measure analysis performance
        strategy = ProfessionalTradingStrategy(test_storage)
        
        start_time = time.time()
        analysis = strategy.analyze_market_structure('nifty', '2025-07-14')
        analysis_time = time.time() - start_time
        
        assert analysis is not None
        assert analysis_time < 10  # Should complete within 10 seconds
    
    def test_concurrent_operations(self, test_storage, sample_data_files, temp_dir):
        """Test concurrent operations on the system"""
        config = get_config()
        
        # Import some initial data
        import_manager = DataImportManager(test_storage, config)
        job_id = import_manager.create_daily_import_job('2025-07-14', temp_dir, ['nifty'])
        import_manager.execute_job(job_id)
        
        # Test concurrent analysis operations
        strategy = ProfessionalTradingStrategy(test_storage)
        
        # Multiple analysis calls (simulating concurrent users)
        results = []
        for i in range(3):
            analysis = strategy.analyze_market_structure('nifty', '2025-07-14')
            results.append(analysis)
        
        # All should succeed or fail consistently
        success_count = sum(1 for r in results if r is not None)
        assert success_count == 0 or success_count == 3  # All or none
    
    def test_data_consistency_workflow(self, test_storage, sample_data_files, temp_dir):
        """Test data consistency across operations"""
        config = get_config()
        
        # Import data
        import_manager = DataImportManager(test_storage, config)
        job_id = import_manager.create_daily_import_job('2025-07-14', temp_dir, ['nifty'])
        import_manager.execute_job(job_id)
        
        # Get initial stats
        initial_stats = test_storage.get_database_stats()
        
        # Run analysis
        strategy = ProfessionalTradingStrategy(test_storage)
        analysis = strategy.analyze_market_structure('nifty', '2025-07-14')
        
        # Get stats after analysis
        post_analysis_stats = test_storage.get_database_stats()
        
        # Data should remain consistent
        assert initial_stats['total_records'] == post_analysis_stats['total_records']
        
        # Extract features
        if analysis:
            feature_engine = FeatureEngineering()
            tick_data = test_storage.get_tick_data('nifty', '2025-07-14', '2025-07-14')
            if not tick_data.empty:
                features = feature_engine.extract_all_features(tick_data)
                
                # Feature count should match tick data count
                assert len(features) == len(tick_data)
        
        # Final stats check
        final_stats = test_storage.get_database_stats()
        assert initial_stats['total_records'] == final_stats['total_records']

class TestSystemIntegration:
    """Test system-level integration"""
    
    def test_configuration_consistency(self):
        """Test that configuration is consistent across components"""
        config = get_config()
        
        # Test that config has required sections
        assert hasattr(config, 'database')
        assert hasattr(config.database, 'connection_string')
        
        # Test that components can use the same config
        from src.data.storage import TickDataStorage
        from src.data.import_manager import DataImportManager
        from src.strategy.professional_trader import ProfessionalTradingStrategy
        
        # All should accept the same config without errors
        # (Using in-memory database for testing)
        storage = TickDataStorage("sqlite:///:memory:")
        storage.init_database()
        
        import_manager = DataImportManager(storage, config)
        strategy = ProfessionalTradingStrategy(storage)
        
        assert import_manager.config == config
        assert strategy.storage == storage
    
    def test_logging_integration(self):
        """Test that logging works across all components"""
        from src.utils.logging import get_logger
        
        # Test that loggers can be created for different components
        loggers = [
            get_logger('test_import'),
            get_logger('test_strategy'),
            get_logger('test_api'),
            get_logger('test_dashboard')
        ]
        
        for logger in loggers:
            assert logger is not None
            # Test that logging doesn't raise errors
            logger.info("Test log message")
    
    def test_database_schema_consistency(self):
        """Test database schema consistency"""
        storage = TickDataStorage("sqlite:///:memory:")
        storage.init_database()
        
        # Test that required tables exist
        tables = storage.get_table_names()
        required_tables = ['tick_data']  # Add other required tables
        
        for table in required_tables:
            assert table in tables, f"Required table missing: {table}"

if __name__ == '__main__':
    pytest.main([__file__, '-v'])
