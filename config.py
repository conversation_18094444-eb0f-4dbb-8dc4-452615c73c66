"""
Configuration management for the Tick Data Analysis Platform
"""
import os
from typing import Dict, Any, Optional
from pathlib import Path
from pydantic_settings import BaseSettings
from pydantic import validator
import logging

class DatabaseConfig(BaseSettings):
    """Database configuration settings"""
    
    # TimescaleDB connection settings
    host: str = "localhost"
    port: int = 5432
    database: str = "tickdata"
    username: str = "postgres"
    password: str = ""
    
    # Connection pool settings
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    
    # Performance settings
    chunk_time_interval: str = "1 day"
    compression_after: str = "7 days"
    
    @property
    def connection_string(self) -> str:
        """Generate database connection string"""
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
    
    class Config:
        env_prefix = "DB_"

class RedisConfig(BaseSettings):
    """Redis configuration settings"""
    
    host: str = "localhost"
    port: int = 6379
    database: int = 0
    password: Optional[str] = None
    
    # Connection settings
    socket_timeout: int = 5
    socket_connect_timeout: int = 5
    max_connections: int = 50
    
    @property
    def connection_string(self) -> str:
        """Generate Redis connection string"""
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.database}"
    
    class Config:
        env_prefix = "REDIS_"

class MLConfig(BaseSettings):
    """Machine Learning configuration settings"""
    
    # Model parameters
    lstm_hidden_dim: int = 128
    lstm_num_layers: int = 2
    lstm_dropout: float = 0.2
    sequence_length: int = 100
    
    # Training parameters
    batch_size: int = 32
    learning_rate: float = 0.001
    weight_decay: float = 1e-5
    epochs: int = 100
    early_stopping_patience: int = 10
    
    # Feature engineering
    volume_window: int = 20
    price_momentum_window: int = 30
    volatility_window: int = 50
    
    # Prediction thresholds
    confidence_threshold: float = 0.7
    magnitude_threshold: float = 0.3
    
    class Config:
        env_prefix = "ML_"

class DataConfig(BaseSettings):
    """Data processing configuration settings"""
    
    # File paths
    data_root: Path = Path("tick_data")
    sample_data_path: Path = Path("sampledata")
    models_path: Path = Path("models")
    logs_path: Path = Path("logs")
    
    # Processing settings
    chunk_size: int = 10000
    max_file_size_mb: int = 500
    parallel_workers: int = 4
    
    # Data validation
    max_price_change_percent: float = 10.0
    min_volume: int = 1
    max_volume: int = 1000000
    
    @validator('data_root', 'sample_data_path', 'models_path', 'logs_path')
    def create_directories(cls, v):
        """Ensure directories exist"""
        v.mkdir(parents=True, exist_ok=True)
        return v
    
    class Config:
        env_prefix = "DATA_"

class APIConfig(BaseSettings):
    """API configuration settings"""
    
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    
    # Security
    secret_key: str = "your-secret-key-change-in-production"
    access_token_expire_minutes: int = 30
    
    # Rate limiting
    rate_limit_per_minute: int = 100
    
    class Config:
        env_prefix = "API_"

class LoggingConfig(BaseSettings):
    """Logging configuration settings"""
    
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_enabled: bool = True
    console_enabled: bool = True
    
    # File logging
    log_file: str = "logs/tickdata.log"
    max_file_size_mb: int = 100
    backup_count: int = 5
    
    class Config:
        env_prefix = "LOG_"

class Config:
    """Main configuration class that combines all settings"""
    
    def __init__(self, env_file: Optional[str] = None):
        """
        Initialize configuration
        
        Args:
            env_file: Path to .env file (optional)
        """
        # Load environment variables from .env file if provided
        if env_file and os.path.exists(env_file):
            from dotenv import load_dotenv
            load_dotenv(env_file)
        
        # Initialize all configuration sections
        self.database = DatabaseConfig()
        self.redis = RedisConfig()
        self.ml = MLConfig()
        self.data = DataConfig()
        self.api = APIConfig()
        self.logging = LoggingConfig()
        
        # Setup logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration"""
        # Create logs directory
        log_dir = Path(self.logging.log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, self.logging.level.upper()),
            format=self.logging.format,
            handlers=self._get_log_handlers()
        )
    
    def _get_log_handlers(self) -> list:
        """Get logging handlers based on configuration"""
        handlers = []
        
        # Console handler
        if self.logging.console_enabled:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(logging.Formatter(self.logging.format))
            handlers.append(console_handler)
        
        # File handler
        if self.logging.file_enabled:
            from logging.handlers import RotatingFileHandler
            file_handler = RotatingFileHandler(
                self.logging.log_file,
                maxBytes=self.logging.max_file_size_mb * 1024 * 1024,
                backupCount=self.logging.backup_count
            )
            file_handler.setFormatter(logging.Formatter(self.logging.format))
            handlers.append(file_handler)
        
        return handlers
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            "database": self.database.dict(),
            "redis": self.redis.dict(),
            "ml": self.ml.dict(),
            "data": self.data.dict(),
            "api": self.api.dict(),
            "logging": self.logging.dict()
        }

# Global configuration instance
config = Config()

# Convenience function to get configuration
def get_config() -> Config:
    """Get the global configuration instance"""
    return config

# Convenience function to reload configuration
def reload_config(env_file: Optional[str] = None) -> Config:
    """Reload configuration with optional env file"""
    global config
    config = Config(env_file)
    return config
