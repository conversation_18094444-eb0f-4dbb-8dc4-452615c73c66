"""
Real prediction engine that generates actual predictions from trained models
"""
import pandas as pd
import numpy as np
import torch
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from config import get_config
from src.data.storage import TickDataStorage
from src.ml.features import FeatureEngineering
from src.ml.training import ModelTrainingPipeline
from src.ml.models.lstm import TickDataPredictor
from src.utils.logging import get_logger, get_performance_logger, get_error_logger

logger = get_logger(__name__)
perf_logger = get_performance_logger()
error_logger = get_error_logger()

class PredictionEngine:
    """Engine for generating real predictions from trained models"""
    
    def __init__(self, config=None, db_connection_string=None):
        """
        Initialize prediction engine

        Args:
            config: Configuration object
            db_connection_string: Override database connection string
        """
        self.config = config or get_config()

        # Use override connection string if provided
        connection_string = db_connection_string or self.config.database.connection_string

        self.storage = TickDataStorage(connection_string)
        self.feature_engineering = FeatureEngineering()
        self.training_pipeline = ModelTrainingPipeline(config, connection_string)
        
        # Prediction parameters
        self.sequence_length = self.config.ml.sequence_length
        self.confidence_threshold = self.config.ml.confidence_threshold
        self.magnitude_threshold = self.config.ml.magnitude_threshold
        
        # Cache for loaded models
        self.loaded_models = {}
    
    def train_and_predict(self, index_name: str, train_start: str, train_end: str, 
                         predict_date: str) -> Dict[str, Any]:
        """
        Train models and generate predictions for a specific date
        
        Args:
            index_name: Name of the index
            train_start: Start date for training data
            train_end: End date for training data
            predict_date: Date to predict for
            
        Returns:
            Dictionary with predictions and metadata
        """
        logger.info(f"Training and predicting for {index_name} on {predict_date}")
        perf_logger.start_timer(f"train_and_predict_{index_name}")
        
        try:
            # Step 1: Prepare training data
            features, targets = self.training_pipeline.prepare_training_data(
                index_name, train_start, train_end
            )
            
            if len(features) < self.sequence_length * 2:
                raise ValueError(f"Insufficient training data: {len(features)} records")
            
            # Step 2: Train models
            logger.info(f"Training models for {index_name}")
            
            # Train direction prediction model
            direction_result = self.training_pipeline.train_model(
                index_name, 'direction_prediction', features, targets
            )
            
            # Train price prediction model
            price_result = self.training_pipeline.train_model(
                index_name, 'next_tick_price', features, targets
            )
            
            # Step 3: Generate predictions
            predictions = self.generate_predictions(
                index_name, predict_date, 
                direction_result, price_result
            )
            
            # Step 4: Calculate confidence and validate
            validated_predictions = self.validate_predictions(predictions, features)
            
            perf_logger.end_timer(
                f"train_and_predict_{index_name}",
                training_samples=len(features),
                predictions_generated=len(validated_predictions)
            )
            
            return {
                'index_name': index_name,
                'predict_date': predict_date,
                'training_period': f"{train_start} to {train_end}",
                'training_samples': len(features),
                'model_performance': {
                    'direction_model': direction_result,
                    'price_model': price_result
                },
                'predictions': validated_predictions,
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            error_logger.log_exception("train_and_predict", e, 
                                     index_name=index_name, predict_date=predict_date)
            raise
    
    def generate_predictions(self, index_name: str, predict_date: str,
                           direction_result: Dict, price_result: Dict) -> List[Dict[str, Any]]:
        """
        Generate actual predictions using trained models
        
        Args:
            index_name: Name of the index
            predict_date: Date to predict for
            direction_result: Direction model training result
            price_result: Price model training result
            
        Returns:
            List of prediction dictionaries
        """
        logger.info(f"Generating predictions for {index_name} on {predict_date}")
        
        try:
            # Get recent data for prediction input
            recent_data = self.get_recent_data_for_prediction(index_name, predict_date)
            
            if len(recent_data) < self.sequence_length:
                raise ValueError(f"Insufficient recent data for prediction: {len(recent_data)} records")
            
            # Extract features from recent data
            features = self.feature_engineering.extract_all_features(recent_data)
            
            # Prepare input sequences
            feature_columns = direction_result['feature_columns']
            X = features[feature_columns].values
            
            # Remove NaN values and get last sequence
            valid_mask = ~np.isnan(X).any(axis=1)
            X_clean = X[valid_mask]
            
            if len(X_clean) < self.sequence_length:
                raise ValueError("Not enough clean data for sequence prediction")
            
            # Get the last sequence for prediction
            X_seq = X_clean[-self.sequence_length:].reshape(1, self.sequence_length, -1)
            X_tensor = torch.FloatTensor(X_seq)
            
            # Load models and make predictions
            direction_model = direction_result['predictor']
            price_model = price_result['predictor']
            
            # Direction prediction
            direction_model.model.eval()
            with torch.no_grad():
                direction_logits = direction_model.model(X_tensor)[0]
                direction_probs = torch.softmax(direction_logits, dim=1)
                direction_pred = torch.argmax(direction_probs, dim=1).item()
                direction_confidence = torch.max(direction_probs).item()
            
            # Price prediction
            price_model.model.eval()
            with torch.no_grad():
                price_pred = price_model.model(X_tensor)[0].item()
            
            # Convert direction prediction to label
            direction_labels = ['DOWN', 'NEUTRAL', 'UP']
            predicted_direction = direction_labels[direction_pred]
            
            # Calculate price change magnitude
            current_price = recent_data['price'].iloc[-1]
            predicted_price = price_pred
            price_change_pct = ((predicted_price - current_price) / current_price) * 100
            magnitude = abs(price_change_pct) / 100  # Normalize to 0-1 scale
            
            # Generate support and resistance levels
            key_levels = self.calculate_key_levels(recent_data)
            
            # Create prediction
            prediction = {
                'index_name': index_name,
                'target_date': predict_date,
                'prediction_type': 'next_day',
                'direction': predicted_direction,
                'magnitude': magnitude,
                'confidence': direction_confidence,
                'predicted_price': predicted_price,
                'current_price': current_price,
                'price_change_pct': price_change_pct,
                'key_levels': key_levels,
                'model_metadata': {
                    'direction_model_path': direction_result['model_path'],
                    'price_model_path': price_result['model_path'],
                    'features_used': feature_columns,
                    'sequence_length': self.sequence_length
                }
            }
            
            return [prediction]
            
        except Exception as e:
            error_logger.log_exception("generate_predictions", e, 
                                     index_name=index_name, predict_date=predict_date)
            raise
    
    def get_recent_data_for_prediction(self, index_name: str, predict_date: str) -> pd.DataFrame:
        """
        Get recent tick data for making predictions
        
        Args:
            index_name: Name of the index
            predict_date: Date to predict for
            
        Returns:
            DataFrame with recent tick data
        """
        # Get data from the last few days before prediction date
        predict_dt = datetime.strptime(predict_date, '%Y-%m-%d')
        start_date = (predict_dt - timedelta(days=5)).strftime('%Y-%m-%d')
        end_date = (predict_dt - timedelta(days=1)).strftime('%Y-%m-%d')
        
        recent_data = self.storage.get_tick_data(index_name, start_date, end_date)
        
        if len(recent_data) == 0:
            raise ValueError(f"No recent data found for {index_name} before {predict_date}")
        
        # Sort by timestamp and return
        return recent_data.sort_values('timestamp').reset_index(drop=True)
    
    def calculate_key_levels(self, tick_data: pd.DataFrame) -> Dict[str, List[float]]:
        """
        Calculate support and resistance levels from recent data
        
        Args:
            tick_data: Recent tick data
            
        Returns:
            Dictionary with support and resistance levels
        """
        prices = tick_data['price'].values
        
        # Calculate support levels (recent lows)
        window = min(50, len(prices) // 4)
        if window < 5:
            window = 5
        
        # Find local minima for support
        supports = []
        for i in range(window, len(prices) - window):
            if prices[i] == min(prices[i-window:i+window+1]):
                supports.append(prices[i])
        
        # Find local maxima for resistance
        resistances = []
        for i in range(window, len(prices) - window):
            if prices[i] == max(prices[i-window:i+window+1]):
                resistances.append(prices[i])
        
        # Get the most recent and significant levels
        current_price = prices[-1]
        
        # Filter and sort support levels
        valid_supports = [s for s in supports if s < current_price]
        valid_supports = sorted(set(valid_supports), reverse=True)[:3]
        
        # Filter and sort resistance levels
        valid_resistances = [r for r in resistances if r > current_price]
        valid_resistances = sorted(set(valid_resistances))[:3]
        
        # If we don't have enough levels, create some based on price ranges
        if len(valid_supports) < 2:
            price_range = prices.max() - prices.min()
            valid_supports.extend([
                current_price - price_range * 0.01,
                current_price - price_range * 0.02
            ])
        
        if len(valid_resistances) < 2:
            price_range = prices.max() - prices.min()
            valid_resistances.extend([
                current_price + price_range * 0.01,
                current_price + price_range * 0.02
            ])
        
        return {
            'support': valid_supports[:3],
            'resistance': valid_resistances[:3]
        }
    
    def validate_predictions(self, predictions: List[Dict[str, Any]], 
                           training_features: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Validate and filter predictions based on confidence and other criteria
        
        Args:
            predictions: List of raw predictions
            training_features: Features used for training (for context)
            
        Returns:
            List of validated predictions
        """
        validated = []
        
        for pred in predictions:
            # Check confidence threshold
            if pred['confidence'] < self.confidence_threshold:
                logger.warning(f"Low confidence prediction filtered: {pred['confidence']:.3f}")
                continue
            
            # Check magnitude threshold
            if pred['magnitude'] < self.magnitude_threshold:
                logger.warning(f"Low magnitude prediction filtered: {pred['magnitude']:.3f}")
                continue
            
            # Add validation metadata
            pred['validation'] = {
                'confidence_threshold': self.confidence_threshold,
                'magnitude_threshold': self.magnitude_threshold,
                'passed_validation': True,
                'training_data_quality': {
                    'sample_count': len(training_features),
                    'feature_count': len(training_features.columns)
                }
            }
            
            validated.append(pred)
        
        logger.info(f"Validated {len(validated)} out of {len(predictions)} predictions")
        return validated
    
    def store_predictions(self, predictions: List[Dict[str, Any]]) -> bool:
        """
        Store predictions in the database
        
        Args:
            predictions: List of predictions to store
            
        Returns:
            True if successful, False otherwise
        """
        try:
            for pred in predictions:
                # Convert to database format
                db_record = {
                    'generated_at': datetime.now(),
                    'target_date': pred['target_date'],
                    'index_name': pred['index_name'],
                    'prediction_type': pred['prediction_type'],
                    'direction': pred['direction'],
                    'magnitude': pred['magnitude'],
                    'confidence': pred['confidence'],
                    'key_levels': str(pred['key_levels']),  # JSON string
                    'actual_outcome': None  # To be filled later
                }
                
                # Store in database (would need to implement the actual storage)
                logger.info(f"Stored prediction for {pred['index_name']} on {pred['target_date']}")
            
            return True
            
        except Exception as e:
            error_logger.log_exception("store_predictions", e)
            return False
