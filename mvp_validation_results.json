{"timestamp": "2025-07-17T16:09:47.982998", "overall_status": "NOT_MVP_READY", "test_results": {"basic_functionality": {"passed": false, "error": "Insufficient data: 3 ticks"}, "cli_tests": {"CLI help": {"returncode": 1, "passed": false, "stdout": "", "stderr": "  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\cli.py\", line 203\n    def daily_import(ctx, date, source_dir, indices, async):\n                                                     ^^^^^\nSyntaxError: invalid syntax\n"}, "Database commands": {"returncode": 1, "passed": false, "stdout": "", "stderr": "  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\cli.py\", line 203\n    def daily_import(ctx, date, source_dir, indices, async):\n                                                     ^^^^^\nSyntaxError: invalid syntax\n"}, "Data commands": {"returncode": 1, "passed": false, "stdout": "", "stderr": "  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\cli.py\", line 203\n    def daily_import(ctx, date, source_dir, indices, async):\n                                                     ^^^^^\nSyntaxError: invalid syntax\n"}, "Trading commands": {"returncode": 1, "passed": false, "stdout": "", "stderr": "  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\cli.py\", line 203\n    def daily_import(ctx, date, source_dir, indices, async):\n                                                     ^^^^^\nSyntaxError: invalid syntax\n"}, "System commands": {"returncode": 1, "passed": false, "stdout": "", "stderr": "  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\cli.py\", line 203\n    def daily_import(ctx, date, source_dir, indices, async):\n                                                     ^^^^^\nSyntaxError: invalid syntax\n"}}, "unit_tests": {"tests/test_import_manager.py": {"returncode": 1, "passed": false, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.13.3, pytest-8.4.1, pluggy-1.6.0 -- C:\\Program Files\\Python313\\python.exe\ncachedir: .pytest_cache\nrootdir: C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\ncollecting ... collected 13 items\n\ntests/test_import_manager.py::TestDataImportManager::test_scan_directory PASSED [  7%]\ntests/test_import_manager.py::TestDataImportManager::test_scan_directory ERROR [  7%]\ntests/test_import_manager.py::TestDataImportManager::test_create_daily_import_job PASSED [ 15%]\ntests/test_import_manager.py::TestDataImportManager::test_create_daily_import_job ERROR [ 15%]\ntests/test_import_manager.py::TestDataImportManager::test_create_bulk_import_job PASSED [ 23%]\ntests/test_import_manager.py::TestDataImportManager::test_create_bulk_import_job ERROR [ 23%]\ntests/test_import_manager.py::TestDataImportManager::test_execute_daily_job PASSED [ 30%]\ntests/test_import_manager.py::TestDataImportManager::test_execute_daily_job ERROR [ 30%]\ntests/test_import_manager.py::TestDataImportManager::test_execute_bulk_job PASSED [ 38%]\ntests/test_import_manager.py::TestDataImportManager::test_execute_bulk_job ERROR [ 38%]\ntests/test_import_manager.py::TestDataImportManager::test_job_history_persistence PASSED [ 46%]\ntests/test_import_manager.py::TestDataImportManager::test_job_history_persistence ERROR [ 46%]\ntests/test_import_manager.py::TestDataImportManager::test_list_jobs PASSED [ 53%]\ntests/test_import_manager.py::TestDataImportManager::test_list_jobs ERROR [ 53%]\ntests/test_import_manager.py::TestDataImportManager::test_cancel_job PASSED [ 61%]\ntests/test_import_manager.py::TestDataImportManager::test_cancel_job ERROR [ 61%]\ntests/test_import_manager.py::TestDataImportManager::test_cleanup_old_jobs PASSED [ 69%]\ntests/test_import_manager.py::TestDataImportManager::test_cleanup_old_jobs ERROR [ 69%]\ntests/test_import_manager.py::TestDataImportManager::test_date_range_filtering PASSED [ 76%]\ntests/test_import_manager.py::TestDataImportManager::test_date_range_filtering ERROR [ 76%]\ntests/test_import_manager.py::TestDataImportManager::test_error_handling PASSED [ 84%]\ntests/test_import_manager.py::TestDataImportManager::test_error_handling ERROR [ 84%]\ntests/test_import_manager.py::TestImportJobDataClass::test_import_job_creation PASSED [ 92%]\ntests/test_import_manager.py::TestFileInfoDataClass::test_file_info_creation ERROR [100%]\n\n=================================== ERRORS ====================================\n_______ ERROR at teardown of TestDataImportManager.test_scan_directory ________\ntests\\test_import_manager.py:30: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp3xbflh2r\\\\test_import.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:52,516 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:52,601 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:52,601 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:52,630 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp3xbflh2r: found 5 data files\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp3xbflh2r: found 5 data files\n___ ERROR at teardown of TestDataImportManager.test_create_daily_import_job ___\ntests\\test_import_manager.py:30: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp228m815f\\\\test_import.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:53,017 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:53,090 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:53,090 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:53,117 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp228m815f: found 5 data files\n2025-07-17 16:09:53,124 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Created daily import job daily_2025-07-14_160953 for 2025-07-14 with 2 files\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp228m815f: found 5 data files\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_160953 for 2025-07-14 with 2 files\n___ ERROR at teardown of TestDataImportManager.test_create_bulk_import_job ____\ntests\\test_import_manager.py:30: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpho7dccds\\\\test_import.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:53,332 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:53,405 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:53,405 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:53,428 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpho7dccds: found 5 data files\n2025-07-17 16:09:53,436 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Created bulk import job bulk_20250717_160953 with 5 files\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpho7dccds: found 5 data files\nINFO     src.data.import_manager:import_manager.py:285 Created bulk import job bulk_20250717_160953 with 5 files\n______ ERROR at teardown of TestDataImportManager.test_execute_daily_job ______\ntests\\test_import_manager.py:30: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmptu08pwdn\\\\test_import.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:53,511 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:53,666 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:53,667 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:53,689 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmptu08pwdn: found 5 data files\\n2025-07-17 16:09:53,697 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Created daily import job daily_2025-07-14_160953 for 2025-07-14 with 1 files\\n2025-07-17 16:09:53,697 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Starting job daily_2025-07-14_160953 (daily)\\n2025-07-17 16:09:53,706 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:09:53,706 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:09:53,724 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 3 records\\n2025-07-17 16:09:53,739 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:09:53,739 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 3 tick data records\\n2025-07-17 16:09:53,739 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:09:53,739 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 3 records from Nifty Ticklist ********.csv\\n2025-07-17 16:09:53,739 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:09:53,740 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Job daily_2025-07-14_160953 completed: 1/1 successful\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmptu08pwdn: found 5 data files\\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_160953 for 2025-07-14 with 1 files\\nINFO     src.data.import_manager:import_manager.py:309 Starting job daily_2025-07-14_160953 (daily)\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 3 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 3 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 3 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:353 Job daily_2025-07-14_160953 completed: 1/1 successful\n______ ERROR at teardown of TestDataImportManager.test_execute_bulk_job _______\ntests\\test_import_manager.py:30: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmps6l8g4sk\\\\test_import.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:53,820 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:53,894 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:53,894 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:53,917 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmps6l8g4sk: found 5 data files\\n2025-07-17 16:09:53,925 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Created bulk import job bulk_20250717_160953 with 2 files\\n2025-07-17 16:09:53,926 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Starting job bulk_20250717_160953 (bulk)\\n2025-07-17 16:09:53,936 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:09:53,936 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:09:53,936 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:09:53,937 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:09:53,955 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for bank_nifty: 3 records\\n2025-07-17 16:09:53,971 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 3 records\\n2025-07-17 16:09:53,981 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:09:53,981 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 3 tick data records\\n2025-07-17 16:09:53,982 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Bank Nifty Ticklist ********.csv\\n2025-07-17 16:09:53,982 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 3 records from Bank Nifty Ticklist ********.csv\\n2025-07-17 16:09:53,982 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:09:54,003 - performance - \\x1b[33mWARNING\\x1b[0m - No start time found for operation: store_tick_data\\n2025-07-17 16:09:54,003 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 3 tick data records\\n2025-07-17 16:09:54,003 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:09:54,003 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 3 records from Nifty Ticklist ********.csv\\n2025-07-17 16:09:54,003 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:09:54,004 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Job bulk_20250717_160953 completed: 2/2 successful\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmps6l8g4sk: found 5 data files\\nINFO     src.data.import_manager:import_manager.py:285 Created bulk import job bulk_20250717_160953 with 2 files\\nINFO     src.data.import_manager:import_manager.py:309 Starting job bulk_20250717_160953 (bulk)\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for bank_nifty: 3 records\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 3 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 3 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 3 records from Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\nWARNING  performance:logging.py:213 No start time found for operation: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 3 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 3 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:353 Job bulk_20250717_160953 completed: 2/2 successful\n___ ERROR at teardown of TestDataImportManager.test_job_history_persistence ___\ntests\\test_import_manager.py:30: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpnm_t4w_e\\\\test_import.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:54,084 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:54,152 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:54,152 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:54,174 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpnm_t4w_e: found 5 data files\n2025-07-17 16:09:54,185 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Created daily import job daily_2025-07-14_160954 for 2025-07-14 with 5 files\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpnm_t4w_e: found 5 data files\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_160954 for 2025-07-14 with 5 files\n__________ ERROR at teardown of TestDataImportManager.test_list_jobs __________\ntests\\test_import_manager.py:30: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp7p5vh53t\\\\test_import.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:54,253 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:54,317 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:54,318 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:54,343 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp7p5vh53t: found 5 data files\n2025-07-17 16:09:54,351 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Created daily import job daily_2025-07-14_160954 for 2025-07-14 with 5 files\n2025-07-17 16:09:54,353 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp7p5vh53t: found 5 data files\n2025-07-17 16:09:54,365 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Created bulk import job bulk_20250717_160954 with 5 files\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp7p5vh53t: found 5 data files\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_160954 for 2025-07-14 with 5 files\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp7p5vh53t: found 5 data files\nINFO     src.data.import_manager:import_manager.py:285 Created bulk import job bulk_20250717_160954 with 5 files\n_________ ERROR at teardown of TestDataImportManager.test_cancel_job __________\ntests\\test_import_manager.py:30: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp4i_qs8ya\\\\test_import.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:54,455 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:54,530 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:54,530 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:54,552 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4i_qs8ya: found 5 data files\n2025-07-17 16:09:54,563 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Created daily import job daily_2025-07-14_160954 for 2025-07-14 with 5 files\n2025-07-17 16:09:54,572 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Job daily_2025-07-14_160954 cancelled\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4i_qs8ya: found 5 data files\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_160954 for 2025-07-14 with 5 files\nINFO     src.data.import_manager:import_manager.py:421 Job daily_2025-07-14_160954 cancelled\n______ ERROR at teardown of TestDataImportManager.test_cleanup_old_jobs _______\ntests\\test_import_manager.py:30: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp95_3420n\\\\test_import.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:54,644 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:54,727 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:54,727 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:54,753 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp95_3420n: found 5 data files\n2025-07-17 16:09:54,764 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Created daily import job daily_2025-07-14_160954 for 2025-07-14 with 5 files\n2025-07-17 16:09:54,785 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Cleaned up 1 old jobs\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp95_3420n: found 5 data files\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_160954 for 2025-07-14 with 5 files\nINFO     src.data.import_manager:import_manager.py:440 Cleaned up 1 old jobs\n____ ERROR at teardown of TestDataImportManager.test_date_range_filtering _____\ntests\\test_import_manager.py:30: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp4tdqtgnw\\\\test_import.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:54,874 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:54,952 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:54,952 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:54,968 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4tdqtgnw: found 3 data files\n2025-07-17 16:09:54,978 - src.data.import_manager - \u001b[32mINFO\u001b[0m - Created bulk import job bulk_20250717_160954 with 1 files\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4tdqtgnw: found 3 data files\nINFO     src.data.import_manager:import_manager.py:285 Created bulk import job bulk_20250717_160954 with 1 files\n_______ ERROR at teardown of TestDataImportManager.test_error_handling ________\ntests\\test_import_manager.py:30: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp6w_a0q_9\\\\test_import.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:55,049 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:55,130 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:55,131 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:55,135 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp6w_a0q_9: found 1 data files\\n2025-07-17 16:09:55,146 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Created daily import job daily_2025-07-14_160955 for 2025-07-14 with 1 files\\n2025-07-17 16:09:55,146 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Starting job daily_2025-07-14_160955 (daily)\\n2025-07-17 16:09:55,155 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:09:55,155 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:09:55,161 - src.data.ingestion - \\x1b[31mERROR\\x1b[0m - File validation failed for Nifty Ticklist ********.csv: [\"Missing expected columns: {'Volume', 'Time', 'Last Rate'}\", 'File is empty']\\n2025-07-17 16:09:55,161 - src.data.import_manager - \\x1b[31mERROR\\x1b[0m - \\u274c Nifty Ticklist ********.csv import failed\\n2025-07-17 16:09:55,162 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Job daily_2025-07-14_160955 completed: 0/1 successful\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp6w_a0q_9: found 1 data files\\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_160955 for 2025-07-14 with 1 files\\nINFO     src.data.import_manager:import_manager.py:309 Starting job daily_2025-07-14_160955 (daily)\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nERROR    src.data.ingestion:ingestion.py:60 File validation failed for Nifty Ticklist ********.csv: [\"Missing expected columns: {'Volume', 'Time', 'Last Rate'}\", 'File is empty']\\nERROR    src.data.import_manager:import_manager.py:376 \\u274c Nifty Ticklist ********.csv import failed\\nINFO     src.data.import_manager:import_manager.py:353 Job daily_2025-07-14_160955 completed: 0/1 successful\n_______ ERROR at setup of TestFileInfoDataClass.test_file_info_creation _______\nfile C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\tests\\test_import_manager.py, line 291\n      def test_file_info_creation(self, temp_dir):\nE       fixture 'temp_dir' not found\n>       available fixtures: cache, capfd, capfdbinary, caplog, capsys, capsysbinary, capteesys, cleanup_test_files, doctest_namespace, mock_model_config, monkeypatch, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, sample_csv_file, sample_features_data, sample_multi_index_data, sample_prediction_data, sample_tick_data, test_config, test_db_engine, test_storage, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory\n>       use 'pytest --fixtures [testpath]' for help on them.\n\nC:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\tests\\test_import_manager.py:291\n============================== warnings summary ===============================\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n  C:\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323: PydanticDeprecatedSince20: Support for class-based `config` is deprecated, use ConfigDict instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n    warnings.warn(DEPRECATION_MESSAGE, DeprecationWarning)\n\nconfig.py:107\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\config.py:107: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n    @validator('data_root', 'sample_data_path', 'models_path', 'logs_path')\n\n-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html\n=========================== short test summary info ===========================\nERROR tests/test_import_manager.py::TestDataImportManager::test_scan_directory\nERROR tests/test_import_manager.py::TestDataImportManager::test_create_daily_import_job\nERROR tests/test_import_manager.py::TestDataImportManager::test_create_bulk_import_job\nERROR tests/test_import_manager.py::TestDataImportManager::test_execute_daily_job\nERROR tests/test_import_manager.py::TestDataImportManager::test_execute_bulk_job\nERROR tests/test_import_manager.py::TestDataImportManager::test_job_history_persistence\nERROR tests/test_import_manager.py::TestDataImportManager::test_list_jobs - P...\nERROR tests/test_import_manager.py::TestDataImportManager::test_cancel_job - ...\nERROR tests/test_import_manager.py::TestDataImportManager::test_cleanup_old_jobs\nERROR tests/test_import_manager.py::TestDataImportManager::test_date_range_filtering\nERROR tests/test_import_manager.py::TestDataImportManager::test_error_handling\nERROR tests/test_import_manager.py::TestFileInfoDataClass::test_file_info_creation\n================== 12 passed, 7 warnings, 12 errors in 2.92s ==================\n", "stderr": ""}, "tests/test_trading_analysis.py": {"returncode": 1, "passed": false, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.13.3, pytest-8.4.1, pluggy-1.6.0 -- C:\\Program Files\\Python313\\python.exe\ncachedir: .pytest_cache\nrootdir: C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\ncollecting ... collected 13 items\n\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_analyze_market_structure FAILED [  7%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_analyze_market_structure ERROR [  7%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_generate_trading_signals FAILED [ 15%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_generate_trading_signals ERROR [ 15%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_volume_breakout_analysis FAILED [ 23%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_volume_breakout_analysis ERROR [ 23%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_support_resistance_identification FAILED [ 30%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_support_resistance_identification ERROR [ 30%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_order_flow_analysis FAILED [ 38%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_order_flow_analysis ERROR [ 38%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_market_character_assessment FAILED [ 46%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_market_character_assessment ERROR [ 46%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_risk_reward_calculation FAILED [ 53%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_risk_reward_calculation ERROR [ 53%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_confidence_scoring FAILED [ 61%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_confidence_scoring ERROR [ 61%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_no_data_handling FAILED [ 69%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_no_data_handling ERROR [ 69%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_multiple_indices FAILED [ 76%]\ntests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_multiple_indices ERROR [ 76%]\ntests/test_trading_analysis.py::TestFeatureEngineering::test_extract_all_features FAILED [ 84%]\ntests/test_trading_analysis.py::TestFeatureEngineering::test_volume_features PASSED [ 92%]\ntests/test_trading_analysis.py::TestFeatureEngineering::test_price_features PASSED [100%]\n\n=================================== ERRORS ====================================\n_ ERROR at teardown of TestProfessionalTradingStrategy.test_analyze_market_structure _\ntests\\test_trading_analysis.py:31: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpty106t62\\\\test_trading.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:57,712 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:57,785 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:57,785 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:09:57,919 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:09:57,919 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:57,920 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n_ ERROR at teardown of TestProfessionalTradingStrategy.test_generate_trading_signals _\ntests\\test_trading_analysis.py:31: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpwwesnrvv\\\\test_trading.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:58,365 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:58,436 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:58,436 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:09:58,547 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:09:58,547 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:58,548 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n_ ERROR at teardown of TestProfessionalTradingStrategy.test_volume_breakout_analysis _\ntests\\test_trading_analysis.py:31: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpkz9oqaw_\\\\test_trading.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:58,704 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:58,776 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:58,776 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:09:58,887 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:09:58,887 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:58,888 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n_ ERROR at teardown of TestProfessionalTradingStrategy.test_support_resistance_identification _\ntests\\test_trading_analysis.py:31: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpgw2ph5x0\\\\test_trading.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:59,041 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:59,114 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:59,114 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:09:59,227 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:09:59,227 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:59,228 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n_ ERROR at teardown of TestProfessionalTradingStrategy.test_order_flow_analysis _\ntests\\test_trading_analysis.py:31: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmphgi0ttlg\\\\test_trading.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:59,380 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:59,488 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:59,489 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:09:59,606 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:09:59,606 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:59,607 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n_ ERROR at teardown of TestProfessionalTradingStrategy.test_market_character_assessment _\ntests\\test_trading_analysis.py:31: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpbf3xyr1u\\\\test_trading.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:59,761 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:59,834 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:59,834 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:09:59,948 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:09:59,948 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:59,949 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n_ ERROR at teardown of TestProfessionalTradingStrategy.test_risk_reward_calculation _\ntests\\test_trading_analysis.py:31: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp81q_wzwg\\\\test_trading.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:00,107 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:00,177 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:00,177 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:00,349 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:00,349 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:00,350 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n_ ERROR at teardown of TestProfessionalTradingStrategy.test_confidence_scoring _\ntests\\test_trading_analysis.py:31: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpz1vlxlzj\\\\test_trading.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:00,510 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:00,583 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:00,583 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:00,702 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:00,702 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:00,703 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n_ ERROR at teardown of TestProfessionalTradingStrategy.test_no_data_handling __\ntests\\test_trading_analysis.py:31: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp3q_uowed\\\\test_trading.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:00,921 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:01,052 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:01,052 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:01,176 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:01,176 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:01,177 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nonexistent on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nonexistent on 2025-07-14\n_ ERROR at teardown of TestProfessionalTradingStrategy.test_multiple_indices __\ntests\\test_trading_analysis.py:31: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpwtnejm1p\\\\test_trading.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:01,284 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:01,451 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:01,451 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:01,573 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:01,573 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:01,691 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:01,693 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n2025-07-17 16:10:01,693 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n2025-07-17 16:10:01,770 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for bank_nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for bank_nifty on 2025-07-14\n================================== FAILURES ===================================\n________ TestProfessionalTradingStrategy.test_analyze_market_structure ________\ntests\\test_trading_analysis.py:93: in test_analyze_market_structure\n    assert key in analysis, f\"Missing key: {key}\"\nE   AssertionError: Missing key: price_levels\nE   assert 'price_levels' in {'date': '2025-07-14', 'exhaustion_patterns': [], 'index_name': 'nifty', 'key_levels': {'all_resistance': [np.float64(25192.************)], 'all_support': [np.float64(25184.***********)], 'current_price': np.float64(25188.33), 'resistance': [np.float64(25192.************)], ...}, ...}\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:57,712 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:57,785 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:57,785 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:09:57,919 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:09:57,919 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:57,920 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n________ TestProfessionalTradingStrategy.test_generate_trading_signals ________\ntests\\test_trading_analysis.py:116: in test_generate_trading_signals\n    signals_low = trading_strategy.generate_trading_signals(analysis, confidence_threshold=0.5)\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   TypeError: ProfessionalTradingStrategy.generate_trading_signals() got an unexpected keyword argument 'confidence_threshold'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:58,365 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:58,436 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:58,436 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:09:58,547 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:09:58,547 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:58,548 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n________ TestProfessionalTradingStrategy.test_volume_breakout_analysis ________\ntests\\test_trading_analysis.py:148: in test_volume_breakout_analysis\n    assert volume_analysis['percentile_75'] <= volume_analysis['percentile_90']\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   KeyError: 'percentile_75'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:58,704 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:58,776 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:58,776 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:09:58,887 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:09:58,887 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:58,888 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n___ TestProfessionalTradingStrategy.test_support_resistance_identification ____\ntests\\test_trading_analysis.py:161: in test_support_resistance_identification\n    price_levels = analysis['price_levels']\n                   ^^^^^^^^^^^^^^^^^^^^^^^^\nE   KeyError: 'price_levels'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:59,041 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:59,114 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:59,114 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:09:59,227 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:09:59,227 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:59,228 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n__________ TestProfessionalTradingStrategy.test_order_flow_analysis ___________\ntests\\test_trading_analysis.py:187: in test_order_flow_analysis\n    assert 'net_flow' in order_flow\nE   AssertionError: assert 'net_flow' in {'buying_pressure': np.float64(0.4990181599831221), 'flow_strength': np.float64(0.008552556841234071), 'order_flow_imbalance': np.float64(0.008552556841234071), 'recent_momentum': -0.08, ...}\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:59,380 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:59,488 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:59,489 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:09:59,606 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:09:59,606 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:59,607 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n______ TestProfessionalTradingStrategy.test_market_character_assessment _______\ntests\\test_trading_analysis.py:198: in test_market_character_assessment\n    market_character = analysis['market_character']\n                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   KeyError: 'market_character'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:09:59,761 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:09:59,834 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:09:59,834 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:09:59,948 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:09:59,948 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:09:59,949 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n________ TestProfessionalTradingStrategy.test_risk_reward_calculation _________\ntests\\test_trading_analysis.py:218: in test_risk_reward_calculation\n    signals = trading_strategy.generate_trading_signals(analysis, confidence_threshold=0.6)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   TypeError: ProfessionalTradingStrategy.generate_trading_signals() got an unexpected keyword argument 'confidence_threshold'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:00,107 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:00,177 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:00,177 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:00,349 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:00,349 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:00,350 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n___________ TestProfessionalTradingStrategy.test_confidence_scoring ___________\ntests\\test_trading_analysis.py:245: in test_confidence_scoring\n    signals = trading_strategy.generate_trading_signals(analysis, confidence_threshold=0.3)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   TypeError: ProfessionalTradingStrategy.generate_trading_signals() got an unexpected keyword argument 'confidence_threshold'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:00,510 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:00,583 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:00,583 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:00,702 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:00,702 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:00,703 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n____________ TestProfessionalTradingStrategy.test_no_data_handling ____________\ntests\\test_trading_analysis.py:271: in test_no_data_handling\n    analysis = trading_strategy.analyze_market_structure('nonexistent', '2025-07-14')\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nsrc\\strategy\\professional_trader.py:51: in analyze_market_structure\n    raise ValueError(f\"Insufficient data: {len(tick_data)} ticks\")\nE   ValueError: Insufficient data: 0 ticks\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:00,921 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:01,052 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:01,052 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:01,176 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:01,176 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:01,177 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nonexistent on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nonexistent on 2025-07-14\n____________ TestProfessionalTradingStrategy.test_multiple_indices ____________\ntests\\test_trading_analysis.py:294: in test_multiple_indices\n    nifty_price = nifty_analysis['price_levels']['current_price']\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   KeyError: 'price_levels'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:01,284 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:01,451 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:01,451 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:01,573 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:01,573 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:01,691 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:01,693 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 1000 tick data records\n2025-07-17 16:10:01,693 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n2025-07-17 16:10:01,770 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for bank_nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 1000 tick data records\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for bank_nifty on 2025-07-14\n______________ TestFeatureEngineering.test_extract_all_features _______________\ntests\\test_trading_analysis.py:333: in test_extract_all_features\n    assert nan_ratio < 0.1, f\"Too many NaN values: {nan_ratio:.2%}\"\nE   AssertionError: Too many NaN values: 20.65%\nE   assert np.float64(0.*****************) < 0.1\n============================== warnings summary ===============================\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n  C:\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323: PydanticDeprecatedSince20: Support for class-based `config` is deprecated, use ConfigDict instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n    warnings.warn(DEPRECATION_MESSAGE, DeprecationWarning)\n\nconfig.py:107\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\config.py:107: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n    @validator('data_root', 'sample_data_path', 'models_path', 'logs_path')\n\ntests/test_trading_analysis.py: 10 warnings\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\strategy\\professional_trader.py:355: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n    '1min': tick_data.resample('1T').agg({\n\ntests/test_trading_analysis.py: 10 warnings\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\strategy\\professional_trader.py:359: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n    '5min': tick_data.resample('5T').agg({\n\ntests/test_trading_analysis.py: 10 warnings\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\strategy\\professional_trader.py:363: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n    '15min': tick_data.resample('15T').agg({\n\ntests/test_trading_analysis.py::TestFeatureEngineering::test_extract_all_features\ntests/test_trading_analysis.py::TestFeatureEngineering::test_volume_features\ntests/test_trading_analysis.py::TestFeatureEngineering::test_price_features\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\tests\\test_trading_analysis.py:310: FutureWarning: 'S' is deprecated and will be removed in a future version, please use 's' instead.\n    timestamps = pd.date_range('2025-07-14 09:15:00', periods=100, freq='2S')\n\n-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html\n=========================== short test summary info ===========================\nFAILED tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_analyze_market_structure\nFAILED tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_generate_trading_signals\nFAILED tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_volume_breakout_analysis\nFAILED tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_support_resistance_identification\nFAILED tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_order_flow_analysis\nFAILED tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_market_character_assessment\nFAILED tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_risk_reward_calculation\nFAILED tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_confidence_scoring\nFAILED tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_no_data_handling\nFAILED tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_multiple_indices\nFAILED tests/test_trading_analysis.py::TestFeatureEngineering::test_extract_all_features\nERROR tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_analyze_market_structure\nERROR tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_generate_trading_signals\nERROR tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_volume_breakout_analysis\nERROR tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_support_resistance_identification\nERROR tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_order_flow_analysis\nERROR tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_market_character_assessment\nERROR tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_risk_reward_calculation\nERROR tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_confidence_scoring\nERROR tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_no_data_handling\nERROR tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_multiple_indices\n============ 11 failed, 2 passed, 40 warnings, 10 errors in 4.42s =============\n", "stderr": ""}, "tests/test_interfaces.py": {"returncode": 1, "passed": false, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.13.3, pytest-8.4.1, pluggy-1.6.0 -- C:\\Program Files\\Python313\\python.exe\ncachedir: .pytest_cache\nrootdir: C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\ncollecting ... collected 20 items\n\ntests/test_interfaces.py::TestCLIInterface::test_cli_help_command FAILED [  5%]\ntests/test_interfaces.py::TestCLIInterface::test_cli_db_commands FAILED  [ 10%]\ntests/test_interfaces.py::TestCLIInterface::test_cli_data_commands FAILED [ 15%]\ntests/test_interfaces.py::TestCLIInterface::test_cli_trading_commands FAILED [ 20%]\ntests/test_interfaces.py::TestCLIInterface::test_cli_system_commands FAILED [ 25%]\ntests/test_interfaces.py::TestAPIEndpoints::test_health_endpoint PASSED  [ 30%]\ntests/test_interfaces.py::TestAPIEndpoints::test_health_endpoint ERROR   [ 30%]\ntests/test_interfaces.py::TestAPIEndpoints::test_system_status_endpoint PASSED [ 35%]\ntests/test_interfaces.py::TestAPIEndpoints::test_system_status_endpoint ERROR [ 35%]\ntests/test_interfaces.py::TestAPIEndpoints::test_analysis_endpoint FAILED [ 40%]\ntests/test_interfaces.py::TestAPIEndpoints::test_analysis_endpoint ERROR [ 40%]\ntests/test_interfaces.py::TestAPIEndpoints::test_signals_endpoint PASSED [ 45%]\ntests/test_interfaces.py::TestAPIEndpoints::test_signals_endpoint ERROR  [ 45%]\ntests/test_interfaces.py::TestAPIEndpoints::test_data_export_endpoint FAILED [ 50%]\ntests/test_interfaces.py::TestAPIEndpoints::test_data_export_endpoint ERROR [ 50%]\ntests/test_interfaces.py::TestAPIEndpoints::test_import_jobs_endpoint PASSED [ 55%]\ntests/test_interfaces.py::TestAPIEndpoints::test_import_jobs_endpoint ERROR [ 55%]\ntests/test_interfaces.py::TestAPIEndpoints::test_api_error_handling FAILED [ 60%]\ntests/test_interfaces.py::TestAPIEndpoints::test_api_error_handling ERROR [ 60%]\ntests/test_interfaces.py::TestWebDashboard::test_dashboard_main_page PASSED [ 65%]\ntests/test_interfaces.py::TestWebDashboard::test_dashboard_main_page ERROR [ 65%]\ntests/test_interfaces.py::TestWebDashboard::test_dashboard_api_status PASSED [ 70%]\ntests/test_interfaces.py::TestWebDashboard::test_dashboard_api_status ERROR [ 70%]\ntests/test_interfaces.py::TestWebDashboard::test_dashboard_import_jobs PASSED [ 75%]\ntests/test_interfaces.py::TestWebDashboard::test_dashboard_import_jobs ERROR [ 75%]\ntests/test_interfaces.py::TestWebDashboard::test_dashboard_latest_data PASSED [ 80%]\ntests/test_interfaces.py::TestWebDashboard::test_dashboard_latest_data ERROR [ 80%]\ntests/test_interfaces.py::TestPlatformManager::test_platform_manager_help PASSED [ 85%]\ntests/test_interfaces.py::TestPlatformManager::test_platform_start_help PASSED [ 90%]\ntests/test_interfaces.py::TestIntegrationWorkflows::test_cli_to_api_workflow FAILED [ 95%]\ntests/test_interfaces.py::TestIntegrationWorkflows::test_cli_to_api_workflow ERROR [ 95%]\ntests/test_interfaces.py::TestIntegrationWorkflows::test_error_propagation PASSED [100%]\n\n=================================== ERRORS ====================================\n_________ ERROR at teardown of TestAPIEndpoints.test_health_endpoint __________\ntests\\test_interfaces.py:127: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp4auv1ywi\\\\test_api.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:06,573 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:06,643 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:06,644 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:06,668 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:06,669 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 100 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 100 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:06,701 - errors - \u001b[31mERROR\u001b[0m - Exception in get_database_stats: 'str' object has no attribute 'strftime'\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\data\\storage.py\", line 410, in get_database_stats\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\n                    ^^^^^^^^^^^^^^^^^^\nAttributeError: 'str' object has no attribute 'strftime'\n------------------------------ Captured log call ------------------------------\nERROR    errors:logging.py:267 Exception in get_database_stats: 'str' object has no attribute 'strftime'\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\data\\storage.py\", line 410, in get_database_stats\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\n                    ^^^^^^^^^^^^^^^^^^\nAttributeError: 'str' object has no attribute 'strftime'\n______ ERROR at teardown of TestAPIEndpoints.test_system_status_endpoint ______\ntests\\test_interfaces.py:127: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp3gwbr_ny\\\\test_api.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:06,772 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:06,839 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:06,840 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:06,863 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:06,863 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 100 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 100 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:06,880 - errors - \u001b[31mERROR\u001b[0m - Exception in get_database_stats: 'str' object has no attribute 'strftime'\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\data\\storage.py\", line 410, in get_database_stats\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\n                    ^^^^^^^^^^^^^^^^^^\nAttributeError: 'str' object has no attribute 'strftime'\n2025-07-17 16:10:06,881 - errors - \u001b[31mERROR\u001b[0m - Exception in get_data_quality_stats: 'str' object has no attribute 'isoformat'\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\data\\storage.py\", line 442, in get_data_quality_stats\n    latest_timestamp = result[0].isoformat() if result and result[0] else \"No data\"\n                       ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'str' object has no attribute 'isoformat'. Did you mean: 'format'?\n------------------------------ Captured log call ------------------------------\nERROR    errors:logging.py:267 Exception in get_database_stats: 'str' object has no attribute 'strftime'\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\data\\storage.py\", line 410, in get_database_stats\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\n                    ^^^^^^^^^^^^^^^^^^\nAttributeError: 'str' object has no attribute 'strftime'\nERROR    errors:logging.py:267 Exception in get_data_quality_stats: 'str' object has no attribute 'isoformat'\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\data\\storage.py\", line 442, in get_data_quality_stats\n    latest_timestamp = result[0].isoformat() if result and result[0] else \"No data\"\n                       ^^^^^^^^^^^^^^^^^^^\nAttributeError: 'str' object has no attribute 'isoformat'. Did you mean: 'format'?\n________ ERROR at teardown of TestAPIEndpoints.test_analysis_endpoint _________\ntests\\test_interfaces.py:127: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpu53nh95p\\\\test_api.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:06,951 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:07,019 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:07,019 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:07,041 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:07,041 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 100 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 100 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:07,054 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n_________ ERROR at teardown of TestAPIEndpoints.test_signals_endpoint _________\ntests\\test_interfaces.py:127: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp8315bh0v\\\\test_api.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:07,515 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:07,589 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:07,590 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:07,613 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:07,613 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 100 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 100 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:07,630 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n2025-07-17 16:10:07,651 - src.api.endpoints - \u001b[31mERROR\u001b[0m - Error generating signals for nifty: ProfessionalTradingStrategy.generate_trading_signals() got an unexpected keyword argument 'confidence_threshold'\n2025-07-17 16:10:07,651 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for bank_nifty on 2025-07-14\n2025-07-17 16:10:07,653 - src.api.endpoints - \u001b[31mERROR\u001b[0m - Error generating signals for bank_nifty: Insufficient data: 0 ticks\n2025-07-17 16:10:07,653 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for fin_nifty on 2025-07-14\n2025-07-17 16:10:07,654 - src.api.endpoints - \u001b[31mERROR\u001b[0m - Error generating signals for fin_nifty: Insufficient data: 0 ticks\n2025-07-17 16:10:07,654 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for midcap_nifty on 2025-07-14\n2025-07-17 16:10:07,655 - src.api.endpoints - \u001b[31mERROR\u001b[0m - Error generating signals for midcap_nifty: Insufficient data: 0 ticks\n2025-07-17 16:10:07,655 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty_next on 2025-07-14\n2025-07-17 16:10:07,657 - src.api.endpoints - \u001b[31mERROR\u001b[0m - Error generating signals for nifty_next: Insufficient data: 0 ticks\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\nERROR    src.api.endpoints:endpoints.py:341 Error generating signals for nifty: ProfessionalTradingStrategy.generate_trading_signals() got an unexpected keyword argument 'confidence_threshold'\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for bank_nifty on 2025-07-14\nERROR    src.api.endpoints:endpoints.py:341 Error generating signals for bank_nifty: Insufficient data: 0 ticks\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for fin_nifty on 2025-07-14\nERROR    src.api.endpoints:endpoints.py:341 Error generating signals for fin_nifty: Insufficient data: 0 ticks\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for midcap_nifty on 2025-07-14\nERROR    src.api.endpoints:endpoints.py:341 Error generating signals for midcap_nifty: Insufficient data: 0 ticks\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty_next on 2025-07-14\nERROR    src.api.endpoints:endpoints.py:341 Error generating signals for nifty_next: Insufficient data: 0 ticks\n_______ ERROR at teardown of TestAPIEndpoints.test_data_export_endpoint _______\ntests\\test_interfaces.py:127: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpo9jz4y4q\\\\test_api.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:07,724 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:07,794 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:07,794 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:07,815 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:07,815 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 100 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 100 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:07,829 - src.api.endpoints - \u001b[31mERROR\u001b[0m - Export data error: TypeConversionDict.get() got an unexpected keyword argument 'required'\n------------------------------ Captured log call ------------------------------\nERROR    src.api.endpoints:endpoints.py:245 Export data error: TypeConversionDict.get() got an unexpected keyword argument 'required'\n_______ ERROR at teardown of TestAPIEndpoints.test_import_jobs_endpoint _______\ntests\\test_interfaces.py:127: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmphxbys64i\\\\test_api.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:07,917 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:07,995 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:07,996 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:08,021 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:08,022 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 100 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 100 tick data records\n________ ERROR at teardown of TestAPIEndpoints.test_api_error_handling ________\ntests\\test_interfaces.py:127: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpaesjh0lx\\\\test_api.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:08,110 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:08,197 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:08,197 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:08,222 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:08,222 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 100 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 100 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:08,241 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for invalid_index on 2025-07-14\n2025-07-17 16:10:08,244 - src.api.endpoints - \u001b[31mERROR\u001b[0m - Market analysis error: Insufficient data: 0 ticks\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for invalid_index on 2025-07-14\nERROR    src.api.endpoints:endpoints.py:275 Market analysis error: Insufficient data: 0 ticks\n_______ ERROR at teardown of TestWebDashboard.test_dashboard_main_page ________\ntests\\test_interfaces.py:250: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp72xac4qq\\\\test_dashboard.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:08,324 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:08,404 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:08,404 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n_______ ERROR at teardown of TestWebDashboard.test_dashboard_api_status _______\ntests\\test_interfaces.py:250: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpzufj79wf\\\\test_dashboard.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:08,683 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:08,767 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:08,767 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n______ ERROR at teardown of TestWebDashboard.test_dashboard_import_jobs _______\ntests\\test_interfaces.py:250: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpxpevq225\\\\test_dashboard.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:08,896 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:08,981 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:08,982 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n______ ERROR at teardown of TestWebDashboard.test_dashboard_latest_data _______\ntests\\test_interfaces.py:250: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpat9rcohn\\\\test_dashboard.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:09,065 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:09,147 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:09,147 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n___ ERROR at teardown of TestIntegrationWorkflows.test_cli_to_api_workflow ____\ntests\\test_interfaces.py:343: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpuyzz5kkw\\\\integration_test.db'\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:15,296 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:15,379 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:15,380 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:15,537 - errors - \u001b[31mERROR\u001b[0m - Exception in database_initialization: No module named 'psycopg2'\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\data\\storage.py\", line 32, in __init__\n    self.engine = create_engine(\n                  ~~~~~~~~~~~~~^\n        db_connection_string,\n        ^^^^^^^^^^^^^^^^^^^^^\n    ...<5 lines>...\n        echo=False  # Set to True for SQL debugging\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"<string>\", line 2, in create_engine\n  File \"C:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py\", line 281, in warned\n    return fn(*args, **kwargs)  # type: ignore[no-any-return]\n  File \"C:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\engine\\create.py\", line 602, in create_engine\n    dbapi = dbapi_meth(**dbapi_args)\n  File \"C:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py\", line 696, in import_dbapi\n    import psycopg2\nModuleNotFoundError: No module named 'psycopg2'\n------------------------------ Captured log call ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nERROR    errors:logging.py:267 Exception in database_initialization: No module named 'psycopg2'\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\data\\storage.py\", line 32, in __init__\n    self.engine = create_engine(\n                  ~~~~~~~~~~~~~^\n        db_connection_string,\n        ^^^^^^^^^^^^^^^^^^^^^\n    ...<5 lines>...\n        echo=False  # Set to True for SQL debugging\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"<string>\", line 2, in create_engine\n  File \"C:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py\", line 281, in warned\n    return fn(*args, **kwargs)  # type: ignore[no-any-return]\n  File \"C:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\engine\\create.py\", line 602, in create_engine\n    dbapi = dbapi_meth(**dbapi_args)\n  File \"C:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py\", line 696, in import_dbapi\n    import psycopg2\nModuleNotFoundError: No module named 'psycopg2'\n================================== FAILURES ===================================\n___________________ TestCLIInterface.test_cli_help_command ____________________\ntests\\test_interfaces.py:64: in test_cli_help_command\n    assert result.returncode == 0\nE   assert 1 == 0\nE    +  where 1 = CompletedProcess(args=['C:\\\\Program Files\\\\Python313\\\\python.exe', 'cli.py', '--help'], returncode=1, stdout='', stderr='  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\cli.py\", line 203\\n    def daily_import(ctx, date, source_dir, indices, async):\\n                                                     ^^^^^\\nSyntaxError: invalid syntax\\n').returncode\n____________________ TestCLIInterface.test_cli_db_commands ____________________\ntests\\test_interfaces.py:78: in test_cli_db_commands\n    assert result.returncode == 0\nE   assert 1 == 0\nE    +  where 1 = CompletedProcess(args=['C:\\\\Program Files\\\\Python313\\\\python.exe', 'cli.py', 'db', '--help'], returncode=1, stdout='', stderr='  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\cli.py\", line 203\\n    def daily_import(ctx, date, source_dir, indices, async):\\n                                                     ^^^^^\\nSyntaxError: invalid syntax\\n').returncode\n___________________ TestCLIInterface.test_cli_data_commands ___________________\ntests\\test_interfaces.py:90: in test_cli_data_commands\n    assert result.returncode == 0\nE   assert 1 == 0\nE    +  where 1 = CompletedProcess(args=['C:\\\\Program Files\\\\Python313\\\\python.exe', 'cli.py', 'data', '--help'], returncode=1, stdout='', stderr='  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\cli.py\", line 203\\n    def daily_import(ctx, date, source_dir, indices, async):\\n                                                     ^^^^^\\nSyntaxError: invalid syntax\\n').returncode\n_________________ TestCLIInterface.test_cli_trading_commands __________________\ntests\\test_interfaces.py:103: in test_cli_trading_commands\n    assert result.returncode == 0\nE   assert 1 == 0\nE    +  where 1 = CompletedProcess(args=['C:\\\\Program Files\\\\Python313\\\\python.exe', 'cli.py', 'trading', '--help'], returncode=1, stdout='', stderr='  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\cli.py\", line 203\\n    def daily_import(ctx, date, source_dir, indices, async):\\n                                                     ^^^^^\\nSyntaxError: invalid syntax\\n').returncode\n__________________ TestCLIInterface.test_cli_system_commands __________________\ntests\\test_interfaces.py:114: in test_cli_system_commands\n    assert result.returncode == 0\nE   assert 1 == 0\nE    +  where 1 = CompletedProcess(args=['C:\\\\Program Files\\\\Python313\\\\python.exe', 'cli.py', 'system', '--help'], returncode=1, stdout='', stderr='  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\cli.py\", line 203\\n    def daily_import(ctx, date, source_dir, indices, async):\\n                                                     ^^^^^\\nSyntaxError: invalid syntax\\n').returncode\n___________________ TestAPIEndpoints.test_analysis_endpoint ___________________\ntests\\test_interfaces.py:185: in test_analysis_endpoint\n    response = api_app.get('/api/analysis/nifty?date=2025-07-14')\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\werkzeug\\test.py:1162: in get\n    return self.open(*args, **kw)\n           ^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask\\testing.py:235: in open\n    response = super().open(\nC:\\Program Files\\Python313\\Lib\\site-packages\\werkzeug\\test.py:1116: in open\n    response_parts = self.run_wsgi_app(request.environ, buffered=buffered)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\werkzeug\\test.py:988: in run_wsgi_app\n    rv = run_wsgi_app(self.application, environ, buffered=buffered)\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\werkzeug\\test.py:1264: in run_wsgi_app\n    app_rv = app(environ, start_response)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask\\app.py:1536: in __call__\n    return self.wsgi_app(environ, start_response)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask\\app.py:1514: in wsgi_app\n    response = self.handle_exception(e)\n               ^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask_restx\\api.py:671: in error_router\n    return original_handler(f)\n           ^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask_cors\\extension.py:176: in wrapped_function\n    return cors_after_request(app.make_response(f(*args, **kwargs)))\n                                                ^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask_restx\\api.py:669: in error_router\n    return self.handle_error(e)\n           ^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask\\app.py:1511: in wsgi_app\n    response = self.full_dispatch_request()\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask\\app.py:919: in full_dispatch_request\n    rv = self.handle_user_exception(e)\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask_restx\\api.py:671: in error_router\n    return original_handler(f)\n           ^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask_cors\\extension.py:176: in wrapped_function\n    return cors_after_request(app.make_response(f(*args, **kwargs)))\n                                                ^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask_restx\\api.py:669: in error_router\n    return self.handle_error(e)\n           ^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask\\app.py:917: in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask\\app.py:902: in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask_restx\\api.py:406: in wrapper\n    return self.make_response(data, code, headers=headers)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask_restx\\api.py:430: in make_response\n    resp = self.representations[mediatype](data, *args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\flask_restx\\representations.py:22: in output_json\n    dumped = dumps(data, **settings) + \"\\n\"\n             ^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\json\\__init__.py:231: in dumps\n    return _default_encoder.encode(obj)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\json\\encoder.py:200: in encode\n    chunks = self.iterencode(o, _one_shot=True)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\json\\encoder.py:261: in iterencode\n    return _iterencode(o, 0)\n           ^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\json\\encoder.py:180: in default\n    raise TypeError(f'Object of type {o.__class__.__name__} '\nE   TypeError: Object of type int64 is not JSON serializable\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:06,951 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:07,019 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:07,019 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:07,041 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:07,041 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 100 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 100 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:07,054 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n_________________ TestAPIEndpoints.test_data_export_endpoint __________________\ntests\\test_interfaces.py:214: in test_data_export_endpoint\n    assert response.status_code in [200, 404]\nE   assert 500 in [200, 404]\nE    +  where 500 = <WrapperTestResponse streamed [500 INTERNAL SERVER ERROR]>.status_code\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:07,724 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:07,794 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:07,794 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:07,815 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:07,815 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 100 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 100 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:07,829 - src.api.endpoints - \u001b[31mERROR\u001b[0m - Export data error: TypeConversionDict.get() got an unexpected keyword argument 'required'\n------------------------------ Captured log call ------------------------------\nERROR    src.api.endpoints:endpoints.py:245 Export data error: TypeConversionDict.get() got an unexpected keyword argument 'required'\n__________________ TestAPIEndpoints.test_api_error_handling ___________________\ntests\\test_interfaces.py:236: in test_api_error_handling\n    assert response.status_code == 404\nE   assert 500 == 404\nE    +  where 500 = <WrapperTestResponse streamed [500 INTERNAL SERVER ERROR]>.status_code\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:08,110 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:08,197 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:08,197 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:08,222 - performance - \u001b[32mINFO\u001b[0m - Operation completed: store_tick_data\n2025-07-17 16:10:08,222 - src.data.storage - \u001b[32mINFO\u001b[0m - Stored 100 tick data records\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nINFO     performance:logging.py:219 Operation completed: store_tick_data\nINFO     src.data.storage:storage.py:212 Stored 100 tick data records\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:08,241 - src.strategy.professional_trader - \u001b[32mINFO\u001b[0m - Analyzing market structure for invalid_index on 2025-07-14\n2025-07-17 16:10:08,244 - src.api.endpoints - \u001b[31mERROR\u001b[0m - Market analysis error: Insufficient data: 0 ticks\n------------------------------ Captured log call ------------------------------\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for invalid_index on 2025-07-14\nERROR    src.api.endpoints:endpoints.py:275 Market analysis error: Insufficient data: 0 ticks\n______________ TestIntegrationWorkflows.test_cli_to_api_workflow ______________\ntests\\test_interfaces.py:360: in test_cli_to_api_workflow\n    api_app = create_api_app(config)\n              ^^^^^^^^^^^^^^^^^^^^^^\nsrc\\api\\endpoints.py:38: in create_api_app\n    storage = TickDataStorage(config.database.connection_string)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nsrc\\data\\storage.py:32: in __init__\n    self.engine = create_engine(\n<string>:2: in create_engine\n    ???\nC:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py:281: in warned\n    return fn(*args, **kwargs)  # type: ignore[no-any-return]\n           ^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\engine\\create.py:602: in create_engine\n    dbapi = dbapi_meth(**dbapi_args)\n            ^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py:696: in import_dbapi\n    import psycopg2\nE   ModuleNotFoundError: No module named 'psycopg2'\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:15,296 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:15,379 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:15,380 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n2025-07-17 16:10:15,537 - errors - \u001b[31mERROR\u001b[0m - Exception in database_initialization: No module named 'psycopg2'\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\data\\storage.py\", line 32, in __init__\n    self.engine = create_engine(\n                  ~~~~~~~~~~~~~^\n        db_connection_string,\n        ^^^^^^^^^^^^^^^^^^^^^\n    ...<5 lines>...\n        echo=False  # Set to True for SQL debugging\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"<string>\", line 2, in create_engine\n  File \"C:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py\", line 281, in warned\n    return fn(*args, **kwargs)  # type: ignore[no-any-return]\n  File \"C:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\engine\\create.py\", line 602, in create_engine\n    dbapi = dbapi_meth(**dbapi_args)\n  File \"C:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py\", line 696, in import_dbapi\n    import psycopg2\nModuleNotFoundError: No module named 'psycopg2'\n------------------------------ Captured log call ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\nERROR    errors:logging.py:267 Exception in database_initialization: No module named 'psycopg2'\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\data\\storage.py\", line 32, in __init__\n    self.engine = create_engine(\n                  ~~~~~~~~~~~~~^\n        db_connection_string,\n        ^^^^^^^^^^^^^^^^^^^^^\n    ...<5 lines>...\n        echo=False  # Set to True for SQL debugging\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"<string>\", line 2, in create_engine\n  File \"C:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py\", line 281, in warned\n    return fn(*args, **kwargs)  # type: ignore[no-any-return]\n  File \"C:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\engine\\create.py\", line 602, in create_engine\n    dbapi = dbapi_meth(**dbapi_args)\n  File \"C:\\Program Files\\Python313\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py\", line 696, in import_dbapi\n    import psycopg2\nModuleNotFoundError: No module named 'psycopg2'\n============================== warnings summary ===============================\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n  C:\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323: PydanticDeprecatedSince20: Support for class-based `config` is deprecated, use ConfigDict instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n    warnings.warn(DEPRECATION_MESSAGE, DeprecationWarning)\n\nconfig.py:107\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\config.py:107: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n    @validator('data_root', 'sample_data_path', 'models_path', 'logs_path')\n\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\flask_restx\\api.py:19\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\flask_restx\\api.py:19\n  C:\\Program Files\\Python313\\Lib\\site-packages\\flask_restx\\api.py:19: DeprecationWarning: jsonschema.RefResolver is deprecated as of v4.18.0, in favor of the https://github.com/python-jsonschema/referencing library, which provides more compliant referencing behavior as well as more flexible APIs for customization. A future release will remove RefResolver. Please file a feature request (on referencing) if you are missing an API for the kind of customization you need.\n    from jsonschema import RefResolver\n\ntests/test_interfaces.py::TestAPIEndpoints::test_health_endpoint\ntests/test_interfaces.py::TestAPIEndpoints::test_system_status_endpoint\ntests/test_interfaces.py::TestAPIEndpoints::test_analysis_endpoint\ntests/test_interfaces.py::TestAPIEndpoints::test_signals_endpoint\ntests/test_interfaces.py::TestAPIEndpoints::test_data_export_endpoint\ntests/test_interfaces.py::TestAPIEndpoints::test_import_jobs_endpoint\ntests/test_interfaces.py::TestAPIEndpoints::test_api_error_handling\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\tests\\test_interfaces.py:138: FutureWarning: 'S' is deprecated and will be removed in a future version, please use 's' instead.\n    'timestamp': pd.date_range('2025-07-14 09:15:00', periods=100, freq='2S'),\n\ntests/test_interfaces.py::TestAPIEndpoints::test_analysis_endpoint\ntests/test_interfaces.py::TestAPIEndpoints::test_signals_endpoint\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\strategy\\professional_trader.py:355: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n    '1min': tick_data.resample('1T').agg({\n\ntests/test_interfaces.py::TestAPIEndpoints::test_analysis_endpoint\ntests/test_interfaces.py::TestAPIEndpoints::test_signals_endpoint\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\strategy\\professional_trader.py:359: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n    '5min': tick_data.resample('5T').agg({\n\ntests/test_interfaces.py::TestAPIEndpoints::test_analysis_endpoint\ntests/test_interfaces.py::TestAPIEndpoints::test_signals_endpoint\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\strategy\\professional_trader.py:363: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n    '15min': tick_data.resample('15T').agg({\n\n-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html\n=========================== short test summary info ===========================\nFAILED tests/test_interfaces.py::TestCLIInterface::test_cli_help_command - as...\nFAILED tests/test_interfaces.py::TestCLIInterface::test_cli_db_commands - ass...\nFAILED tests/test_interfaces.py::TestCLIInterface::test_cli_data_commands - a...\nFAILED tests/test_interfaces.py::TestCLIInterface::test_cli_trading_commands\nFAILED tests/test_interfaces.py::TestCLIInterface::test_cli_system_commands\nFAILED tests/test_interfaces.py::TestAPIEndpoints::test_analysis_endpoint - T...\nFAILED tests/test_interfaces.py::TestAPIEndpoints::test_data_export_endpoint\nFAILED tests/test_interfaces.py::TestAPIEndpoints::test_api_error_handling - ...\nFAILED tests/test_interfaces.py::TestIntegrationWorkflows::test_cli_to_api_workflow\nERROR tests/test_interfaces.py::TestAPIEndpoints::test_health_endpoint - Perm...\nERROR tests/test_interfaces.py::TestAPIEndpoints::test_system_status_endpoint\nERROR tests/test_interfaces.py::TestAPIEndpoints::test_analysis_endpoint - Pe...\nERROR tests/test_interfaces.py::TestAPIEndpoints::test_signals_endpoint - Per...\nERROR tests/test_interfaces.py::TestAPIEndpoints::test_data_export_endpoint\nERROR tests/test_interfaces.py::TestAPIEndpoints::test_import_jobs_endpoint\nERROR tests/test_interfaces.py::TestAPIEndpoints::test_api_error_handling - P...\nERROR tests/test_interfaces.py::TestWebDashboard::test_dashboard_main_page - ...\nERROR tests/test_interfaces.py::TestWebDashboard::test_dashboard_api_status\nERROR tests/test_interfaces.py::TestWebDashboard::test_dashboard_import_jobs\nERROR tests/test_interfaces.py::TestWebDashboard::test_dashboard_latest_data\nERROR tests/test_interfaces.py::TestIntegrationWorkflows::test_cli_to_api_workflow\n============ 9 failed, 11 passed, 22 warnings, 12 errors in 11.47s ============\n", "stderr": ""}}, "integration_tests": {"returncode": 1, "passed": false, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.13.3, pytest-8.4.1, pluggy-1.6.0 -- C:\\Program Files\\Python313\\python.exe\ncachedir: .pytest_cache\nrootdir: C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\ncollecting ... collected 9 items\n\ntests/test_integration.py::TestCompleteWorkflows::test_complete_daily_workflow FAILED [ 11%]\ntests/test_integration.py::TestCompleteWorkflows::test_complete_daily_workflow ERROR [ 11%]\ntests/test_integration.py::TestCompleteWorkflows::test_bulk_import_and_analysis_workflow FAILED [ 22%]\ntests/test_integration.py::TestCompleteWorkflows::test_bulk_import_and_analysis_workflow ERROR [ 22%]\ntests/test_integration.py::TestCompleteWorkflows::test_error_recovery_workflow FAILED [ 33%]\ntests/test_integration.py::TestCompleteWorkflows::test_error_recovery_workflow ERROR [ 33%]\ntests/test_integration.py::TestCompleteWorkflows::test_performance_workflow PASSED [ 44%]\ntests/test_integration.py::TestCompleteWorkflows::test_performance_workflow ERROR [ 44%]\ntests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations PASSED [ 55%]\ntests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations ERROR [ 55%]\ntests/test_integration.py::TestCompleteWorkflows::test_data_consistency_workflow PASSED [ 66%]\ntests/test_integration.py::TestCompleteWorkflows::test_data_consistency_workflow ERROR [ 66%]\ntests/test_integration.py::TestSystemIntegration::test_configuration_consistency PASSED [ 77%]\ntests/test_integration.py::TestSystemIntegration::test_logging_integration PASSED [ 88%]\ntests/test_integration.py::TestSystemIntegration::test_database_schema_consistency PASSED [100%]\n\n=================================== ERRORS ====================================\n___ ERROR at teardown of TestCompleteWorkflows.test_complete_daily_workflow ___\ntests\\test_integration.py:36: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp0i9vz206\\\\integration_test.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:18,155 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:18,230 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:18,230 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:18,434 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp0i9vz206: found 15 data files\\n2025-07-17 16:10:18,444 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Created daily import job daily_2025-07-14_161018 for 2025-07-14 with 2 files\\n2025-07-17 16:10:18,444 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Starting job daily_2025-07-14_161018 (daily)\\n2025-07-17 16:10:18,454 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,454 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,455 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,456 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,499 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 500 records\\n2025-07-17 16:10:18,500 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for bank_nifty: 500 records\\n2025-07-17 16:10:18,605 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:18,605 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:18,606 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,606 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,606 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:18,618 - performance - \\x1b[33mWARNING\\x1b[0m - No start time found for operation: store_tick_data\\n2025-07-17 16:10:18,618 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:18,619 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,619 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,619 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:18,619 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Job daily_2025-07-14_161018 completed: 2/2 successful\\n2025-07-17 16:10:18,630 - errors - \\x1b[31mERROR\\x1b[0m - Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp0i9vz206: found 15 data files\\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_161018 for 2025-07-14 with 2 files\\nINFO     src.data.import_manager:import_manager.py:309 Starting job daily_2025-07-14_161018 (daily)\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 500 records\\nINFO     src.data.validation:validation.py:88 Data validation passed for bank_nifty: 500 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nWARNING  performance:logging.py:213 No start time found for operation: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:353 Job daily_2025-07-14_161018 completed: 2/2 successful\\nERROR    errors:logging.py:267 Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n_ ERROR at teardown of TestCompleteWorkflows.test_bulk_import_and_analysis_workflow _\ntests\\test_integration.py:36: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp5gy3409u\\\\integration_test.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:18,964 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:19,049 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:19,049 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:19,249 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp5gy3409u: found 15 data files\\n2025-07-17 16:10:19,260 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Created bulk import job bulk_20250717_161019 with 15 files\\n2025-07-17 16:10:19,260 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Starting job bulk_20250717_161019 (bulk)\\n2025-07-17 16:10:19,271 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Cleaning database before import...\\n2025-07-17 16:10:19,281 - src.data.storage - \\x1b[32mINFO\\x1b[0m - All data cleared from database\\n2025-07-17 16:10:19,284 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,284 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,284 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,285 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,285 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,285 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,285 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,286 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,342 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for bank_nifty: 500 records\\n2025-07-17 16:10:19,344 - src.data.ingestion - \\x1b[33mWARNING\\x1b[0m - Data validation warnings for Bank Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\n2025-07-17 16:10:19,370 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for midcap_nifty: 500 records\\n2025-07-17 16:10:19,374 - src.data.ingestion - \\x1b[33mWARNING\\x1b[0m - Data validation warnings for Midcap Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\n2025-07-17 16:10:19,389 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 500 records\\n2025-07-17 16:10:19,389 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for fin_nifty: 500 records\\n2025-07-17 16:10:19,417 - src.data.ingestion - \\x1b[33mWARNING\\x1b[0m - Data validation warnings for Fin Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\n2025-07-17 16:10:19,416 - src.data.ingestion - \\x1b[33mWARNING\\x1b[0m - Data validation warnings for Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\n2025-07-17 16:10:19,530 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:19,531 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,546 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,546 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,547 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,547 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:19,547 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:19,558 - performance - \\x1b[33mWARNING\\x1b[0m - No start time found for operation: store_tick_data\\n2025-07-17 16:10:19,558 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,561 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,562 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,572 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,572 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,573 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,573 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty_next: 500 records\\n2025-07-17 16:10:19,574 - src.data.ingestion - \\x1b[33mWARNING\\x1b[0m - Data validation warnings for Nifty Nxt Ticklist ********.csv: ['Weekend data found: 500 records']\\n2025-07-17 16:10:19,581 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:19,581 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,581 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,581 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,582 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Fin Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,582 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,582 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,601 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for bank_nifty: 500 records\\n2025-07-17 16:10:19,670 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:19,686 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,688 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,688 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,688 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Midcap Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,688 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,688 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,697 - performance - \\x1b[33mWARNING\\x1b[0m - No start time found for operation: store_tick_data\\n2025-07-17 16:10:19,697 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,709 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:19,710 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for fin_nifty: 500 records\\n2025-07-17 16:10:19,711 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:19,714 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Nxt Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,714 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,714 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,720 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:19,733 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,738 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,738 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for midcap_nifty: 500 records\\n2025-07-17 16:10:19,738 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,742 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,748 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:19,764 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:19,811 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:19,822 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,827 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,829 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,833 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Fin Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,834 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,841 - performance - \\x1b[33mWARNING\\x1b[0m - No start time found for operation: store_tick_data\\n2025-07-17 16:10:19,841 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,844 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,845 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 500 records\\n2025-07-17 16:10:19,846 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,849 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,850 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Midcap Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,851 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,851 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,916 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:19,920 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,933 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,933 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,936 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,940 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,940 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,942 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for bank_nifty: 500 records\\n2025-07-17 16:10:19,945 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty_next: 500 records\\n2025-07-17 16:10:20,014 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for fin_nifty: 500 records\\n2025-07-17 16:10:20,029 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:20,052 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:20,059 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,075 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,092 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:20,096 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,096 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:20,096 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,097 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:20,097 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,097 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:20,098 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Nxt Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,098 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:20,098 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:20,109 - performance - \\x1b[33mWARNING\\x1b[0m - No start time found for operation: store_tick_data\\n2025-07-17 16:10:20,109 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:20,117 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,118 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,120 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Fin Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,122 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for midcap_nifty: 500 records\\n2025-07-17 16:10:20,194 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty_next: 500 records\\n2025-07-17 16:10:20,201 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:20,211 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:20,212 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 500 records\\n2025-07-17 16:10:20,212 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,248 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,279 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Midcap Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,303 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:20,304 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:20,304 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,304 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,304 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,324 - performance - \\x1b[33mWARNING\\x1b[0m - No start time found for operation: store_tick_data\\n2025-07-17 16:10:20,324 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:20,325 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:20,325 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:20,325 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Nxt Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,326 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Job bulk_20250717_161019 completed: 15/15 successful\\n2025-07-17 16:10:20,339 - errors - \\x1b[31mERROR\\x1b[0m - Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp5gy3409u: found 15 data files\\nINFO     src.data.import_manager:import_manager.py:285 Created bulk import job bulk_20250717_161019 with 15 files\\nINFO     src.data.import_manager:import_manager.py:309 Starting job bulk_20250717_161019 (bulk)\\nINFO     src.data.import_manager:import_manager.py:318 Cleaning database before import...\\nINFO     src.data.storage:storage.py:507 All data cleared from database\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Fin Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Midcap Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Fin Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Midcap Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for bank_nifty: 500 records\\nWARNING  src.data.ingestion:ingestion.py:80 Data validation warnings for Bank Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\nINFO     src.data.validation:validation.py:88 Data validation passed for midcap_nifty: 500 records\\nWARNING  src.data.ingestion:ingestion.py:80 Data validation warnings for Midcap Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 500 records\\nINFO     src.data.validation:validation.py:88 Data validation passed for fin_nifty: 500 records\\nWARNING  src.data.ingestion:ingestion.py:80 Data validation warnings for Fin Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\nWARNING  src.data.ingestion:ingestion.py:80 Data validation warnings for Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Nxt Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Nxt Ticklist ********.csv\\nWARNING  performance:logging.py:213 No start time found for operation: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Bank Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty_next: 500 records\\nWARNING  src.data.ingestion:ingestion.py:80 Data validation warnings for Nifty Nxt Ticklist ********.csv: ['Weekend data found: 500 records']\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Fin Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Fin Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Fin Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Fin Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Fin Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for bank_nifty: 500 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Midcap Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Midcap Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Midcap Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Midcap Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Midcap Nifty Ticklist ********.csv\\nWARNING  performance:logging.py:213 No start time found for operation: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Nxt Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for fin_nifty: 500 records\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Nxt Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Nxt Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Bank Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for midcap_nifty: 500 records\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Nxt Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Nxt Ticklist ********.csv\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Fin Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Fin Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Fin Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Bank Nifty Ticklist ********.csv\\nWARNING  performance:logging.py:213 No start time found for operation: store_tick_data\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Bank Nifty Ticklist ********.csv\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 500 records\\nINFO     performance:logging.py:219 Operation completed: process_file_Midcap Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Midcap Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Midcap Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Fin Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Fin Nifty Ticklist ********.csv\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Midcap Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Midcap Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for bank_nifty: 500 records\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty_next: 500 records\\nINFO     src.data.validation:validation.py:88 Data validation passed for fin_nifty: 500 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Nxt Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Nxt Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Nxt Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Nxt Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Nxt Ticklist ********.csv\\nWARNING  performance:logging.py:213 No start time found for operation: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Fin Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Fin Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Fin Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.validation:validation.py:88 Data validation passed for midcap_nifty: 500 records\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty_next: 500 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 500 records\\nINFO     performance:logging.py:219 Operation completed: process_file_Midcap Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Midcap Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Midcap Nifty Ticklist ********.csv imported successfully\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nWARNING  performance:logging.py:213 No start time found for operation: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Nxt Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Nxt Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Nxt Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:353 Job bulk_20250717_161019 completed: 15/15 successful\\nERROR    errors:logging.py:267 Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n___ ERROR at teardown of TestCompleteWorkflows.test_error_recovery_workflow ___\ntests\\test_integration.py:36: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmppm3znsn6\\\\integration_test.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:20,464 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:20,563 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:20,564 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:20,573 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmppm3znsn6: found 2 data files\\n2025-07-17 16:10:20,585 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Created daily import job daily_2025-07-14_161020 for 2025-07-14 with 2 files\\n2025-07-17 16:10:20,585 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Starting job daily_2025-07-14_161020 (daily)\\n2025-07-17 16:10:20,598 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,598 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,598 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,599 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,604 - src.data.ingestion - \\x1b[31mERROR\\x1b[0m - File validation failed for Bank Nifty Ticklist ********.csv: [\"Missing expected columns: {'Time', 'Volume', 'Last Rate'}\"]\\n2025-07-17 16:10:20,604 - src.data.import_manager - \\x1b[31mERROR\\x1b[0m - \\u274c Bank Nifty Ticklist ********.csv import failed\\n2025-07-17 16:10:20,613 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 2 records\\n2025-07-17 16:10:20,626 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:20,626 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 2 tick data records\\n2025-07-17 16:10:20,627 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,627 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 2 records from Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,627 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,627 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Job daily_2025-07-14_161020 completed: 1/2 successful\\n2025-07-17 16:10:20,641 - errors - \\x1b[31mERROR\\x1b[0m - Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmppm3znsn6: found 2 data files\\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_161020 for 2025-07-14 with 2 files\\nINFO     src.data.import_manager:import_manager.py:309 Starting job daily_2025-07-14_161020 (daily)\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nERROR    src.data.ingestion:ingestion.py:60 File validation failed for Bank Nifty Ticklist ********.csv: [\"Missing expected columns: {'Time', 'Volume', 'Last Rate'}\"]\\nERROR    src.data.import_manager:import_manager.py:376 \\u274c Bank Nifty Ticklist ********.csv import failed\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 2 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 2 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 2 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:353 Job daily_2025-07-14_161020 completed: 1/2 successful\\nERROR    errors:logging.py:267 Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n____ ERROR at teardown of TestCompleteWorkflows.test_performance_workflow _____\ntests\\test_integration.py:36: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpmu71v88u\\\\integration_test.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:20,715 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:20,790 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:20,790 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:20,814 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpmu71v88u: found 1 data files\\n2025-07-17 16:10:20,825 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Created daily import job daily_2025-07-14_161020 for 2025-07-14 with 1 files\\n2025-07-17 16:10:20,825 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Starting job daily_2025-07-14_161020 (daily)\\n2025-07-17 16:10:20,838 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,838 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,890 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 2000 records\\n2025-07-17 16:10:21,141 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:21,141 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 2000 tick data records\\n2025-07-17 16:10:21,142 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:10:21,143 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 2000 records from Nifty Ticklist ********.csv\\n2025-07-17 16:10:21,143 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:21,143 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Job daily_2025-07-14_161020 completed: 1/1 successful\\n2025-07-17 16:10:21,157 - src.strategy.professional_trader - \\x1b[32mINFO\\x1b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpmu71v88u: found 1 data files\\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_161020 for 2025-07-14 with 1 files\\nINFO     src.data.import_manager:import_manager.py:309 Starting job daily_2025-07-14_161020 (daily)\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 2000 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 2000 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 2000 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:353 Job daily_2025-07-14_161020 completed: 1/1 successful\\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n____ ERROR at teardown of TestCompleteWorkflows.test_concurrent_operations ____\ntests\\test_integration.py:36: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp1_m6lz9_\\\\integration_test.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:21,385 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:21,465 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:21,465 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:21,689 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp1_m6lz9_: found 15 data files\\n2025-07-17 16:10:21,701 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Created daily import job daily_2025-07-14_161021 for 2025-07-14 with 1 files\\n2025-07-17 16:10:21,701 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Starting job daily_2025-07-14_161021 (daily)\\n2025-07-17 16:10:21,715 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:10:21,715 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:10:21,737 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 500 records\\n2025-07-17 16:10:21,795 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:21,795 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:21,795 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:10:21,795 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Ticklist ********.csv\\n2025-07-17 16:10:21,795 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:21,796 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Job daily_2025-07-14_161021 completed: 1/1 successful\\n2025-07-17 16:10:21,808 - src.strategy.professional_trader - \\x1b[32mINFO\\x1b[0m - Analyzing market structure for nifty on 2025-07-14\\n2025-07-17 16:10:21,854 - src.strategy.professional_trader - \\x1b[32mINFO\\x1b[0m - Analyzing market structure for nifty on 2025-07-14\\n2025-07-17 16:10:21,903 - src.strategy.professional_trader - \\x1b[32mINFO\\x1b[0m - Analyzing market structure for nifty on 2025-07-14\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp1_m6lz9_: found 15 data files\\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_161021 for 2025-07-14 with 1 files\\nINFO     src.data.import_manager:import_manager.py:309 Starting job daily_2025-07-14_161021 (daily)\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 500 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:353 Job daily_2025-07-14_161021 completed: 1/1 successful\\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\n__ ERROR at teardown of TestCompleteWorkflows.test_data_consistency_workflow __\ntests\\test_integration.py:36: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Program Files\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Program Files\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Program Files\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp7h88_653\\\\integration_test.db'\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:22,015 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:22,092 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:22,093 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:22,301 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp7h88_653: found 15 data files\\n2025-07-17 16:10:22,315 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Created daily import job daily_2025-07-14_161022 for 2025-07-14 with 1 files\\n2025-07-17 16:10:22,315 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Starting job daily_2025-07-14_161022 (daily)\\n2025-07-17 16:10:22,329 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:10:22,329 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:10:22,351 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 500 records\\n2025-07-17 16:10:22,414 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:22,414 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:22,414 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:10:22,415 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Ticklist ********.csv\\n2025-07-17 16:10:22,415 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:22,415 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Job daily_2025-07-14_161022 completed: 1/1 successful\\n2025-07-17 16:10:22,430 - errors - \\x1b[31mERROR\\x1b[0m - Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\\n2025-07-17 16:10:22,431 - src.strategy.professional_trader - \\x1b[32mINFO\\x1b[0m - Analyzing market structure for nifty on 2025-07-14\\n2025-07-17 16:10:22,474 - errors - \\x1b[31mERROR\\x1b[0m - Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\\n2025-07-17 16:10:22,519 - errors - \\x1b[31mERROR\\x1b[0m - Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp7h88_653: found 15 data files\\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_161022 for 2025-07-14 with 1 files\\nINFO     src.data.import_manager:import_manager.py:309 Starting job daily_2025-07-14_161022 (daily)\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 500 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:353 Job daily_2025-07-14_161022 completed: 1/1 successful\\nERROR    errors:logging.py:267 Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\\nINFO     src.strategy.professional_trader:professional_trader.py:45 Analyzing market structure for nifty on 2025-07-14\\nERROR    errors:logging.py:267 Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\\nERROR    errors:logging.py:267 Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n================================== FAILURES ===================================\n_____________ TestCompleteWorkflows.test_complete_daily_workflow ______________\ntests\\test_integration.py:129: in test_complete_daily_workflow\n    assert stats['total_records'] > 0\nE   assert 0 > 0\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:18,155 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:18,230 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:18,230 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:18,434 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp0i9vz206: found 15 data files\\n2025-07-17 16:10:18,444 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Created daily import job daily_2025-07-14_161018 for 2025-07-14 with 2 files\\n2025-07-17 16:10:18,444 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Starting job daily_2025-07-14_161018 (daily)\\n2025-07-17 16:10:18,454 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,454 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,455 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,456 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,499 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 500 records\\n2025-07-17 16:10:18,500 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for bank_nifty: 500 records\\n2025-07-17 16:10:18,605 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:18,605 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:18,606 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,606 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,606 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:18,618 - performance - \\x1b[33mWARNING\\x1b[0m - No start time found for operation: store_tick_data\\n2025-07-17 16:10:18,618 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:18,619 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,619 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:18,619 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:18,619 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Job daily_2025-07-14_161018 completed: 2/2 successful\\n2025-07-17 16:10:18,630 - errors - \\x1b[31mERROR\\x1b[0m - Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp0i9vz206: found 15 data files\\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_161018 for 2025-07-14 with 2 files\\nINFO     src.data.import_manager:import_manager.py:309 Starting job daily_2025-07-14_161018 (daily)\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 500 records\\nINFO     src.data.validation:validation.py:88 Data validation passed for bank_nifty: 500 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nWARNING  performance:logging.py:213 No start time found for operation: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:353 Job daily_2025-07-14_161018 completed: 2/2 successful\\nERROR    errors:logging.py:267 Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n________ TestCompleteWorkflows.test_bulk_import_and_analysis_workflow _________\ntests\\test_integration.py:180: in test_bulk_import_and_analysis_workflow\n    assert stats['total_records'] > 1000  # Should have substantial data\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   assert 0 > 1000\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:18,964 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:19,049 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:19,049 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:19,249 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp5gy3409u: found 15 data files\\n2025-07-17 16:10:19,260 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Created bulk import job bulk_20250717_161019 with 15 files\\n2025-07-17 16:10:19,260 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Starting job bulk_20250717_161019 (bulk)\\n2025-07-17 16:10:19,271 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Cleaning database before import...\\n2025-07-17 16:10:19,281 - src.data.storage - \\x1b[32mINFO\\x1b[0m - All data cleared from database\\n2025-07-17 16:10:19,284 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,284 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,284 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,285 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,285 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,285 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,285 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,286 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,342 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for bank_nifty: 500 records\\n2025-07-17 16:10:19,344 - src.data.ingestion - \\x1b[33mWARNING\\x1b[0m - Data validation warnings for Bank Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\n2025-07-17 16:10:19,370 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for midcap_nifty: 500 records\\n2025-07-17 16:10:19,374 - src.data.ingestion - \\x1b[33mWARNING\\x1b[0m - Data validation warnings for Midcap Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\n2025-07-17 16:10:19,389 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 500 records\\n2025-07-17 16:10:19,389 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for fin_nifty: 500 records\\n2025-07-17 16:10:19,417 - src.data.ingestion - \\x1b[33mWARNING\\x1b[0m - Data validation warnings for Fin Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\n2025-07-17 16:10:19,416 - src.data.ingestion - \\x1b[33mWARNING\\x1b[0m - Data validation warnings for Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\n2025-07-17 16:10:19,530 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:19,531 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,546 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,546 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,547 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,547 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:19,547 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:19,558 - performance - \\x1b[33mWARNING\\x1b[0m - No start time found for operation: store_tick_data\\n2025-07-17 16:10:19,558 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,561 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,562 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,572 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,572 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,573 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,573 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty_next: 500 records\\n2025-07-17 16:10:19,574 - src.data.ingestion - \\x1b[33mWARNING\\x1b[0m - Data validation warnings for Nifty Nxt Ticklist ********.csv: ['Weekend data found: 500 records']\\n2025-07-17 16:10:19,581 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:19,581 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,581 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,581 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,582 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Fin Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,582 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,582 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,601 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for bank_nifty: 500 records\\n2025-07-17 16:10:19,670 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:19,686 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,688 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,688 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,688 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Midcap Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,688 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,688 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,697 - performance - \\x1b[33mWARNING\\x1b[0m - No start time found for operation: store_tick_data\\n2025-07-17 16:10:19,697 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,709 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:19,710 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for fin_nifty: 500 records\\n2025-07-17 16:10:19,711 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:19,714 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Nxt Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,714 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,714 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,720 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:19,733 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,738 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,738 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for midcap_nifty: 500 records\\n2025-07-17 16:10:19,738 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,742 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,748 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:19,764 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:19,811 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:19,822 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,827 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,829 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,833 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Fin Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,834 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,841 - performance - \\x1b[33mWARNING\\x1b[0m - No start time found for operation: store_tick_data\\n2025-07-17 16:10:19,841 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,844 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,845 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 500 records\\n2025-07-17 16:10:19,846 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,849 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,850 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Midcap Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,851 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,851 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,916 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:19,920 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:19,933 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,933 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,936 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:19,940 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,940 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:19,942 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for bank_nifty: 500 records\\n2025-07-17 16:10:19,945 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty_next: 500 records\\n2025-07-17 16:10:20,014 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for fin_nifty: 500 records\\n2025-07-17 16:10:20,029 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:20,052 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:20,059 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,075 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,092 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:20,096 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,096 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:20,096 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,097 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:20,097 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,097 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:20,098 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Nxt Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,098 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:20,098 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:20,109 - performance - \\x1b[33mWARNING\\x1b[0m - No start time found for operation: store_tick_data\\n2025-07-17 16:10:20,109 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:20,117 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,118 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Fin Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,120 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Fin Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,122 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for midcap_nifty: 500 records\\n2025-07-17 16:10:20,194 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty_next: 500 records\\n2025-07-17 16:10:20,201 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:20,211 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:20,212 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 500 records\\n2025-07-17 16:10:20,212 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,248 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Midcap Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,279 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Midcap Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,303 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:20,304 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:20,304 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,304 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,304 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,324 - performance - \\x1b[33mWARNING\\x1b[0m - No start time found for operation: store_tick_data\\n2025-07-17 16:10:20,324 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 500 tick data records\\n2025-07-17 16:10:20,325 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:20,325 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 500 records from Nifty Nxt Ticklist ********.csv\\n2025-07-17 16:10:20,325 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Nxt Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,326 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Job bulk_20250717_161019 completed: 15/15 successful\\n2025-07-17 16:10:20,339 - errors - \\x1b[31mERROR\\x1b[0m - Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp5gy3409u: found 15 data files\\nINFO     src.data.import_manager:import_manager.py:285 Created bulk import job bulk_20250717_161019 with 15 files\\nINFO     src.data.import_manager:import_manager.py:309 Starting job bulk_20250717_161019 (bulk)\\nINFO     src.data.import_manager:import_manager.py:318 Cleaning database before import...\\nINFO     src.data.storage:storage.py:507 All data cleared from database\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Fin Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Midcap Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Fin Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Midcap Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for bank_nifty: 500 records\\nWARNING  src.data.ingestion:ingestion.py:80 Data validation warnings for Bank Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\nINFO     src.data.validation:validation.py:88 Data validation passed for midcap_nifty: 500 records\\nWARNING  src.data.ingestion:ingestion.py:80 Data validation warnings for Midcap Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 500 records\\nINFO     src.data.validation:validation.py:88 Data validation passed for fin_nifty: 500 records\\nWARNING  src.data.ingestion:ingestion.py:80 Data validation warnings for Fin Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\nWARNING  src.data.ingestion:ingestion.py:80 Data validation warnings for Nifty Ticklist ********.csv: ['Weekend data found: 500 records']\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Nxt Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Nxt Ticklist ********.csv\\nWARNING  performance:logging.py:213 No start time found for operation: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Bank Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty_next: 500 records\\nWARNING  src.data.ingestion:ingestion.py:80 Data validation warnings for Nifty Nxt Ticklist ********.csv: ['Weekend data found: 500 records']\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Fin Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Fin Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Fin Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Fin Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Fin Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for bank_nifty: 500 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Midcap Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Midcap Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Midcap Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Midcap Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Midcap Nifty Ticklist ********.csv\\nWARNING  performance:logging.py:213 No start time found for operation: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Nxt Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for fin_nifty: 500 records\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Nxt Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Nxt Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Bank Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for midcap_nifty: 500 records\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Nxt Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Nxt Ticklist ********.csv\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Fin Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Fin Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Fin Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Bank Nifty Ticklist ********.csv\\nWARNING  performance:logging.py:213 No start time found for operation: store_tick_data\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Bank Nifty Ticklist ********.csv\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 500 records\\nINFO     performance:logging.py:219 Operation completed: process_file_Midcap Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Midcap Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Midcap Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Fin Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Fin Nifty Ticklist ********.csv\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Midcap Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Midcap Nifty Ticklist ********.csv\\nINFO     src.data.validation:validation.py:88 Data validation passed for bank_nifty: 500 records\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty_next: 500 records\\nINFO     src.data.validation:validation.py:88 Data validation passed for fin_nifty: 500 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Bank Nifty Ticklist ********.csv\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Bank Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Nxt Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Nxt Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Nxt Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Nxt Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Nxt Ticklist ********.csv\\nWARNING  performance:logging.py:213 No start time found for operation: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Fin Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Fin Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Fin Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.validation:validation.py:88 Data validation passed for midcap_nifty: 500 records\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty_next: 500 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 500 records\\nINFO     performance:logging.py:219 Operation completed: process_file_Midcap Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Midcap Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Midcap Nifty Ticklist ********.csv imported successfully\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nWARNING  performance:logging.py:213 No start time found for operation: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 500 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Nxt Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 500 records from Nifty Nxt Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Nxt Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:353 Job bulk_20250717_161019 completed: 15/15 successful\\nERROR    errors:logging.py:267 Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n_____________ TestCompleteWorkflows.test_error_recovery_workflow ______________\ntests\\test_integration.py:247: in test_error_recovery_workflow\n    assert stats['total_records'] > 0\nE   assert 0 > 0\n---------------------------- Captured stdout setup ----------------------------\n2025-07-17 16:10:20,464 - src.data.storage - \u001b[32mINFO\u001b[0m - Database connection initialized successfully\n2025-07-17 16:10:20,563 - src.data.storage - \u001b[32mINFO\u001b[0m - Database tables created successfully\n2025-07-17 16:10:20,564 - src.data.storage - \u001b[32mINFO\u001b[0m - Database initialized successfully\n----------------------------- Captured log setup ------------------------------\nINFO     src.data.storage:storage.py:44 Database connection initialized successfully\nINFO     src.data.storage:storage.py:163 Database tables created successfully\nINFO     src.data.storage:storage.py:53 Database initialized successfully\n---------------------------- Captured stdout call -----------------------------\n2025-07-17 16:10:20,573 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmppm3znsn6: found 2 data files\\n2025-07-17 16:10:20,585 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Created daily import job daily_2025-07-14_161020 for 2025-07-14 with 2 files\\n2025-07-17 16:10:20,585 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Starting job daily_2025-07-14_161020 (daily)\\n2025-07-17 16:10:20,598 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,598 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Bank Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,598 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Processing: Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,599 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Processing file: Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,604 - src.data.ingestion - \\x1b[31mERROR\\x1b[0m - File validation failed for Bank Nifty Ticklist ********.csv: [\"Missing expected columns: {'Time', 'Volume', 'Last Rate'}\"]\\n2025-07-17 16:10:20,604 - src.data.import_manager - \\x1b[31mERROR\\x1b[0m - \\u274c Bank Nifty Ticklist ********.csv import failed\\n2025-07-17 16:10:20,613 - src.data.validation - \\x1b[32mINFO\\x1b[0m - Data validation passed for nifty: 2 records\\n2025-07-17 16:10:20,626 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: store_tick_data\\n2025-07-17 16:10:20,626 - src.data.storage - \\x1b[32mINFO\\x1b[0m - Stored 2 tick data records\\n2025-07-17 16:10:20,627 - performance - \\x1b[32mINFO\\x1b[0m - Operation completed: process_file_Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,627 - src.data.ingestion - \\x1b[32mINFO\\x1b[0m - Successfully processed 2 records from Nifty Ticklist ********.csv\\n2025-07-17 16:10:20,627 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - \\u2705 Nifty Ticklist ********.csv imported successfully\\n2025-07-17 16:10:20,627 - src.data.import_manager - \\x1b[32mINFO\\x1b[0m - Job daily_2025-07-14_161020 completed: 1/2 successful\\n2025-07-17 16:10:20,641 - errors - \\x1b[31mERROR\\x1b[0m - Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n------------------------------ Captured log call ------------------------------\nINFO     src.data.import_manager:import_manager.py:179 Scanned C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmppm3znsn6: found 2 data files\\nINFO     src.data.import_manager:import_manager.py:226 Created daily import job daily_2025-07-14_161020 for 2025-07-14 with 2 files\\nINFO     src.data.import_manager:import_manager.py:309 Starting job daily_2025-07-14_161020 (daily)\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Bank Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Bank Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:369 Processing: Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:54 Processing file: Nifty Ticklist ********.csv\\nERROR    src.data.ingestion:ingestion.py:60 File validation failed for Bank Nifty Ticklist ********.csv: [\"Missing expected columns: {'Time', 'Volume', 'Last Rate'}\"]\\nERROR    src.data.import_manager:import_manager.py:376 \\u274c Bank Nifty Ticklist ********.csv import failed\\nINFO     src.data.validation:validation.py:88 Data validation passed for nifty: 2 records\\nINFO     performance:logging.py:219 Operation completed: store_tick_data\\nINFO     src.data.storage:storage.py:212 Stored 2 tick data records\\nINFO     performance:logging.py:219 Operation completed: process_file_Nifty Ticklist ********.csv\\nINFO     src.data.ingestion:ingestion.py:91 Successfully processed 2 records from Nifty Ticklist ********.csv\\nINFO     src.data.import_manager:import_manager.py:373 \\u2705 Nifty Ticklist ********.csv imported successfully\\nINFO     src.data.import_manager:import_manager.py:353 Job daily_2025-07-14_161020 completed: 1/2 successful\\nERROR    errors:logging.py:267 Exception in get_database_stats: 'str' object has no attribute 'strftime'\\nTraceback (most recent call last):\\n  File \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projs\\\\TickFiles\\\\src\\\\data\\\\storage.py\", line 410, in get_database_stats\\n    date_range = f\"{result[0].strftime('%Y-%m-%d')} to {result[1].strftime('%Y-%m-%d')}\"\\n                    ^^^^^^^^^^^^^^^^^^\\nAttributeError: 'str' object has no attribute 'strftime'\n============================== warnings summary ===============================\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n..\\..\\..\\..\\..\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323\n  C:\\Program Files\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_config.py:323: PydanticDeprecatedSince20: Support for class-based `config` is deprecated, use ConfigDict instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n    warnings.warn(DEPRECATION_MESSAGE, DeprecationWarning)\n\nconfig.py:107\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\config.py:107: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n    @validator('data_root', 'sample_data_path', 'models_path', 'logs_path')\n\ntests/test_integration.py::TestCompleteWorkflows::test_performance_workflow\ntests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations\ntests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations\ntests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations\ntests/test_integration.py::TestCompleteWorkflows::test_data_consistency_workflow\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\strategy\\professional_trader.py:355: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n    '1min': tick_data.resample('1T').agg({\n\ntests/test_integration.py::TestCompleteWorkflows::test_performance_workflow\ntests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations\ntests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations\ntests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations\ntests/test_integration.py::TestCompleteWorkflows::test_data_consistency_workflow\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\strategy\\professional_trader.py:359: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n    '5min': tick_data.resample('5T').agg({\n\ntests/test_integration.py::TestCompleteWorkflows::test_performance_workflow\ntests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations\ntests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations\ntests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations\ntests/test_integration.py::TestCompleteWorkflows::test_data_consistency_workflow\n  C:\\Users\\<USER>\\Documents\\Projs\\TickFiles\\src\\strategy\\professional_trader.py:363: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n    '15min': tick_data.resample('15T').agg({\n\n-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html\n=========================== short test summary info ===========================\nFAILED tests/test_integration.py::TestCompleteWorkflows::test_complete_daily_workflow\nFAILED tests/test_integration.py::TestCompleteWorkflows::test_bulk_import_and_analysis_workflow\nFAILED tests/test_integration.py::TestCompleteWorkflows::test_error_recovery_workflow\nERROR tests/test_integration.py::TestCompleteWorkflows::test_complete_daily_workflow\nERROR tests/test_integration.py::TestCompleteWorkflows::test_bulk_import_and_analysis_workflow\nERROR tests/test_integration.py::TestCompleteWorkflows::test_error_recovery_workflow\nERROR tests/test_integration.py::TestCompleteWorkflows::test_performance_workflow\nERROR tests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations\nERROR tests/test_integration.py::TestCompleteWorkflows::test_data_consistency_workflow\n============= 3 failed, 6 passed, 22 warnings, 6 errors in 4.57s ==============\n", "stderr": ""}}, "component_status": {"src.data.storage.TickDataStorage": {"imported": true, "error": null}, "src.data.import_manager.DataImportManager": {"imported": true, "error": null}, "src.strategy.professional_trader.ProfessionalTradingStrategy": {"imported": true, "error": null}, "src.ml.features.FeatureEngineering": {"imported": true, "error": null}, "src.api.endpoints.create_api_app": {"imported": true, "error": null}, "src.web.dashboard.TradingDashboard": {"imported": true, "error": null}, "config.get_config": {"imported": true, "error": null}}, "performance_metrics": {"error": "Insufficient data: 3 ticks"}, "recommendations": [{"priority": "HIGH", "category": "Testing", "issue": "Unit tests failing", "recommendation": "Fix failing unit tests before MVP release"}, {"priority": "CRITICAL", "category": "Core Functionality", "issue": "Basic functionality not working", "recommendation": "Fix core data import and analysis functionality"}, {"priority": "CRITICAL", "category": "MVP Status", "issue": "Critical issues prevent MVP", "recommendation": "Fix critical issues before MVP deployment"}]}