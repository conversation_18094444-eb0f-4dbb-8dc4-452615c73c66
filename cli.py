#!/usr/bin/env python3
"""
Command Line Interface for the Tick Data Analysis Platform
"""
import click
import sys
from pathlib import Path
import pandas as pd
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from config import get_config
from src.database.init_db import DatabaseManager
from src.data.ingestion import TickDataIngestion
from src.data.storage import TickDataStorage
from src.ml.features import FeatureEngineering
from src.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

@click.group()
@click.option('--config-file', default=None, help='Path to configuration file')
@click.pass_context
def cli(ctx, config_file):
    """Tick Data Analysis Platform CLI"""
    ctx.ensure_object(dict)
    
    # Load configuration
    if config_file:
        from config import reload_config
        ctx.obj['config'] = reload_config(config_file)
    else:
        ctx.obj['config'] = get_config()

@cli.group()
@click.pass_context
def db(ctx):
    """Database management commands"""
    pass

@db.command()
@click.pass_context
def init(ctx):
    """Initialize database schema"""
    config = ctx.obj['config']
    db_manager = DatabaseManager()
    
    click.echo("Initializing database...")
    
    # Check connection first
    if not db_manager.check_connection():
        click.echo("❌ Database connection failed", err=True)
        sys.exit(1)
    
    # Initialize schema
    if db_manager.initialize_database():
        click.echo("✅ Database initialized successfully")
    else:
        click.echo("❌ Database initialization failed", err=True)
        sys.exit(1)

@db.command()
@click.pass_context
def check(ctx):
    """Check database connection and status"""
    db_manager = DatabaseManager()
    
    click.echo("Checking database connection...")
    
    if db_manager.check_connection():
        click.echo("✅ Database connection successful")
        
        if db_manager.check_timescaledb_extension():
            click.echo("✅ TimescaleDB extension available")
        else:
            click.echo("⚠️  TimescaleDB extension not available")
        
        if db_manager.validate_database():
            click.echo("✅ Database schema validation passed")
        else:
            click.echo("❌ Database schema validation failed")
    else:
        click.echo("❌ Database connection failed")
        sys.exit(1)

@db.command()
@click.pass_context
def stats(ctx):
    """Show database statistics"""
    db_manager = DatabaseManager()
    stats = db_manager.get_database_stats()
    
    if stats:
        click.echo("\n📊 Database Statistics:")
        click.echo(f"Connection: {stats['connection_string']}")
        
        click.echo("\nTable Sizes:")
        for table, size in stats['table_sizes'].items():
            click.echo(f"  {table}: {size}")
        
        click.echo("\nRecord Counts:")
        for table, count in stats['record_counts'].items():
            click.echo(f"  {table}: {count:,}")
    else:
        click.echo("❌ Failed to get database statistics")

@cli.group()
@click.pass_context
def data(ctx):
    """Data processing commands"""
    pass

@data.command()
@click.argument('file_path', type=click.Path(exists=True))
@click.option('--validate/--no-validate', default=True, help='Validate data before processing')
@click.pass_context
def ingest(ctx, file_path, validate):
    """Ingest a single tick data file"""
    config = ctx.obj['config']
    
    click.echo(f"Ingesting file: {file_path}")
    
    try:
        ingestion = TickDataIngestion(config.database.connection_string, config)
        
        if ingestion.process_file(file_path, validate_file=validate):
            click.echo("✅ File ingested successfully")
        else:
            click.echo("❌ File ingestion failed")
            sys.exit(1)
    
    except Exception as e:
        click.echo(f"❌ Error during ingestion: {e}", err=True)
        sys.exit(1)

@data.command()
@click.argument('directory_path', type=click.Path(exists=True, file_okay=False))
@click.option('--validate/--no-validate', default=True, help='Validate data before processing')
@click.pass_context
def ingest_dir(ctx, directory_path, validate):
    """Ingest all CSV files in a directory"""
    config = ctx.obj['config']
    
    click.echo(f"Ingesting files from directory: {directory_path}")
    
    try:
        ingestion = TickDataIngestion(config.database.connection_string, config)
        
        # Process directory
        results = ingestion.process_directory(directory_path)
        
        click.echo(f"\n📊 Processing Results:")
        click.echo(f"Total files: {results['total_files']}")
        click.echo(f"Successful: {results['successful']}")
        click.echo(f"Failed: {results['failed']}")
        
        if results['failed'] > 0:
            click.echo("⚠️  Some files failed to process")
        else:
            click.echo("✅ All files processed successfully")
    
    except Exception as e:
        click.echo(f"❌ Error during batch ingestion: {e}", err=True)
        sys.exit(1)

@data.command()
@click.option('--index', required=True, help='Index name (e.g., nifty, bank_nifty)')
@click.option('--start-date', required=True, help='Start date (YYYY-MM-DD)')
@click.option('--end-date', required=True, help='End date (YYYY-MM-DD)')
@click.option('--output', help='Output CSV file path')
@click.pass_context
def export(ctx, index, start_date, end_date, output):
    """Export tick data to CSV"""
    config = ctx.obj['config']
    
    click.echo(f"Exporting {index} data from {start_date} to {end_date}")
    
    try:
        storage = TickDataStorage(config.database.connection_string)
        data = storage.get_tick_data(index, start_date, end_date)
        
        if len(data) == 0:
            click.echo("⚠️  No data found for the specified criteria")
            return
        
        if output:
            data.to_csv(output, index=False)
            click.echo(f"✅ Data exported to {output}")
        else:
            click.echo(f"Found {len(data)} records:")
            click.echo(data.head().to_string())
    
    except Exception as e:
        click.echo(f"❌ Error during export: {e}", err=True)
        sys.exit(1)

@cli.group()
@click.pass_context
def ml(ctx):
    """Machine learning commands"""
    pass

@ml.command()
@click.option('--index', required=True, help='Index name')
@click.option('--start-date', required=True, help='Start date (YYYY-MM-DD)')
@click.option('--end-date', required=True, help='End date (YYYY-MM-DD)')
@click.option('--output', help='Output CSV file for features')
@click.pass_context
def extract_features(ctx, index, start_date, end_date, output):
    """Extract features from tick data"""
    config = ctx.obj['config']
    
    click.echo(f"Extracting features for {index} from {start_date} to {end_date}")
    
    try:
        # Get tick data
        storage = TickDataStorage(config.database.connection_string)
        tick_data = storage.get_tick_data(index, start_date, end_date)
        
        if len(tick_data) == 0:
            click.echo("⚠️  No tick data found for the specified criteria")
            return
        
        # Extract features
        fe = FeatureEngineering()
        features = fe.extract_all_features(tick_data)
        
        click.echo(f"✅ Extracted {len(features.columns)} features from {len(tick_data)} ticks")
        
        if output:
            # Combine with original data
            combined = pd.concat([tick_data, features], axis=1)
            combined.to_csv(output, index=False)
            click.echo(f"✅ Features saved to {output}")
        else:
            click.echo("Feature summary:")
            click.echo(features.describe().to_string())
    
    except Exception as e:
        click.echo(f"❌ Error during feature extraction: {e}", err=True)
        sys.exit(1)

@cli.command()
@click.pass_context
def status(ctx):
    """Show system status"""
    config = ctx.obj['config']
    
    click.echo("🔍 System Status Check")
    click.echo("=" * 50)
    
    # Database status
    click.echo("\n📊 Database:")
    db_manager = DatabaseManager()
    if db_manager.check_connection():
        click.echo("  ✅ Connection: OK")
        stats = db_manager.get_database_stats()
        if stats:
            for table, count in stats['record_counts'].items():
                click.echo(f"  📈 {table}: {count:,} records")
    else:
        click.echo("  ❌ Connection: Failed")
    
    # Configuration status
    click.echo("\n⚙️  Configuration:")
    click.echo(f"  📍 Database: {config.database.host}:{config.database.port}")
    click.echo(f"  🗄️  Database name: {config.database.database}")
    click.echo(f"  📝 Log level: {config.logging.level}")
    
    # Data directories
    click.echo("\n📁 Data Directories:")
    directories = [
        ("Data root", config.data.data_root),
        ("Models", config.data.models_path),
        ("Logs", config.data.logs_path)
    ]
    
    for name, path in directories:
        if path.exists():
            click.echo(f"  ✅ {name}: {path}")
        else:
            click.echo(f"  ❌ {name}: {path} (missing)")

@cli.command()
def version():
    """Show version information"""
    click.echo("Tick Data Analysis Platform")
    click.echo("Version: 1.0.0-dev")
    click.echo("Python: " + sys.version)

if __name__ == '__main__':
    cli()
