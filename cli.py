#!/usr/bin/env python3
"""
Unified Command Line Interface for the Tick Data Analysis Platform
Professional Trading System with Comprehensive Data Management
"""
import click
import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from config import get_config
from src.database.init_db import DatabaseManager
from src.data.ingestion import TickDataIngestion
from src.data.storage import TickDataStorage
from src.data.import_manager import DataImportManager
from src.ml.features import FeatureEngineering
from src.strategy.professional_trader import ProfessionalTradingStrategy
from src.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

@click.group()
@click.option('--config-file', default=None, help='Path to configuration file')
@click.pass_context
def cli(ctx, config_file):
    """Tick Data Analysis Platform CLI"""
    ctx.ensure_object(dict)
    
    # Load configuration
    if config_file:
        from config import reload_config
        ctx.obj['config'] = reload_config(config_file)
    else:
        ctx.obj['config'] = get_config()

@cli.group()
@click.pass_context
def db(ctx):
    """Database management commands"""
    pass

@db.command()
@click.pass_context
def init(ctx):
    """Initialize database schema"""
    config = ctx.obj['config']
    db_manager = DatabaseManager()
    
    click.echo("Initializing database...")
    
    # Check connection first
    if not db_manager.check_connection():
        click.echo("❌ Database connection failed", err=True)
        sys.exit(1)
    
    # Initialize schema
    if db_manager.initialize_database():
        click.echo("✅ Database initialized successfully")
    else:
        click.echo("❌ Database initialization failed", err=True)
        sys.exit(1)

@db.command()
@click.pass_context
def check(ctx):
    """Check database connection and status"""
    db_manager = DatabaseManager()
    
    click.echo("Checking database connection...")
    
    if db_manager.check_connection():
        click.echo("✅ Database connection successful")
        
        if db_manager.check_timescaledb_extension():
            click.echo("✅ TimescaleDB extension available")
        else:
            click.echo("⚠️  TimescaleDB extension not available")
        
        if db_manager.validate_database():
            click.echo("✅ Database schema validation passed")
        else:
            click.echo("❌ Database schema validation failed")
    else:
        click.echo("❌ Database connection failed")
        sys.exit(1)

@db.command()
@click.pass_context
def stats(ctx):
    """Show database statistics"""
    db_manager = DatabaseManager()
    stats = db_manager.get_database_stats()
    
    if stats:
        click.echo("\n📊 Database Statistics:")
        click.echo(f"Connection: {stats['connection_string']}")
        
        click.echo("\nTable Sizes:")
        for table, size in stats['table_sizes'].items():
            click.echo(f"  {table}: {size}")
        
        click.echo("\nRecord Counts:")
        for table, count in stats['record_counts'].items():
            click.echo(f"  {table}: {count:,}")
    else:
        click.echo("❌ Failed to get database statistics")

@cli.group()
@click.pass_context
def data(ctx):
    """Data processing commands"""
    pass

@data.command()
@click.argument('file_path', type=click.Path(exists=True))
@click.option('--validate/--no-validate', default=True, help='Validate data before processing')
@click.pass_context
def ingest(ctx, file_path, validate):
    """Ingest a single tick data file"""
    config = ctx.obj['config']
    
    click.echo(f"Ingesting file: {file_path}")
    
    try:
        ingestion = TickDataIngestion(config.database.connection_string, config)
        
        if ingestion.process_file(file_path, validate_file=validate):
            click.echo("✅ File ingested successfully")
        else:
            click.echo("❌ File ingestion failed")
            sys.exit(1)
    
    except Exception as e:
        click.echo(f"❌ Error during ingestion: {e}", err=True)
        sys.exit(1)

@data.command()
@click.argument('directory_path', type=click.Path(exists=True, file_okay=False))
@click.option('--validate/--no-validate', default=True, help='Validate data before processing')
@click.option('--clean/--no-clean', default=False, help='Clean database before import')
@click.option('--summary', help='Save import summary to file')
@click.pass_context
def ingest_dir(ctx, directory_path, validate, clean, summary):
    """Ingest all CSV files in a directory"""
    config = ctx.obj['config']

    click.echo(f"🔄 Ingesting files from directory: {directory_path}")

    try:
        storage = TickDataStorage(config.database.connection_string)
        ingestion = TickDataIngestion(storage, config)

        # Clean database if requested
        if clean:
            click.echo("🧹 Cleaning database...")
            storage.clear_all_data()
            click.echo("✅ Database cleaned")

        # Process directory
        results = ingestion.process_directory(directory_path)

        click.echo(f"\n📊 Processing Results:")
        click.echo(f"Total files: {results['total_files']}")
        click.echo(f"Successful: {results['successful']}")
        click.echo(f"Failed: {results['failed']}")

        # Save summary if requested
        if summary:
            summary_data = {
                'timestamp': datetime.now().isoformat(),
                'directory': directory_path,
                'results': results
            }
            with open(summary, 'w') as f:
                json.dump(summary_data, f, indent=2)
            click.echo(f"📄 Summary saved to: {summary}")

        if results['failed'] > 0:
            click.echo("⚠️  Some files failed to process")
        else:
            click.echo("✅ All files processed successfully")

    except Exception as e:
        click.echo(f"❌ Error during batch ingestion: {e}", err=True)
        sys.exit(1)

@data.command()
@click.option('--date', help='Specific date to import (YYYY-MM-DD), defaults to today')
@click.option('--source-dir', default='sampledata', help='Source directory for data files')
@click.option('--indices', help='Comma-separated list of indices to import (default: all)')
@click.option('--async/--sync', default=False, help='Run import asynchronously')
@click.pass_context
def daily_import(ctx, date, source_dir, indices, async):
    """Import data for a specific trading day using the import manager"""
    config = ctx.obj['config']

    # Use today's date if not specified
    if not date:
        date = datetime.now().strftime('%Y-%m-%d')

    click.echo(f"📅 Daily import for: {date}")

    try:
        # Initialize components
        storage = TickDataStorage(config.database.connection_string)
        import_manager = DataImportManager(storage, config)

        # Parse indices if specified
        indices_list = None
        if indices:
            indices_list = [idx.strip() for idx in indices.split(',')]

        # Create import job
        job_id = import_manager.create_daily_import_job(
            date=date,
            source_dir=Path(source_dir),
            indices=indices_list
        )

        click.echo(f"📋 Created import job: {job_id}")

        if async:
            click.echo("🔄 Job created and will run in background")
            click.echo(f"💡 Use 'python cli.py data job-status {job_id}' to check progress")
        else:
            # Execute job synchronously
            click.echo("🔄 Executing import job...")

            success = import_manager.execute_job(job_id)
            job = import_manager.get_job_status(job_id)

            click.echo(f"\n📊 Daily Import Results:")
            click.echo(f"Job ID: {job_id}")
            click.echo(f"Status: {job.status}")
            click.echo(f"Files Total: {job.files_total}")
            click.echo(f"Files Processed: {job.files_processed}")
            click.echo(f"Files Successful: {job.files_successful}")
            click.echo(f"Files Failed: {job.files_failed}")

            if job.error_message:
                click.echo(f"Error: {job.error_message}")

            if success:
                click.echo("🎉 Daily import completed successfully!")
            else:
                click.echo("⚠️  Import completed with errors")
                sys.exit(1)

    except Exception as e:
        click.echo(f"❌ Error during daily import: {e}", err=True)
        sys.exit(1)

@data.command()
@click.option('--source-dir', default='sampledata', help='Source directory for data files')
@click.option('--start-date', help='Start date (YYYY-MM-DD) for date range filter')
@click.option('--end-date', help='End date (YYYY-MM-DD) for date range filter')
@click.option('--indices', help='Comma-separated list of indices to import (default: all)')
@click.option('--clean/--no-clean', default=False, help='Clean database before import')
@click.option('--async/--sync', default=False, help='Run import asynchronously')
@click.pass_context
def bulk_import(ctx, source_dir, start_date, end_date, indices, clean, async):
    """Bulk import data using the import manager"""
    config = ctx.obj['config']

    click.echo("📦 Bulk data import...")

    try:
        # Initialize components
        storage = TickDataStorage(config.database.connection_string)
        import_manager = DataImportManager(storage, config)

        # Parse date range
        date_range = None
        if start_date and end_date:
            date_range = (start_date, end_date)
            click.echo(f"📅 Date range: {start_date} to {end_date}")

        # Parse indices
        indices_list = None
        if indices:
            indices_list = [idx.strip() for idx in indices.split(',')]
            click.echo(f"📊 Indices: {', '.join(indices_list)}")

        # Create bulk import job
        job_id = import_manager.create_bulk_import_job(
            source_dir=Path(source_dir),
            date_range=date_range,
            indices=indices_list,
            clean_database=clean
        )

        click.echo(f"📋 Created bulk import job: {job_id}")

        if clean:
            click.echo("🧹 Database will be cleaned before import")

        if async:
            click.echo("🔄 Job created and will run in background")
            click.echo(f"💡 Use 'python cli.py data job-status {job_id}' to check progress")
        else:
            # Execute job synchronously
            click.echo("🔄 Executing bulk import job...")

            success = import_manager.execute_job(job_id)
            job = import_manager.get_job_status(job_id)

            click.echo(f"\n📊 Bulk Import Results:")
            click.echo(f"Job ID: {job_id}")
            click.echo(f"Status: {job.status}")
            click.echo(f"Files Total: {job.files_total}")
            click.echo(f"Files Processed: {job.files_processed}")
            click.echo(f"Files Successful: {job.files_successful}")
            click.echo(f"Files Failed: {job.files_failed}")

            if job.error_message:
                click.echo(f"Error: {job.error_message}")

            if success:
                click.echo("🎉 Bulk import completed successfully!")
            else:
                click.echo("⚠️  Import completed with errors")
                sys.exit(1)

    except Exception as e:
        click.echo(f"❌ Error during bulk import: {e}", err=True)
        sys.exit(1)

@data.command()
@click.argument('job_id', required=False)
@click.option('--status', help='Filter by status (pending, running, completed, failed)')
@click.option('--limit', default=20, help='Maximum number of jobs to show')
@click.pass_context
def job_status(ctx, job_id, status, limit):
    """Show import job status"""
    config = ctx.obj['config']

    try:
        storage = TickDataStorage(config.database.connection_string)
        import_manager = DataImportManager(storage, config)

        if job_id:
            # Show specific job
            job = import_manager.get_job_status(job_id)
            if not job:
                click.echo(f"❌ Job not found: {job_id}")
                sys.exit(1)

            click.echo(f"📋 Job Details: {job_id}")
            click.echo(f"Type: {job.job_type}")
            click.echo(f"Status: {job.status}")
            click.echo(f"Created: {job.created_at}")
            click.echo(f"Started: {job.started_at or 'Not started'}")
            click.echo(f"Completed: {job.completed_at or 'Not completed'}")
            click.echo(f"Files Total: {job.files_total}")
            click.echo(f"Files Processed: {job.files_processed}")
            click.echo(f"Files Successful: {job.files_successful}")
            click.echo(f"Files Failed: {job.files_failed}")

            if job.files_total > 0:
                progress = job.files_processed / job.files_total * 100
                click.echo(f"Progress: {progress:.1f}%")

            if job.error_message:
                click.echo(f"Error: {job.error_message}")
        else:
            # List jobs
            jobs = import_manager.list_jobs(status, limit)

            if not jobs:
                click.echo("📋 No import jobs found")
                return

            click.echo(f"📋 Import Jobs ({len(jobs)} found):")
            click.echo()

            # Table header
            click.echo(f"{'Job ID':<20} {'Type':<8} {'Status':<12} {'Progress':<10} {'Files':<15} {'Created':<20}")
            click.echo("-" * 90)

            for job in jobs:
                progress = f"{job.files_processed}/{job.files_total}"
                if job.files_total > 0:
                    pct = job.files_processed / job.files_total * 100
                    progress += f" ({pct:.0f}%)"

                created = job.created_at.strftime('%Y-%m-%d %H:%M')

                click.echo(f"{job.job_id:<20} {job.job_type:<8} {job.status:<12} {progress:<10} "
                          f"{job.files_successful}/{job.files_total}{'✅' if job.files_failed == 0 else '⚠️':<15} {created:<20}")

    except Exception as e:
        click.echo(f"❌ Error getting job status: {e}", err=True)
        sys.exit(1)

@data.command()
@click.option('--index', required=True, help='Index name (e.g., nifty, bank_nifty)')
@click.option('--start-date', required=True, help='Start date (YYYY-MM-DD)')
@click.option('--end-date', required=True, help='End date (YYYY-MM-DD)')
@click.option('--output', help='Output CSV file path')
@click.pass_context
def export(ctx, index, start_date, end_date, output):
    """Export tick data to CSV"""
    config = ctx.obj['config']
    
    click.echo(f"Exporting {index} data from {start_date} to {end_date}")
    
    try:
        storage = TickDataStorage(config.database.connection_string)
        data = storage.get_tick_data(index, start_date, end_date)
        
        if len(data) == 0:
            click.echo("⚠️  No data found for the specified criteria")
            return
        
        if output:
            data.to_csv(output, index=False)
            click.echo(f"✅ Data exported to {output}")
        else:
            click.echo(f"Found {len(data)} records:")
            click.echo(data.head().to_string())
    
    except Exception as e:
        click.echo(f"❌ Error during export: {e}", err=True)
        sys.exit(1)

@cli.group()
@click.pass_context
def trading(ctx):
    """Professional trading analysis and signal generation"""
    pass

@trading.command()
@click.option('--date', help='Analysis date (YYYY-MM-DD), defaults to latest data')
@click.option('--indices', help='Comma-separated list of indices (default: all)')
@click.option('--output', help='Save analysis to JSON file')
@click.pass_context
def analyze(ctx, date, indices, output):
    """Run professional trading analysis"""
    config = ctx.obj['config']

    click.echo("🎯 Running Professional Trading Analysis...")

    try:
        storage = TickDataStorage(config.database.connection_string)
        strategy = ProfessionalTradingStrategy(storage)

        # Get available indices if not specified
        if not indices:
            available_indices = ['nifty', 'bank_nifty', 'fin_nifty', 'midcap_nifty', 'nifty_next']
        else:
            available_indices = [idx.strip() for idx in indices.split(',')]

        # Use latest date if not specified
        if not date:
            # Get latest date from database
            latest_data = storage.get_latest_data()
            if latest_data.empty:
                click.echo("❌ No data found in database")
                sys.exit(1)
            date = latest_data['timestamp'].dt.date.iloc[0].strftime('%Y-%m-%d')

        click.echo(f"📅 Analysis date: {date}")
        click.echo(f"📊 Indices: {', '.join(available_indices)}")

        # Run analysis for each index
        analysis_results = {}

        for index_name in available_indices:
            click.echo(f"\n🔍 Analyzing {index_name}...")

            try:
                analysis = strategy.analyze_market_structure(index_name, date)
                if analysis:
                    analysis_results[index_name] = analysis

                    # Display key metrics
                    if 'volume_analysis' in analysis:
                        vol_data = analysis['volume_analysis']
                        click.echo(f"  📈 Volume Threshold (75th): {vol_data.get('percentile_75', 'N/A'):,}")
                        click.echo(f"  📈 Volume Threshold (90th): {vol_data.get('percentile_90', 'N/A'):,}")

                    if 'price_levels' in analysis:
                        levels = analysis['price_levels']
                        click.echo(f"  💰 Current Price: ₹{levels.get('current_price', 'N/A')}")
                        click.echo(f"  🎯 Support: ₹{levels.get('support', 'N/A')}")
                        click.echo(f"  🎯 Resistance: ₹{levels.get('resistance', 'N/A')}")

                    click.echo(f"  ✅ {index_name} analysis completed")
                else:
                    click.echo(f"  ⚠️  No data available for {index_name}")

            except Exception as e:
                click.echo(f"  ❌ Analysis failed for {index_name}: {e}")

        # Save results if requested
        if output:
            analysis_data = {
                'timestamp': datetime.now().isoformat(),
                'analysis_date': date,
                'indices': list(analysis_results.keys()),
                'results': analysis_results
            }

            with open(output, 'w') as f:
                json.dump(analysis_data, f, indent=2, default=str)

            click.echo(f"\n💾 Analysis saved to: {output}")

        click.echo(f"\n🎉 Analysis completed for {len(analysis_results)} indices")

    except Exception as e:
        click.echo(f"❌ Error during analysis: {e}", err=True)
        sys.exit(1)

@trading.command()
@click.option('--date', help='Signal generation date (YYYY-MM-DD), defaults to latest data')
@click.option('--indices', help='Comma-separated list of indices (default: all)')
@click.option('--confidence', default=0.7, help='Minimum confidence threshold (0.0-1.0)')
@click.option('--output', help='Save signals to JSON file')
@click.pass_context
def signals(ctx, date, indices, confidence, output):
    """Generate trading signals with entry/exit levels"""
    config = ctx.obj['config']

    click.echo("📡 Generating Trading Signals...")

    try:
        storage = TickDataStorage(config.database.connection_string)
        strategy = ProfessionalTradingStrategy(storage)

        # Get available indices if not specified
        if not indices:
            available_indices = ['nifty', 'bank_nifty', 'fin_nifty', 'midcap_nifty', 'nifty_next']
        else:
            available_indices = [idx.strip() for idx in indices.split(',')]

        # Use latest date if not specified
        if not date:
            latest_data = storage.get_latest_data()
            if latest_data.empty:
                click.echo("❌ No data found in database")
                sys.exit(1)
            date = latest_data['timestamp'].dt.date.iloc[0].strftime('%Y-%m-%d')

        click.echo(f"📅 Signal date: {date}")
        click.echo(f"🎯 Confidence threshold: {confidence}")

        # Generate signals for each index
        all_signals = {}

        for index_name in available_indices:
            click.echo(f"\n📡 Generating signals for {index_name}...")

            try:
                # Get market analysis first
                analysis = strategy.analyze_market_structure(index_name, date)
                if not analysis:
                    click.echo(f"  ⚠️  No analysis data for {index_name}")
                    continue

                # Generate trading signals
                signals = strategy.generate_trading_signals(analysis, confidence_threshold=confidence)

                if signals:
                    all_signals[index_name] = signals

                    # Display signal summary
                    for signal in signals:
                        direction = signal.get('direction', 'Unknown')
                        entry = signal.get('entry_price', 'N/A')
                        target = signal.get('target_price', 'N/A')
                        stop = signal.get('stop_loss', 'N/A')
                        conf = signal.get('confidence', 0)
                        volume_req = signal.get('volume_requirement', 'N/A')

                        click.echo(f"  🎯 {direction.upper()} Signal:")
                        click.echo(f"    Entry: ₹{entry}")
                        click.echo(f"    Target: ₹{target}")
                        click.echo(f"    Stop: ₹{stop}")
                        click.echo(f"    Confidence: {conf:.1%}")
                        click.echo(f"    Volume Required: {volume_req:,}")
                else:
                    click.echo(f"  ⚠️  No signals generated for {index_name}")

            except Exception as e:
                click.echo(f"  ❌ Signal generation failed for {index_name}: {e}")

        # Save signals if requested
        if output:
            signal_data = {
                'timestamp': datetime.now().isoformat(),
                'signal_date': date,
                'confidence_threshold': confidence,
                'indices': list(all_signals.keys()),
                'signals': all_signals
            }

            with open(output, 'w') as f:
                json.dump(signal_data, f, indent=2, default=str)

            click.echo(f"\n💾 Signals saved to: {output}")

        click.echo(f"\n🎉 Generated signals for {len(all_signals)} indices")

    except Exception as e:
        click.echo(f"❌ Error during signal generation: {e}", err=True)
        sys.exit(1)

@cli.group()
@click.pass_context
def ml(ctx):
    """Machine learning commands"""
    pass

@ml.command()
@click.option('--index', required=True, help='Index name')
@click.option('--start-date', required=True, help='Start date (YYYY-MM-DD)')
@click.option('--end-date', required=True, help='End date (YYYY-MM-DD)')
@click.option('--output', help='Output CSV file for features')
@click.pass_context
def extract_features(ctx, index, start_date, end_date, output):
    """Extract features from tick data"""
    config = ctx.obj['config']
    
    click.echo(f"Extracting features for {index} from {start_date} to {end_date}")
    
    try:
        # Get tick data
        storage = TickDataStorage(config.database.connection_string)
        tick_data = storage.get_tick_data(index, start_date, end_date)
        
        if len(tick_data) == 0:
            click.echo("⚠️  No tick data found for the specified criteria")
            return
        
        # Extract features
        fe = FeatureEngineering()
        features = fe.extract_all_features(tick_data)
        
        click.echo(f"✅ Extracted {len(features.columns)} features from {len(tick_data)} ticks")
        
        if output:
            # Combine with original data
            combined = pd.concat([tick_data, features], axis=1)
            combined.to_csv(output, index=False)
            click.echo(f"✅ Features saved to {output}")
        else:
            click.echo("Feature summary:")
            click.echo(features.describe().to_string())
    
    except Exception as e:
        click.echo(f"❌ Error during feature extraction: {e}", err=True)
        sys.exit(1)

@cli.command()
@click.pass_context
def status(ctx):
    """Show system status"""
    config = ctx.obj['config']
    
    click.echo("🔍 System Status Check")
    click.echo("=" * 50)
    
    # Database status
    click.echo("\n📊 Database:")
    db_manager = DatabaseManager()
    if db_manager.check_connection():
        click.echo("  ✅ Connection: OK")
        stats = db_manager.get_database_stats()
        if stats:
            for table, count in stats['record_counts'].items():
                click.echo(f"  📈 {table}: {count:,} records")
    else:
        click.echo("  ❌ Connection: Failed")
    
    # Configuration status
    click.echo("\n⚙️  Configuration:")
    click.echo(f"  📍 Database: {config.database.host}:{config.database.port}")
    click.echo(f"  🗄️  Database name: {config.database.database}")
    click.echo(f"  📝 Log level: {config.logging.level}")
    
    # Data directories
    click.echo("\n📁 Data Directories:")
    directories = [
        ("Data root", config.data.data_root),
        ("Models", config.data.models_path),
        ("Logs", config.data.logs_path)
    ]
    
    for name, path in directories:
        if path.exists():
            click.echo(f"  ✅ {name}: {path}")
        else:
            click.echo(f"  ❌ {name}: {path} (missing)")

@cli.command()
def version():
    """Show version information"""
    click.echo("Tick Data Analysis Platform")
    click.echo("Version: 1.0.0-dev")
    click.echo("Python: " + sys.version)

@cli.group()
@click.pass_context
def system(ctx):
    """System monitoring and management commands"""
    pass

@system.command()
@click.pass_context
def status(ctx):
    """Show system status and database statistics"""
    config = ctx.obj['config']

    click.echo("🔍 System Status Check...")

    try:
        storage = TickDataStorage(config.database.connection_string)

        # Database connection test
        click.echo("\n📊 Database Status:")
        try:
            stats = storage.get_database_stats()
            click.echo(f"  ✅ Connection: OK")
            click.echo(f"  📈 Total Records: {stats.get('total_records', 0):,}")
            click.echo(f"  📅 Date Range: {stats.get('date_range', 'N/A')}")
            click.echo(f"  📊 Indices: {stats.get('indices_count', 0)}")
        except Exception as e:
            click.echo(f"  ❌ Database Error: {e}")

        # Data quality check
        click.echo("\n🔍 Data Quality:")
        try:
            quality_stats = storage.get_data_quality_stats()
            click.echo(f"  📊 Records per Index:")
            for index_name, count in quality_stats.get('records_per_index', {}).items():
                click.echo(f"    {index_name}: {count:,}")

            click.echo(f"  📅 Latest Data: {quality_stats.get('latest_timestamp', 'N/A')}")
            click.echo(f"  ⚠️  Data Gaps: {quality_stats.get('data_gaps', 0)}")
        except Exception as e:
            click.echo(f"  ❌ Quality Check Error: {e}")

        # System resources
        click.echo("\n💾 System Resources:")
        try:
            import psutil
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')

            click.echo(f"  🖥️  CPU Usage: {cpu_percent:.1f}%")
            click.echo(f"  💾 Memory Usage: {memory.percent:.1f}% ({memory.used // (1024**3):.1f}GB / {memory.total // (1024**3):.1f}GB)")
            click.echo(f"  💿 Disk Usage: {disk.percent:.1f}% ({disk.used // (1024**3):.1f}GB / {disk.total // (1024**3):.1f}GB)")
        except ImportError:
            click.echo("  ⚠️  psutil not installed - system metrics unavailable")
        except Exception as e:
            click.echo(f"  ❌ System Check Error: {e}")

        click.echo("\n✅ System status check completed")

    except Exception as e:
        click.echo(f"❌ Error during status check: {e}", err=True)
        sys.exit(1)

@system.command()
@click.option('--days', default=7, help='Number of days to keep (default: 7)')
@click.option('--dry-run/--no-dry-run', default=True, help='Show what would be deleted without actually deleting')
@click.pass_context
def cleanup(ctx, days, dry_run):
    """Clean up old data and temporary files"""
    config = ctx.obj['config']

    if dry_run:
        click.echo("🧹 Cleanup Preview (Dry Run)...")
    else:
        click.echo("🧹 Cleaning up system...")

    try:
        storage = TickDataStorage(config.database.connection_string)

        # Calculate cutoff date
        cutoff_date = datetime.now() - timedelta(days=days)
        click.echo(f"📅 Cutoff date: {cutoff_date.strftime('%Y-%m-%d')}")

        # Find old data
        old_data_count = storage.count_records_before_date(cutoff_date.strftime('%Y-%m-%d'))
        click.echo(f"📊 Records older than {days} days: {old_data_count:,}")

        if old_data_count > 0:
            if dry_run:
                click.echo(f"  🗑️  Would delete {old_data_count:,} old records")
            else:
                click.echo(f"  🗑️  Deleting {old_data_count:,} old records...")
                deleted = storage.delete_records_before_date(cutoff_date.strftime('%Y-%m-%d'))
                click.echo(f"  ✅ Deleted {deleted:,} records")

        # Clean up temporary files
        temp_patterns = ['*.tmp', '*.log.old', 'import_summary_*.csv', 'next_day_predictions_*.json']
        temp_files_found = []

        for pattern in temp_patterns:
            for file_path in Path('.').glob(pattern):
                if file_path.stat().st_mtime < cutoff_date.timestamp():
                    temp_files_found.append(file_path)

        if temp_files_found:
            click.echo(f"📁 Temporary files to clean: {len(temp_files_found)}")
            for file_path in temp_files_found:
                if dry_run:
                    click.echo(f"  🗑️  Would delete: {file_path}")
                else:
                    file_path.unlink()
                    click.echo(f"  🗑️  Deleted: {file_path}")
        else:
            click.echo("📁 No temporary files to clean")

        # Database optimization
        if not dry_run:
            click.echo("🔧 Optimizing database...")
            storage.optimize_database()
            click.echo("✅ Database optimized")
        else:
            click.echo("🔧 Would optimize database")

        if dry_run:
            click.echo("\n💡 Run with --no-dry-run to actually perform cleanup")
        else:
            click.echo("\n✅ Cleanup completed successfully")

    except Exception as e:
        click.echo(f"❌ Error during cleanup: {e}", err=True)
        sys.exit(1)

@system.command()
@click.option('--watch/--no-watch', default=False, help='Watch for new files continuously')
@click.option('--interval', default=60, help='Watch interval in seconds (default: 60)')
@click.option('--source-dir', default='sampledata', help='Directory to monitor')
@click.pass_context
def monitor(ctx, watch, interval, source_dir):
    """Monitor data directory for new files and auto-import"""
    config = ctx.obj['config']

    click.echo("👁️  Data Directory Monitor...")
    click.echo(f"📁 Monitoring: {source_dir}")

    if not Path(source_dir).exists():
        click.echo(f"❌ Directory not found: {source_dir}")
        sys.exit(1)

    try:
        storage = TickDataStorage(config.database.connection_string)
        ingestion = TickDataIngestion(storage, config)

        # Track processed files
        processed_files = set()

        def scan_and_process():
            """Scan directory and process new files"""
            new_files = []

            for file_path in Path(source_dir).glob('*.csv'):
                if file_path.name not in processed_files:
                    new_files.append(file_path)

            if new_files:
                click.echo(f"\n🔄 Found {len(new_files)} new files")

                for file_path in new_files:
                    click.echo(f"📁 Processing: {file_path.name}")

                    try:
                        if ingestion.process_file(str(file_path)):
                            processed_files.add(file_path.name)
                            click.echo(f"✅ {file_path.name} imported successfully")
                        else:
                            click.echo(f"❌ {file_path.name} import failed")
                    except Exception as e:
                        click.echo(f"❌ Error processing {file_path.name}: {e}")

            return len(new_files)

        # Initial scan
        initial_files = scan_and_process()
        click.echo(f"📊 Initial scan: {initial_files} files processed")

        if watch:
            click.echo(f"👁️  Watching for new files (interval: {interval}s)")
            click.echo("Press Ctrl+C to stop monitoring")

            try:
                while True:
                    time.sleep(interval)
                    new_count = scan_and_process()
                    if new_count == 0:
                        click.echo(".", nl=False)  # Show activity without newline

            except KeyboardInterrupt:
                click.echo("\n🛑 Monitoring stopped by user")
        else:
            click.echo("✅ Single scan completed")

    except Exception as e:
        click.echo(f"❌ Error during monitoring: {e}", err=True)
        sys.exit(1)

if __name__ == '__main__':
    cli()
