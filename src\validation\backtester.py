"""
Backtesting and validation framework for prediction accuracy
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from config import get_config
from src.data.storage import TickDataStorage
from src.ml.prediction_engine import PredictionEngine
from src.utils.logging import get_logger, get_performance_logger

logger = get_logger(__name__)
perf_logger = get_performance_logger()

class PredictionBacktester:
    """Backtesting framework for validating prediction accuracy"""
    
    def __init__(self, config=None, db_connection_string=None):
        """
        Initialize backtester

        Args:
            config: Configuration object
            db_connection_string: Override database connection string
        """
        self.config = config or get_config()

        # Use override connection string if provided
        connection_string = db_connection_string or self.config.database.connection_string

        self.storage = TickDataStorage(connection_string)
        self.prediction_engine = PredictionEngine(config, connection_string)
        
    def run_walk_forward_validation(self, index_name: str, start_date: str, 
                                  end_date: str, train_window_days: int = 30) -> Dict[str, Any]:
        """
        Run walk-forward validation on historical data
        
        Args:
            index_name: Name of the index to test
            start_date: Start date for validation period
            end_date: End date for validation period
            train_window_days: Number of days to use for training
            
        Returns:
            Dictionary with validation results
        """
        logger.info(f"Starting walk-forward validation for {index_name}")
        logger.info(f"Period: {start_date} to {end_date}, Training window: {train_window_days} days")
        
        # Generate test dates
        test_dates = self.generate_test_dates(start_date, end_date)
        
        results = {
            'index_name': index_name,
            'validation_period': f"{start_date} to {end_date}",
            'train_window_days': train_window_days,
            'test_dates': test_dates,
            'predictions': [],
            'performance_metrics': {},
            'validation_start': datetime.now().isoformat()
        }
        
        successful_predictions = 0
        failed_predictions = 0
        
        for i, test_date in enumerate(test_dates):
            logger.info(f"Testing {i+1}/{len(test_dates)}: {test_date}")
            
            try:
                # Calculate training period
                test_dt = datetime.strptime(test_date, '%Y-%m-%d')
                train_end = (test_dt - timedelta(days=1)).strftime('%Y-%m-%d')
                train_start = (test_dt - timedelta(days=train_window_days)).strftime('%Y-%m-%d')
                
                # Generate prediction
                prediction_result = self.prediction_engine.train_and_predict(
                    index_name, train_start, train_end, test_date
                )
                
                # Get actual outcome
                actual_outcome = self.get_actual_outcome(index_name, test_date)
                
                if actual_outcome:
                    # Evaluate prediction
                    evaluation = self.evaluate_prediction(
                        prediction_result['predictions'][0], actual_outcome
                    )
                    
                    prediction_result['actual_outcome'] = actual_outcome
                    prediction_result['evaluation'] = evaluation
                    
                    successful_predictions += 1
                else:
                    prediction_result['actual_outcome'] = None
                    prediction_result['evaluation'] = {'error': 'No actual data available'}
                    failed_predictions += 1
                
                results['predictions'].append(prediction_result)
                
            except Exception as e:
                logger.error(f"Failed to generate prediction for {test_date}: {e}")
                failed_predictions += 1
                
                results['predictions'].append({
                    'predict_date': test_date,
                    'error': str(e),
                    'training_period': f"{train_start} to {train_end}"
                })
        
        # Calculate overall performance metrics
        results['performance_metrics'] = self.calculate_performance_metrics(results['predictions'])
        results['summary'] = {
            'total_tests': len(test_dates),
            'successful_predictions': successful_predictions,
            'failed_predictions': failed_predictions,
            'success_rate': successful_predictions / len(test_dates) if test_dates else 0
        }
        
        results['validation_end'] = datetime.now().isoformat()
        
        logger.info(f"Walk-forward validation completed: {successful_predictions}/{len(test_dates)} successful")
        return results
    
    def generate_test_dates(self, start_date: str, end_date: str) -> List[str]:
        """
        Generate list of test dates for validation
        
        Args:
            start_date: Start date
            end_date: End date
            
        Returns:
            List of test dates
        """
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        test_dates = []
        current_dt = start_dt
        
        while current_dt <= end_dt:
            # Skip weekends (assuming market is closed)
            if current_dt.weekday() < 5:  # Monday=0, Friday=4
                test_dates.append(current_dt.strftime('%Y-%m-%d'))
            
            current_dt += timedelta(days=1)
        
        # Sample every few days to make validation manageable
        sampled_dates = test_dates[::3]  # Every 3rd trading day
        
        return sampled_dates
    
    def get_actual_outcome(self, index_name: str, test_date: str) -> Optional[Dict[str, Any]]:
        """
        Get actual market outcome for a test date
        
        Args:
            index_name: Name of the index
            test_date: Date to get outcome for
            
        Returns:
            Dictionary with actual outcome or None if not available
        """
        try:
            # Get tick data for the test date
            tick_data = self.storage.get_tick_data(index_name, test_date, test_date)
            
            if len(tick_data) == 0:
                return None
            
            # Calculate actual metrics
            opening_price = tick_data['price'].iloc[0]
            closing_price = tick_data['price'].iloc[-1]
            high_price = tick_data['price'].max()
            low_price = tick_data['price'].min()
            
            price_change = closing_price - opening_price
            price_change_pct = (price_change / opening_price) * 100
            
            # Determine actual direction
            if price_change_pct > 0.1:
                actual_direction = 'UP'
            elif price_change_pct < -0.1:
                actual_direction = 'DOWN'
            else:
                actual_direction = 'NEUTRAL'
            
            return {
                'date': test_date,
                'opening_price': opening_price,
                'closing_price': closing_price,
                'high_price': high_price,
                'low_price': low_price,
                'price_change': price_change,
                'price_change_pct': price_change_pct,
                'actual_direction': actual_direction,
                'volatility': tick_data['price'].std(),
                'volume_total': tick_data['volume'].sum(),
                'tick_count': len(tick_data)
            }
            
        except Exception as e:
            logger.error(f"Error getting actual outcome for {test_date}: {e}")
            return None
    
    def evaluate_prediction(self, prediction: Dict[str, Any], 
                          actual: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate a single prediction against actual outcome
        
        Args:
            prediction: Prediction dictionary
            actual: Actual outcome dictionary
            
        Returns:
            Dictionary with evaluation metrics
        """
        evaluation = {
            'prediction_date': prediction['target_date'],
            'predicted_direction': prediction['direction'],
            'actual_direction': actual['actual_direction'],
            'predicted_price': prediction.get('predicted_price'),
            'actual_closing_price': actual['closing_price'],
            'confidence': prediction['confidence'],
            'magnitude': prediction['magnitude']
        }
        
        # Direction accuracy
        evaluation['direction_correct'] = (
            prediction['direction'] == actual['actual_direction']
        )
        
        # Price accuracy (if price prediction available)
        if 'predicted_price' in prediction and prediction['predicted_price']:
            price_error = abs(prediction['predicted_price'] - actual['closing_price'])
            price_error_pct = (price_error / actual['closing_price']) * 100
            
            evaluation['price_error'] = price_error
            evaluation['price_error_pct'] = price_error_pct
            evaluation['price_accuracy'] = max(0, 100 - price_error_pct)
        
        # Magnitude accuracy
        actual_magnitude = abs(actual['price_change_pct']) / 100
        magnitude_error = abs(prediction['magnitude'] - actual_magnitude)
        evaluation['magnitude_error'] = magnitude_error
        evaluation['magnitude_accuracy'] = max(0, 1 - magnitude_error)
        
        # Overall score (weighted combination)
        direction_weight = 0.5
        magnitude_weight = 0.3
        confidence_weight = 0.2
        
        direction_score = 1.0 if evaluation['direction_correct'] else 0.0
        magnitude_score = evaluation['magnitude_accuracy']
        confidence_score = prediction['confidence']
        
        evaluation['overall_score'] = (
            direction_weight * direction_score +
            magnitude_weight * magnitude_score +
            confidence_weight * confidence_score
        )
        
        return evaluation
    
    def calculate_performance_metrics(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate overall performance metrics from all predictions
        
        Args:
            predictions: List of prediction results
            
        Returns:
            Dictionary with performance metrics
        """
        # Filter successful predictions with evaluations
        valid_predictions = [
            p for p in predictions 
            if 'evaluation' in p and 'direction_correct' in p['evaluation']
        ]
        
        if not valid_predictions:
            return {'error': 'No valid predictions to evaluate'}
        
        evaluations = [p['evaluation'] for p in valid_predictions]
        
        # Direction accuracy
        direction_correct = sum(1 for e in evaluations if e['direction_correct'])
        direction_accuracy = direction_correct / len(evaluations)
        
        # Average scores
        avg_overall_score = np.mean([e['overall_score'] for e in evaluations])
        avg_confidence = np.mean([e['confidence'] for e in evaluations])
        avg_magnitude_accuracy = np.mean([e['magnitude_accuracy'] for e in evaluations])
        
        # Price accuracy (if available)
        price_evaluations = [e for e in evaluations if 'price_accuracy' in e]
        avg_price_accuracy = np.mean([e['price_accuracy'] for e in price_evaluations]) if price_evaluations else None
        
        # Confidence vs accuracy correlation
        confidences = [e['confidence'] for e in evaluations]
        direction_scores = [1.0 if e['direction_correct'] else 0.0 for e in evaluations]
        confidence_correlation = np.corrcoef(confidences, direction_scores)[0, 1] if len(confidences) > 1 else 0
        
        # Performance by confidence buckets
        high_conf_predictions = [e for e in evaluations if e['confidence'] > 0.8]
        medium_conf_predictions = [e for e in evaluations if 0.6 <= e['confidence'] <= 0.8]
        low_conf_predictions = [e for e in evaluations if e['confidence'] < 0.6]
        
        performance_by_confidence = {
            'high_confidence': {
                'count': len(high_conf_predictions),
                'accuracy': np.mean([1.0 if e['direction_correct'] else 0.0 for e in high_conf_predictions]) if high_conf_predictions else 0
            },
            'medium_confidence': {
                'count': len(medium_conf_predictions),
                'accuracy': np.mean([1.0 if e['direction_correct'] else 0.0 for e in medium_conf_predictions]) if medium_conf_predictions else 0
            },
            'low_confidence': {
                'count': len(low_conf_predictions),
                'accuracy': np.mean([1.0 if e['direction_correct'] else 0.0 for e in low_conf_predictions]) if low_conf_predictions else 0
            }
        }
        
        return {
            'total_predictions': len(valid_predictions),
            'direction_accuracy': direction_accuracy,
            'direction_correct_count': direction_correct,
            'average_overall_score': avg_overall_score,
            'average_confidence': avg_confidence,
            'average_magnitude_accuracy': avg_magnitude_accuracy,
            'average_price_accuracy': avg_price_accuracy,
            'confidence_correlation': confidence_correlation,
            'performance_by_confidence': performance_by_confidence,
            'benchmark_comparison': {
                'random_baseline': 0.33,  # Random 3-way classification
                'improvement_over_random': direction_accuracy - 0.33
            }
        }
