#!/usr/bin/env python3
"""
Script to run real predictions and validation on processed data
"""
import sys
from pathlib import Path
import pandas as pd
import json
from datetime import datetime, timedelta
from sqlalchemy import text

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import get_config
from src.data.storage import TickDataStorage
from src.ml.prediction_engine import PredictionEngine
from src.validation.backtester import PredictionBacktester
from src.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

def get_available_data_info(storage):
    """Get information about available data"""
    try:
        with storage.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT 
                    index_name,
                    MIN(DATE(timestamp)) as start_date,
                    MAX(DATE(timestamp)) as end_date,
                    COUNT(*) as record_count
                FROM tick_data 
                GROUP BY index_name
                ORDER BY index_name
            """))
            
            data_info = {}
            for row in result.fetchall():
                data_info[row[0]] = {
                    'start_date': row[1],
                    'end_date': row[2],
                    'record_count': row[3]
                }
            
            return data_info
    except Exception as e:
        logger.error(f"Error getting data info: {e}")
        return {}

def run_single_prediction_demo(storage, prediction_engine):
    """Run a single prediction demonstration"""
    logger.info("Running single prediction demonstration...")
    
    # Get available data
    data_info = get_available_data_info(storage)
    
    if not data_info:
        logger.error("No data available for prediction")
        return False
    
    # Select first available index
    index_name = list(data_info.keys())[0]
    index_info = data_info[index_name]
    
    logger.info(f"Using index: {index_name}")
    logger.info(f"Available data: {index_info['start_date']} to {index_info['end_date']}")
    logger.info(f"Record count: {index_info['record_count']:,}")
    
    # Calculate training and prediction dates
    end_date = datetime.strptime(index_info['end_date'], '%Y-%m-%d')
    predict_date = end_date.strftime('%Y-%m-%d')
    train_end = (end_date - timedelta(days=1)).strftime('%Y-%m-%d')
    train_start = (end_date - timedelta(days=20)).strftime('%Y-%m-%d')  # 20 days of training
    
    logger.info(f"Training period: {train_start} to {train_end}")
    logger.info(f"Prediction date: {predict_date}")
    
    try:
        # Generate prediction
        result = prediction_engine.train_and_predict(
            index_name, train_start, train_end, predict_date
        )
        
        # Display results
        logger.info("🎯 Prediction Results:")
        logger.info("=" * 50)
        
        prediction = result['predictions'][0]
        logger.info(f"Index: {prediction['index_name']}")
        logger.info(f"Target Date: {prediction['target_date']}")
        logger.info(f"Direction: {prediction['direction']}")
        logger.info(f"Confidence: {prediction['confidence']:.3f}")
        logger.info(f"Magnitude: {prediction['magnitude']:.3f}")
        logger.info(f"Current Price: {prediction['current_price']:.2f}")
        logger.info(f"Predicted Price: {prediction['predicted_price']:.2f}")
        logger.info(f"Expected Change: {prediction['price_change_pct']:.2f}%")
        
        logger.info("\n📊 Key Levels:")
        levels = prediction['key_levels']
        logger.info(f"Support: {[f'{s:.2f}' for s in levels['support']]}")
        logger.info(f"Resistance: {[f'{r:.2f}' for r in levels['resistance']]}")
        
        logger.info("\n🔧 Model Performance:")
        perf = result['model_performance']
        logger.info(f"Training samples: {result['training_samples']:,}")
        logger.info(f"Direction model loss: {perf['direction_model']['final_train_loss']:.4f}")
        logger.info(f"Price model loss: {perf['price_model']['final_train_loss']:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"Prediction failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_validation_demo(storage, backtester):
    """Run validation demonstration"""
    logger.info("Running validation demonstration...")
    
    # Get available data
    data_info = get_available_data_info(storage)
    
    if not data_info:
        logger.error("No data available for validation")
        return False
    
    # Select index with most data
    best_index = max(data_info.keys(), key=lambda k: data_info[k]['record_count'])
    index_info = data_info[best_index]
    
    logger.info(f"Using index: {best_index}")
    logger.info(f"Available data: {index_info['start_date']} to {index_info['end_date']}")
    
    # Calculate validation period (use last part of available data)
    end_date = datetime.strptime(index_info['end_date'], '%Y-%m-%d')
    start_date = datetime.strptime(index_info['start_date'], '%Y-%m-%d')
    
    # Use last 10 days for validation, previous data for training
    validation_start = (end_date - timedelta(days=10)).strftime('%Y-%m-%d')
    validation_end = (end_date - timedelta(days=1)).strftime('%Y-%m-%d')
    
    logger.info(f"Validation period: {validation_start} to {validation_end}")
    
    try:
        # Run walk-forward validation
        results = backtester.run_walk_forward_validation(
            best_index, validation_start, validation_end, train_window_days=15
        )
        
        # Display results
        logger.info("📈 Validation Results:")
        logger.info("=" * 50)
        
        summary = results['summary']
        logger.info(f"Total tests: {summary['total_tests']}")
        logger.info(f"Successful predictions: {summary['successful_predictions']}")
        logger.info(f"Failed predictions: {summary['failed_predictions']}")
        logger.info(f"Success rate: {summary['success_rate']:.1%}")
        
        if 'performance_metrics' in results and 'direction_accuracy' in results['performance_metrics']:
            metrics = results['performance_metrics']
            logger.info(f"\n🎯 Performance Metrics:")
            logger.info(f"Direction accuracy: {metrics['direction_accuracy']:.1%}")
            logger.info(f"Average confidence: {metrics['average_confidence']:.3f}")
            logger.info(f"Average overall score: {metrics['average_overall_score']:.3f}")
            logger.info(f"Improvement over random: {metrics['benchmark_comparison']['improvement_over_random']:.1%}")
            
            # Performance by confidence
            conf_perf = metrics['performance_by_confidence']
            logger.info(f"\n📊 Performance by Confidence:")
            logger.info(f"High confidence (>0.8): {conf_perf['high_confidence']['accuracy']:.1%} ({conf_perf['high_confidence']['count']} predictions)")
            logger.info(f"Medium confidence (0.6-0.8): {conf_perf['medium_confidence']['accuracy']:.1%} ({conf_perf['medium_confidence']['count']} predictions)")
            logger.info(f"Low confidence (<0.6): {conf_perf['low_confidence']['accuracy']:.1%} ({conf_perf['low_confidence']['count']} predictions)")
        
        # Save detailed results
        results_file = f"validation_results_{best_index}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"\n💾 Detailed results saved to: {results_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"Validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    logger.info("🚀 Starting Predictions and Validation Demo")
    
    try:
        # Setup
        config = get_config()
        
        # Use the test database from our previous demo
        db_path = "test_tickdata.db"
        connection_string = f"sqlite:///{db_path}"
        
        # Check if database exists
        if not Path(db_path).exists():
            logger.error(f"Database {db_path} not found. Please run process_sample_data.py first.")
            return False
        
        # Initialize components with SQLite connection
        storage = TickDataStorage(connection_string)
        prediction_engine = PredictionEngine(config, connection_string)
        backtester = PredictionBacktester(config, connection_string)
        
        # Step 1: Show available data
        logger.info("Step 1: Analyzing available data")
        data_info = get_available_data_info(storage)
        
        if not data_info:
            logger.error("No data available")
            return False
        
        logger.info("📊 Available Data:")
        total_records = 0
        for index_name, info in data_info.items():
            logger.info(f"  {index_name}: {info['record_count']:,} records ({info['start_date']} to {info['end_date']})")
            total_records += info['record_count']
        
        logger.info(f"Total records: {total_records:,}")
        
        # Step 2: Run single prediction demo
        logger.info("\nStep 2: Single prediction demonstration")
        if not run_single_prediction_demo(storage, prediction_engine):
            logger.error("Single prediction demo failed")
            return False
        
        # Step 3: Run validation demo
        logger.info("\nStep 3: Validation demonstration")
        if not run_validation_demo(storage, backtester):
            logger.error("Validation demo failed")
            return False
        
        logger.info("\n🎉 All demonstrations completed successfully!")
        logger.info("\nKey Achievements:")
        logger.info("✅ Real model training on historical data")
        logger.info("✅ Actual predictions with confidence scores")
        logger.info("✅ Walk-forward validation methodology")
        logger.info("✅ Performance metrics and benchmarking")
        logger.info("✅ Support/resistance level calculation")
        
        return True
        
    except Exception as e:
        logger.error(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
