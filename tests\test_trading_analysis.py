#!/usr/bin/env python3
"""
Test suite for Trading Analysis Components
Tests professional trading strategy, signal generation, and analysis
"""
import pytest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.data.storage import TickDataStorage
from src.strategy.professional_trader import ProfessionalTradingStrategy
from src.ml.features import FeatureEngineering
from config import get_config

class TestProfessionalTradingStrategy:
    """Test suite for ProfessionalTradingStrategy"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test database"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def test_storage(self, temp_dir):
        """Create test database with sample data"""
        db_path = temp_dir / "test_trading.db"
        storage = TickDataStorage(f"sqlite:///{db_path}")
        storage.init_database()
        
        # Create sample tick data
        sample_data = self._create_sample_tick_data()
        storage.store_tick_data(sample_data)
        
        return storage
    
    @pytest.fixture
    def trading_strategy(self, test_storage):
        """Create trading strategy instance"""
        return ProfessionalTradingStrategy(test_storage)
    
    def _create_sample_tick_data(self):
        """Create realistic sample tick data for testing"""
        # Generate 1000 ticks over a trading day
        base_time = datetime(2025, 7, 14, 9, 15, 0)
        timestamps = [base_time + timedelta(seconds=i*2) for i in range(1000)]
        
        # Generate realistic price movement
        base_price = 25170.0
        prices = []
        volumes = []
        
        current_price = base_price
        for i in range(1000):
            # Add some realistic price movement
            change = np.random.normal(0, 0.5)  # Small random changes
            current_price += change
            prices.append(round(current_price, 2))
            
            # Generate volume with some spikes
            if i % 100 == 0:  # Volume spike every 100 ticks
                volume = np.random.randint(500, 2000)
            else:
                volume = np.random.randint(50, 300)
            volumes.append(volume)
        
        return pd.DataFrame({
            'timestamp': timestamps,
            'index_name': 'nifty',
            'price': prices,
            'volume': volumes
        })
    
    def test_analyze_market_structure(self, trading_strategy):
        """Test market structure analysis"""
        analysis = trading_strategy.analyze_market_structure('nifty', '2025-07-14')
        
        assert analysis is not None
        assert isinstance(analysis, dict)
        
        # Check required analysis components
        expected_keys = ['volume_analysis', 'price_levels', 'market_character', 'order_flow']
        for key in expected_keys:
            assert key in analysis, f"Missing key: {key}"
        
        # Test volume analysis
        volume_analysis = analysis['volume_analysis']
        assert 'percentile_75' in volume_analysis
        assert 'percentile_90' in volume_analysis
        assert 'percentile_95' in volume_analysis
        assert volume_analysis['percentile_75'] > 0
        
        # Test price levels
        price_levels = analysis['price_levels']
        assert 'current_price' in price_levels
        assert 'support' in price_levels
        assert 'resistance' in price_levels
        assert price_levels['current_price'] > 0
    
    def test_generate_trading_signals(self, trading_strategy):
        """Test trading signal generation"""
        # First get market analysis
        analysis = trading_strategy.analyze_market_structure('nifty', '2025-07-14')
        assert analysis is not None
        
        # Generate signals with different confidence thresholds
        signals_low = trading_strategy.generate_trading_signals(analysis, confidence_threshold=0.5)
        signals_high = trading_strategy.generate_trading_signals(analysis, confidence_threshold=0.8)
        
        # Signals should be lists
        assert isinstance(signals_low, list)
        assert isinstance(signals_high, list)
        
        # Higher confidence threshold should produce fewer or equal signals
        assert len(signals_high) <= len(signals_low)
        
        # Test signal structure if signals exist
        if signals_low:
            signal = signals_low[0]
            required_fields = ['direction', 'entry_price', 'target_price', 'stop_loss', 'confidence', 'volume_requirement']
            for field in required_fields:
                assert field in signal, f"Missing signal field: {field}"
            
            # Test signal values
            assert signal['direction'] in ['long', 'short']
            assert signal['entry_price'] > 0
            assert signal['target_price'] > 0
            assert signal['stop_loss'] > 0
            assert 0 <= signal['confidence'] <= 1
            assert signal['volume_requirement'] > 0
    
    def test_volume_breakout_analysis(self, trading_strategy):
        """Test volume breakout analysis functionality"""
        analysis = trading_strategy.analyze_market_structure('nifty', '2025-07-14')
        
        volume_analysis = analysis['volume_analysis']
        
        # Test volume percentiles are in ascending order
        assert volume_analysis['percentile_75'] <= volume_analysis['percentile_90']
        assert volume_analysis['percentile_90'] <= volume_analysis['percentile_95']
        
        # Test volume statistics
        assert 'average_volume' in volume_analysis
        assert 'median_volume' in volume_analysis
        assert volume_analysis['average_volume'] > 0
        assert volume_analysis['median_volume'] > 0
    
    def test_support_resistance_identification(self, trading_strategy):
        """Test support and resistance level identification"""
        analysis = trading_strategy.analyze_market_structure('nifty', '2025-07-14')
        
        price_levels = analysis['price_levels']
        
        # Support should be below current price, resistance above
        current_price = price_levels['current_price']
        support = price_levels['support']
        resistance = price_levels['resistance']
        
        # Basic sanity checks (may not always hold in all market conditions)
        assert support > 0
        assert resistance > 0
        assert current_price > 0
        
        # Test that levels are reasonable (within 10% of current price)
        price_range = current_price * 0.1
        assert abs(support - current_price) <= price_range * 2
        assert abs(resistance - current_price) <= price_range * 2
    
    def test_order_flow_analysis(self, trading_strategy):
        """Test order flow analysis"""
        analysis = trading_strategy.analyze_market_structure('nifty', '2025-07-14')
        
        order_flow = analysis['order_flow']
        
        # Test order flow components
        assert 'buying_pressure' in order_flow
        assert 'selling_pressure' in order_flow
        assert 'net_flow' in order_flow
        
        # Test that pressures are numeric
        assert isinstance(order_flow['buying_pressure'], (int, float))
        assert isinstance(order_flow['selling_pressure'], (int, float))
        assert isinstance(order_flow['net_flow'], (int, float))
    
    def test_market_character_assessment(self, trading_strategy):
        """Test market character assessment"""
        analysis = trading_strategy.analyze_market_structure('nifty', '2025-07-14')
        
        market_character = analysis['market_character']
        
        # Test market character components
        expected_fields = ['volatility', 'trend', 'phase', 'volume_profile']
        for field in expected_fields:
            assert field in market_character, f"Missing market character field: {field}"
        
        # Test volatility is numeric and positive
        assert isinstance(market_character['volatility'], (int, float))
        assert market_character['volatility'] >= 0
        
        # Test trend is valid
        assert market_character['trend'] in ['bullish', 'bearish', 'sideways']
        
        # Test phase is valid
        assert market_character['phase'] in ['accumulation', 'distribution', 'trending', 'consolidation']
    
    def test_risk_reward_calculation(self, trading_strategy):
        """Test risk/reward ratio calculations in signals"""
        analysis = trading_strategy.analyze_market_structure('nifty', '2025-07-14')
        signals = trading_strategy.generate_trading_signals(analysis, confidence_threshold=0.6)
        
        if signals:
            for signal in signals:
                entry = signal['entry_price']
                target = signal['target_price']
                stop = signal['stop_loss']
                
                # Calculate risk and reward
                if signal['direction'] == 'long':
                    risk = entry - stop
                    reward = target - entry
                else:  # short
                    risk = stop - entry
                    reward = entry - target
                
                # Risk and reward should be positive
                assert risk > 0, f"Risk should be positive: {risk}"
                assert reward > 0, f"Reward should be positive: {reward}"
                
                # Risk/reward ratio should be reasonable (not too extreme)
                rr_ratio = reward / risk if risk > 0 else 0
                assert 0.5 <= rr_ratio <= 5.0, f"Risk/reward ratio out of range: {rr_ratio}"
    
    def test_confidence_scoring(self, trading_strategy):
        """Test signal confidence scoring"""
        analysis = trading_strategy.analyze_market_structure('nifty', '2025-07-14')
        signals = trading_strategy.generate_trading_signals(analysis, confidence_threshold=0.3)
        
        if signals:
            for signal in signals:
                confidence = signal['confidence']
                
                # Confidence should be between 0 and 1
                assert 0 <= confidence <= 1, f"Confidence out of range: {confidence}"
                
                # Higher confidence signals should have better risk/reward
                if confidence > 0.8:
                    entry = signal['entry_price']
                    target = signal['target_price']
                    stop = signal['stop_loss']
                    
                    if signal['direction'] == 'long':
                        rr_ratio = (target - entry) / (entry - stop)
                    else:
                        rr_ratio = (entry - target) / (stop - entry)
                    
                    # High confidence signals should have decent risk/reward
                    assert rr_ratio >= 0.8, f"High confidence signal has poor R/R: {rr_ratio}"
    
    def test_no_data_handling(self, trading_strategy):
        """Test handling of missing data"""
        # Test with non-existent index
        analysis = trading_strategy.analyze_market_structure('nonexistent', '2025-07-14')
        assert analysis is None or analysis == {}
        
        # Test with non-existent date
        analysis = trading_strategy.analyze_market_structure('nifty', '2025-01-01')
        assert analysis is None or analysis == {}
    
    def test_multiple_indices(self, test_storage, trading_strategy):
        """Test analysis with multiple indices"""
        # Add data for another index
        sample_data = self._create_sample_tick_data()
        sample_data['index_name'] = 'bank_nifty'
        sample_data['price'] = sample_data['price'] * 2.3  # Different price level
        test_storage.store_tick_data(sample_data)
        
        # Test analysis for both indices
        nifty_analysis = trading_strategy.analyze_market_structure('nifty', '2025-07-14')
        bank_nifty_analysis = trading_strategy.analyze_market_structure('bank_nifty', '2025-07-14')
        
        assert nifty_analysis is not None
        assert bank_nifty_analysis is not None
        
        # Price levels should be different
        nifty_price = nifty_analysis['price_levels']['current_price']
        bank_nifty_price = bank_nifty_analysis['price_levels']['current_price']
        
        assert abs(nifty_price - bank_nifty_price) > 1000  # Should be significantly different

class TestFeatureEngineering:
    """Test suite for FeatureEngineering"""
    
    @pytest.fixture
    def feature_engine(self):
        """Create feature engineering instance"""
        return FeatureEngineering()
    
    @pytest.fixture
    def sample_tick_data(self):
        """Create sample tick data for feature extraction"""
        timestamps = pd.date_range('2025-07-14 09:15:00', periods=100, freq='2S')
        
        # Generate realistic price and volume data
        prices = 25170 + np.cumsum(np.random.normal(0, 0.1, 100))
        volumes = np.random.randint(50, 500, 100)
        
        return pd.DataFrame({
            'timestamp': timestamps,
            'price': prices,
            'volume': volumes,
            'index_name': 'nifty'
        })
    
    def test_extract_all_features(self, feature_engine, sample_tick_data):
        """Test comprehensive feature extraction"""
        features = feature_engine.extract_all_features(sample_tick_data)
        
        assert isinstance(features, pd.DataFrame)
        assert len(features) == len(sample_tick_data)
        assert len(features.columns) > 10  # Should extract multiple features
        
        # Check for no NaN values in most features (some may have NaN for first few rows)
        nan_ratio = features.isnull().sum().sum() / (len(features) * len(features.columns))
        assert nan_ratio < 0.1, f"Too many NaN values: {nan_ratio:.2%}"
    
    def test_volume_features(self, feature_engine, sample_tick_data):
        """Test volume-based feature extraction"""
        features = feature_engine.extract_all_features(sample_tick_data)
        
        # Check for volume-related features
        volume_features = [col for col in features.columns if 'volume' in col.lower()]
        assert len(volume_features) > 0, "No volume features found"
        
        # Test that volume features are numeric
        for feature in volume_features:
            assert features[feature].dtype in ['float64', 'int64'], f"Non-numeric volume feature: {feature}"
    
    def test_price_features(self, feature_engine, sample_tick_data):
        """Test price-based feature extraction"""
        features = feature_engine.extract_all_features(sample_tick_data)
        
        # Check for price-related features
        price_features = [col for col in features.columns if any(word in col.lower() 
                         for word in ['price', 'momentum', 'return', 'volatility'])]
        assert len(price_features) > 0, "No price features found"
        
        # Test that price features are numeric
        for feature in price_features:
            assert features[feature].dtype in ['float64', 'int64'], f"Non-numeric price feature: {feature}"

if __name__ == '__main__':
    pytest.main([__file__, '-v'])
