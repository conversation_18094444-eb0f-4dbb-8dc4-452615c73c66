"""
Tests for feature engineering functionality
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.ml.features import FeatureEngineering

class TestFeatureEngineering:
    """Test cases for FeatureEngineering class"""
    
    def test_initialization(self):
        """Test FeatureEngineering initialization"""
        fe = FeatureEngineering()
        
        # Check that feature registry is populated
        assert len(fe.feature_registry) > 0
        
        # Check that expected features are registered
        expected_features = [
            'volume_delta', 'relative_volume', 'volume_imbalance',
            'price_momentum', 'price_reversal', 'tick_direction'
        ]
        
        for feature in expected_features:
            assert feature in fe.feature_registry
    
    def test_calculate_volume_delta(self, sample_tick_data):
        """Test volume delta calculation"""
        fe = FeatureEngineering()
        
        # Calculate volume delta
        result = fe.calculate_volume_delta(sample_tick_data, window=5)
        
        # Check result properties
        assert isinstance(result, pd.Series)
        assert len(result) == len(sample_tick_data)
        
        # Check that first few values are NaN (due to window)
        assert pd.isna(result.iloc[:5]).all()
        
        # Check that later values are not NaN
        assert not pd.isna(result.iloc[10:]).all()
    
    def test_calculate_relative_volume(self, sample_tick_data):
        """Test relative volume calculation"""
        fe = FeatureEngineering()
        
        # Calculate relative volume
        result = fe.calculate_relative_volume(sample_tick_data, window=20)
        
        # Check result properties
        assert isinstance(result, pd.Series)
        assert len(result) == len(sample_tick_data)
        
        # Check that values are positive (volume ratio)
        valid_values = result.dropna()
        assert (valid_values > 0).all()
    
    def test_calculate_volume_imbalance(self, sample_tick_data):
        """Test volume imbalance calculation"""
        fe = FeatureEngineering()
        
        # Calculate volume imbalance
        result = fe.calculate_volume_imbalance(sample_tick_data, window=10)
        
        # Check result properties
        assert isinstance(result, pd.Series)
        assert len(result) == len(sample_tick_data)
        
        # Check that values are between -1 and 1
        valid_values = result.dropna()
        assert (valid_values >= -1).all()
        assert (valid_values <= 1).all()
    
    def test_calculate_price_momentum(self, sample_tick_data):
        """Test price momentum calculation"""
        fe = FeatureEngineering()
        
        # Calculate price momentum
        result = fe.calculate_price_momentum(sample_tick_data, window=10)
        
        # Check result properties
        assert isinstance(result, pd.Series)
        assert len(result) == len(sample_tick_data)
        
        # Check that first few values are NaN
        assert pd.isna(result.iloc[:10]).all()
    
    def test_detect_price_reversal(self, sample_tick_data):
        """Test price reversal detection"""
        fe = FeatureEngineering()
        
        # Detect price reversals
        result = fe.detect_price_reversal(sample_tick_data, window=5)
        
        # Check result properties
        assert isinstance(result, pd.Series)
        assert len(result) == len(sample_tick_data)
        
        # Check that values are in {-1, 0, 1}
        valid_values = result.dropna()
        assert valid_values.isin([-1, 0, 1]).all()
    
    def test_calculate_tick_direction(self, sample_tick_data):
        """Test tick direction calculation"""
        fe = FeatureEngineering()
        
        # Calculate tick direction
        result = fe.calculate_tick_direction(sample_tick_data, window=10)
        
        # Check result properties
        assert isinstance(result, pd.Series)
        assert len(result) == len(sample_tick_data)
        
        # Check that values are between -1 and 1
        valid_values = result.dropna()
        assert (valid_values >= -1).all()
        assert (valid_values <= 1).all()
    
    def test_extract_time_of_day(self, sample_tick_data):
        """Test time of day feature extraction"""
        fe = FeatureEngineering()
        
        # Extract time of day features
        result = fe.extract_time_of_day(sample_tick_data)
        
        # Check result properties
        assert isinstance(result, pd.DataFrame)
        assert len(result) == len(sample_tick_data)
        
        # Check expected columns
        expected_columns = ['normalized_time', 'opening_phase', 'lunch_phase', 'closing_phase']
        assert list(result.columns) == expected_columns
        
        # Check value ranges
        assert (result['normalized_time'] >= 0).all()
        assert (result['opening_phase'].isin([0, 1])).all()
        assert (result['lunch_phase'].isin([0, 1])).all()
        assert (result['closing_phase'].isin([0, 1])).all()
    
    def test_extract_tick_pattern(self, sample_tick_data):
        """Test tick pattern extraction"""
        fe = FeatureEngineering()
        
        # Extract tick patterns
        result = fe.extract_tick_pattern(sample_tick_data, window=3)
        
        # Check result properties
        assert isinstance(result, pd.DataFrame)
        assert len(result) == len(sample_tick_data)
        
        # Check expected columns
        expected_columns = ['consecutive_up', 'consecutive_down', 'up_exhaustion', 'down_exhaustion']
        assert list(result.columns) == expected_columns
        
        # Check that exhaustion signals are binary
        assert result['up_exhaustion'].isin([0, 1]).all()
        assert result['down_exhaustion'].isin([0, 1]).all()
    
    def test_identify_support_resistance(self, sample_tick_data):
        """Test support/resistance identification"""
        fe = FeatureEngineering()
        
        # Identify support/resistance
        result = fe.identify_support_resistance(sample_tick_data, window=20)
        
        # Check result properties
        assert isinstance(result, pd.DataFrame)
        assert len(result) == len(sample_tick_data)
        
        # Check expected columns
        expected_columns = [
            'distance_to_resistance', 'distance_to_support',
            'near_resistance', 'near_support',
            'resistance_break', 'support_break'
        ]
        assert list(result.columns) == expected_columns
        
        # Check that binary columns are indeed binary
        binary_columns = ['near_resistance', 'near_support', 'resistance_break', 'support_break']
        for col in binary_columns:
            assert result[col].isin([0, 1]).all()
    
    def test_extract_all_features(self, sample_tick_data):
        """Test extraction of all features"""
        fe = FeatureEngineering()
        
        # Extract all features
        result = fe.extract_all_features(sample_tick_data)
        
        # Check result properties
        assert isinstance(result, pd.DataFrame)
        assert len(result) == len(sample_tick_data)
        
        # Check that we have multiple features
        assert len(result.columns) > 10
        
        # Check that feature names match expected patterns
        feature_prefixes = [
            'volume_delta', 'relative_volume', 'volume_imbalance',
            'price_momentum', 'price_reversal', 'tick_direction'
        ]
        
        for prefix in feature_prefixes:
            matching_columns = [col for col in result.columns if col.startswith(prefix)]
            assert len(matching_columns) > 0, f"No columns found for feature {prefix}"
    
    def test_feature_extraction_with_insufficient_data(self):
        """Test feature extraction with insufficient data"""
        fe = FeatureEngineering()
        
        # Create minimal dataset
        minimal_data = pd.DataFrame({
            'timestamp': [datetime.now()],
            'price': [19500.0],
            'volume': [1000],
            'index_name': ['test']
        })
        
        # Extract features
        result = fe.extract_all_features(minimal_data)
        
        # Should not crash and return DataFrame with correct shape
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 1
    
    def test_feature_extraction_with_constant_prices(self):
        """Test feature extraction with constant prices"""
        fe = FeatureEngineering()
        
        # Create data with constant prices
        n_points = 100
        constant_data = pd.DataFrame({
            'timestamp': pd.date_range('2025-07-15 09:15:00', periods=n_points, freq='2S'),
            'price': [19500.0] * n_points,  # Constant price
            'volume': np.random.randint(100, 1000, n_points),
            'index_name': ['test'] * n_points
        })
        
        # Extract features
        result = fe.extract_all_features(constant_data)
        
        # Should handle constant prices gracefully
        assert isinstance(result, pd.DataFrame)
        assert len(result) == n_points
        
        # Price momentum should be zero for constant prices
        if 'price_momentum' in result.columns:
            momentum_values = result['price_momentum'].dropna()
            if len(momentum_values) > 0:
                assert (momentum_values == 0).all()
    
    def test_feature_extraction_with_missing_values(self):
        """Test feature extraction with missing values in input"""
        fe = FeatureEngineering()
        
        # Create data with some missing values
        data_with_na = pd.DataFrame({
            'timestamp': pd.date_range('2025-07-15 09:15:00', periods=50, freq='2S'),
            'price': [19500.0 + i*0.1 if i % 5 != 0 else np.nan for i in range(50)],
            'volume': [1000 + i*10 if i % 7 != 0 else np.nan for i in range(50)],
            'index_name': ['test'] * 50
        })
        
        # Extract features
        result = fe.extract_all_features(data_with_na)
        
        # Should handle missing values gracefully
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 50
    
    def test_individual_feature_functions_with_edge_cases(self):
        """Test individual feature functions with edge cases"""
        fe = FeatureEngineering()
        
        # Test with single data point
        single_point = pd.DataFrame({
            'timestamp': [datetime.now()],
            'price': [19500.0],
            'volume': [1000],
            'index_name': ['test']
        })
        
        # These should not crash
        fe.calculate_volume_delta(single_point, window=1)
        fe.calculate_price_momentum(single_point, window=1)
        fe.calculate_tick_direction(single_point, window=1)
        
        # Test with empty DataFrame
        empty_df = pd.DataFrame(columns=['timestamp', 'price', 'volume', 'index_name'])
        
        # Should handle empty data gracefully
        result = fe.extract_time_of_day(empty_df)
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 0
