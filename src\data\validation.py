"""
Data validation utilities for tick data analysis platform
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass

from src.utils.logging import get_logger, get_error_logger

logger = get_logger(__name__)
error_logger = get_error_logger()

@dataclass
class ValidationResult:
    """Result of data validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    stats: Dict[str, Any]

class TickDataValidator:
    """Validates tick data for quality and consistency"""
    
    def __init__(self, config=None):
        """
        Initialize validator
        
        Args:
            config: Configuration object with validation parameters
        """
        self.config = config
        
        # Default validation parameters
        self.max_price_change_percent = getattr(config.data, 'max_price_change_percent', 10.0) if config else 10.0
        self.min_volume = getattr(config.data, 'min_volume', 1) if config else 1
        self.max_volume = getattr(config.data, 'max_volume', 1000000) if config else 1000000
        
        # Market hours (IST)
        self.market_open = (9, 15)  # 9:15 AM
        self.market_close = (15, 30)  # 3:30 PM
    
    def validate_tick_data(self, df: pd.DataFrame, index_name: str = None) -> ValidationResult:
        """
        Comprehensive validation of tick data
        
        Args:
            df: DataFrame containing tick data
            index_name: Name of the index (for context)
            
        Returns:
            ValidationResult with validation details
        """
        errors = []
        warnings = []
        stats = {}
        
        try:
            # Basic structure validation
            structure_errors = self._validate_structure(df)
            errors.extend(structure_errors)
            
            if not structure_errors:  # Only proceed if structure is valid
                # Data quality validation
                quality_errors, quality_warnings = self._validate_data_quality(df)
                errors.extend(quality_errors)
                warnings.extend(quality_warnings)
                
                # Business logic validation
                business_errors, business_warnings = self._validate_business_logic(df)
                errors.extend(business_errors)
                warnings.extend(business_warnings)
                
                # Time series validation
                time_errors, time_warnings = self._validate_time_series(df)
                errors.extend(time_errors)
                warnings.extend(time_warnings)
                
                # Calculate statistics
                stats = self._calculate_stats(df)
            
            is_valid = len(errors) == 0
            
            # Log validation results
            if is_valid:
                logger.info(f"Data validation passed for {index_name or 'unknown'}: {len(df)} records")
            else:
                error_logger.log_validation_error(
                    f"tick_data_{index_name or 'unknown'}", 
                    {"errors": errors, "warnings": warnings}
                )
            
            return ValidationResult(
                is_valid=is_valid,
                errors=errors,
                warnings=warnings,
                stats=stats
            )
            
        except Exception as e:
            error_logger.log_exception("data_validation", e, index_name=index_name)
            return ValidationResult(
                is_valid=False,
                errors=[f"Validation failed with exception: {str(e)}"],
                warnings=[],
                stats={}
            )
    
    def _validate_structure(self, df: pd.DataFrame) -> List[str]:
        """Validate DataFrame structure"""
        errors = []
        
        # Check if DataFrame is empty
        if df.empty:
            errors.append("DataFrame is empty")
            return errors
        
        # Check required columns
        required_columns = ['timestamp', 'price', 'volume']
        missing_columns = set(required_columns) - set(df.columns)
        if missing_columns:
            errors.append(f"Missing required columns: {missing_columns}")
        
        # Check data types
        if 'timestamp' in df.columns:
            if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
                errors.append("timestamp column must be datetime type")
        
        if 'price' in df.columns:
            if not pd.api.types.is_numeric_dtype(df['price']):
                errors.append("price column must be numeric type")
        
        if 'volume' in df.columns:
            if not pd.api.types.is_numeric_dtype(df['volume']):
                errors.append("volume column must be numeric type")
        
        return errors
    
    def _validate_data_quality(self, df: pd.DataFrame) -> Tuple[List[str], List[str]]:
        """Validate data quality"""
        errors = []
        warnings = []
        
        # Check for null values
        null_counts = df.isnull().sum()
        for column, null_count in null_counts.items():
            if null_count > 0:
                if column in ['timestamp', 'price', 'volume']:
                    errors.append(f"Null values in critical column {column}: {null_count}")
                else:
                    warnings.append(f"Null values in column {column}: {null_count}")
        
        # Check for duplicate timestamps
        if 'timestamp' in df.columns:
            duplicate_timestamps = df['timestamp'].duplicated().sum()
            if duplicate_timestamps > 0:
                warnings.append(f"Duplicate timestamps found: {duplicate_timestamps}")
        
        # Check price values
        if 'price' in df.columns:
            # Negative prices
            negative_prices = (df['price'] <= 0).sum()
            if negative_prices > 0:
                errors.append(f"Non-positive price values: {negative_prices}")
            
            # Extreme price changes
            if len(df) > 1:
                price_changes = df['price'].pct_change().abs() * 100
                extreme_changes = (price_changes > self.max_price_change_percent).sum()
                if extreme_changes > 0:
                    warnings.append(f"Extreme price changes (>{self.max_price_change_percent}%): {extreme_changes}")
        
        # Check volume values
        if 'volume' in df.columns:
            # Negative volumes
            negative_volumes = (df['volume'] < 0).sum()
            if negative_volumes > 0:
                errors.append(f"Negative volume values: {negative_volumes}")
            
            # Volume range validation
            low_volumes = (df['volume'] < self.min_volume).sum()
            if low_volumes > 0:
                warnings.append(f"Volume below minimum ({self.min_volume}): {low_volumes}")
            
            high_volumes = (df['volume'] > self.max_volume).sum()
            if high_volumes > 0:
                warnings.append(f"Volume above maximum ({self.max_volume}): {high_volumes}")
        
        return errors, warnings
    
    def _validate_business_logic(self, df: pd.DataFrame) -> Tuple[List[str], List[str]]:
        """Validate business logic rules"""
        errors = []
        warnings = []
        
        if 'timestamp' not in df.columns:
            return errors, warnings
        
        # Check market hours
        df_copy = df.copy()
        df_copy['hour'] = df_copy['timestamp'].dt.hour
        df_copy['minute'] = df_copy['timestamp'].dt.minute
        
        # Define market hours check
        def is_market_hours(row):
            hour, minute = row['hour'], row['minute']
            start_minutes = self.market_open[0] * 60 + self.market_open[1]
            end_minutes = self.market_close[0] * 60 + self.market_close[1]
            current_minutes = hour * 60 + minute
            return start_minutes <= current_minutes <= end_minutes
        
        outside_market_hours = ~df_copy.apply(is_market_hours, axis=1)
        outside_count = outside_market_hours.sum()
        
        if outside_count > 0:
            warnings.append(f"Records outside market hours: {outside_count}")
        
        # Check for weekend data
        weekend_data = df['timestamp'].dt.dayofweek.isin([5, 6]).sum()  # Saturday=5, Sunday=6
        if weekend_data > 0:
            warnings.append(f"Weekend data found: {weekend_data} records")
        
        return errors, warnings
    
    def _validate_time_series(self, df: pd.DataFrame) -> Tuple[List[str], List[str]]:
        """Validate time series properties"""
        errors = []
        warnings = []
        
        if 'timestamp' not in df.columns or len(df) < 2:
            return errors, warnings
        
        # Check if data is sorted by timestamp
        if not df['timestamp'].is_monotonic_increasing:
            warnings.append("Data is not sorted by timestamp")
        
        # Check for large time gaps
        time_diffs = df['timestamp'].diff().dt.total_seconds()
        
        # Typical tick interval should be 1-10 seconds
        large_gaps = (time_diffs > 300).sum()  # More than 5 minutes
        if large_gaps > 0:
            warnings.append(f"Large time gaps (>5 min) found: {large_gaps}")
        
        # Check for very frequent ticks (potential data quality issue)
        very_frequent = (time_diffs < 0.1).sum()  # Less than 100ms
        if very_frequent > 0:
            warnings.append(f"Very frequent ticks (<100ms) found: {very_frequent}")
        
        return errors, warnings
    
    def _calculate_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate data statistics"""
        stats = {
            'record_count': len(df),
            'date_range': None,
            'price_stats': {},
            'volume_stats': {},
            'time_stats': {}
        }
        
        if 'timestamp' in df.columns and len(df) > 0:
            stats['date_range'] = {
                'start': df['timestamp'].min().isoformat(),
                'end': df['timestamp'].max().isoformat(),
                'duration_hours': (df['timestamp'].max() - df['timestamp'].min()).total_seconds() / 3600
            }
            
            # Time interval statistics
            if len(df) > 1:
                time_diffs = df['timestamp'].diff().dt.total_seconds().dropna()
                stats['time_stats'] = {
                    'mean_interval_seconds': time_diffs.mean(),
                    'median_interval_seconds': time_diffs.median(),
                    'min_interval_seconds': time_diffs.min(),
                    'max_interval_seconds': time_diffs.max()
                }
        
        if 'price' in df.columns:
            stats['price_stats'] = {
                'mean': df['price'].mean(),
                'median': df['price'].median(),
                'std': df['price'].std(),
                'min': df['price'].min(),
                'max': df['price'].max(),
                'range': df['price'].max() - df['price'].min()
            }
        
        if 'volume' in df.columns:
            stats['volume_stats'] = {
                'mean': df['volume'].mean(),
                'median': df['volume'].median(),
                'std': df['volume'].std(),
                'min': df['volume'].min(),
                'max': df['volume'].max(),
                'total': df['volume'].sum()
            }
        
        return stats

def validate_file_format(file_path: str) -> ValidationResult:
    """
    Validate tick data file format
    
    Args:
        file_path: Path to the file to validate
        
    Returns:
        ValidationResult
    """
    errors = []
    warnings = []
    stats = {}
    
    try:
        # Check file extension
        if not file_path.lower().endswith('.csv'):
            errors.append("File must be CSV format")
            return ValidationResult(False, errors, warnings, stats)
        
        # Try to read the file
        df = pd.read_csv(file_path, nrows=5)  # Read first 5 rows for validation
        
        # Check expected columns
        expected_columns = ['Time', 'Last Rate', 'Volume']
        missing_columns = set(expected_columns) - set(df.columns)
        if missing_columns:
            errors.append(f"Missing expected columns: {missing_columns}")
        
        # Check if file is empty
        full_df = pd.read_csv(file_path)
        if len(full_df) == 0:
            errors.append("File is empty")
        
        import os
        stats = {
            'file_size_mb': os.path.getsize(file_path) / (1024 * 1024),
            'record_count': len(full_df),
            'columns': list(df.columns)
        }
        
        is_valid = len(errors) == 0
        
        return ValidationResult(is_valid, errors, warnings, stats)
        
    except Exception as e:
        errors.append(f"Failed to read file: {str(e)}")
        return ValidationResult(False, errors, warnings, stats)
