<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Indian Market Tick Data Analysis Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            font-weight: bold;
            background-color: #f1f5f9;
        }
        .stats-value {
            font-size: 24px;
            font-weight: bold;
        }
        .stats-label {
            font-size: 14px;
            color: #6c757d;
        }
        .signal-card {
            border-left: 5px solid;
        }
        .signal-buy {
            border-left-color: #28a745;
        }
        .signal-sell {
            border-left-color: #dc3545;
        }
        .progress {
            height: 10px;
            margin-top: 5px;
        }
        .nav-tabs .nav-link.active {
            font-weight: bold;
        }
        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.1);
        }
        .badge-volume {
            background-color: #6f42c1;
        }
        .badge-price {
            background-color: #20c997;
        }
        .badge-support {
            background-color: #28a745;
        }
        .badge-resistance {
            background-color: #dc3545;
        }
        #status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-graph-up"></i> Indian Market Tick Data Analysis
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" id="dashboard-tab">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="data-tab">Data Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="analysis-tab">Analysis</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="signals-tab">Trading Signals</a>
                    </li>
                </ul>
                <div class="ms-auto d-flex align-items-center text-light">
                    <span id="status-indicator" class="status-disconnected"></span>
                    <span id="connection-status">Disconnected</span>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Dashboard View -->
        <div id="dashboard-view">
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <span>System Status</span>
                            <button class="btn btn-sm btn-outline-primary" id="refresh-status">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <div class="stats-value" id="total-records">-</div>
                                    <div class="stats-label">Total Records</div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="stats-value" id="indices-count">-</div>
                                    <div class="stats-label">Indices</div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="stats-value" id="latest-date">-</div>
                                    <div class="stats-label">Latest Data</div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="stats-value" id="data-quality">-</div>
                                    <div class="stats-label">Data Quality</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">Latest Market Data</div>
                        <div class="card-body">
                            <div id="latest-data-container">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">Recent Import Jobs</div>
                        <div class="card-body">
                            <div id="recent-jobs-container">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">Trading Signals</div>
                        <div class="card-body">
                            <div id="signals-container">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Management View -->
        <div id="data-view" style="display: none;">
            <div class="card">
                <div class="card-header">Data Import</div>
                <div class="card-body">
                    <ul class="nav nav-tabs" id="importTabs">
                        <li class="nav-item">
                            <a class="nav-link active" id="daily-tab" data-bs-toggle="tab" href="#daily">Daily Import</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="bulk-tab" data-bs-toggle="tab" href="#bulk">Bulk Import</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="jobs-tab" data-bs-toggle="tab" href="#jobs">Import Jobs</a>
                        </li>
                    </ul>
                    <div class="tab-content mt-3">
                        <div class="tab-pane fade show active" id="daily">
                            <form id="daily-import-form">
                                <div class="mb-3">
                                    <label for="daily-date" class="form-label">Date</label>
                                    <input type="date" class="form-control" id="daily-date">
                                </div>
                                <div class="mb-3">
                                    <label for="daily-source-dir" class="form-label">Source Directory</label>
                                    <input type="text" class="form-control" id="daily-source-dir" value="sampledata">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Indices</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="nifty" id="daily-nifty" checked>
                                        <label class="form-check-label" for="daily-nifty">Nifty 50</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="bank_nifty" id="daily-bank-nifty" checked>
                                        <label class="form-check-label" for="daily-bank-nifty">Bank Nifty</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="fin_nifty" id="daily-fin-nifty" checked>
                                        <label class="form-check-label" for="daily-fin-nifty">Fin Nifty</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="midcap_nifty" id="daily-midcap-nifty" checked>
                                        <label class="form-check-label" for="daily-midcap-nifty">Midcap Nifty</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="nifty_next" id="daily-nifty-next" checked>
                                        <label class="form-check-label" for="daily-nifty-next">Nifty Next 50</label>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">Start Daily Import</button>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="bulk">
                            <form id="bulk-import-form">
                                <div class="mb-3">
                                    <label for="bulk-source-dir" class="form-label">Source Directory</label>
                                    <input type="text" class="form-control" id="bulk-source-dir" value="sampledata">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Date Range (Optional)</label>
                                    <div class="row">
                                        <div class="col">
                                            <input type="date" class="form-control" id="bulk-start-date" placeholder="Start Date">
                                        </div>
                                        <div class="col">
                                            <input type="date" class="form-control" id="bulk-end-date" placeholder="End Date">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="bulk-clean-db">
                                    <label class="form-check-label" for="bulk-clean-db">Clean database before import</label>
                                </div>
                                <button type="submit" class="btn btn-primary">Start Bulk Import</button>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="jobs">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Job ID</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Progress</th>
                                            <th>Files</th>
                                        </tr>
                                    </thead>
                                    <tbody id="jobs-table-body">
                                        <tr>
                                            <td colspan="6" class="text-center">Loading jobs...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analysis View -->
        <div id="analysis-view" style="display: none;">
            <!-- Analysis content will be added here -->
        </div>

        <!-- Trading Signals View -->
        <div id="signals-view" style="display: none;">
            <!-- Signals content will be added here -->
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/socket.io/client-dist/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // JavaScript code will be added here
        document.addEventListener('DOMContentLoaded', function() {
            // Socket.io connection
            const socket = io();
            
            // Connection status
            socket.on('connect', function() {
                document.getElementById('status-indicator').className = 'status-connected';
                document.getElementById('connection-status').textContent = 'Connected';
                
                // Start monitoring
                socket.emit('start_monitoring');
            });
            
            socket.on('disconnect', function() {
                document.getElementById('status-indicator').className = 'status-disconnected';
                document.getElementById('connection-status').textContent = 'Disconnected';
            });
            
            // Tab navigation
            document.getElementById('dashboard-tab').addEventListener('click', function(e) {
                e.preventDefault();
                showView('dashboard-view');
            });
            
            document.getElementById('data-tab').addEventListener('click', function(e) {
                e.preventDefault();
                showView('data-view');
            });
            
            document.getElementById('analysis-tab').addEventListener('click', function(e) {
                e.preventDefault();
                showView('analysis-view');
            });
            
            document.getElementById('signals-tab').addEventListener('click', function(e) {
                e.preventDefault();
                showView('signals-view');
            });
            
            function showView(viewId) {
                // Hide all views
                document.getElementById('dashboard-view').style.display = 'none';
                document.getElementById('data-view').style.display = 'none';
                document.getElementById('analysis-view').style.display = 'none';
                document.getElementById('signals-view').style.display = 'none';
                
                // Show selected view
                document.getElementById(viewId).style.display = 'block';
                
                // Update active tab
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                
                if (viewId === 'dashboard-view') {
                    document.getElementById('dashboard-tab').classList.add('active');
                } else if (viewId === 'data-view') {
                    document.getElementById('data-tab').classList.add('active');
                } else if (viewId === 'analysis-view') {
                    document.getElementById('analysis-tab').classList.add('active');
                } else if (viewId === 'signals-view') {
                    document.getElementById('signals-tab').classList.add('active');
                }
            }
            
            // Load initial data
            loadSystemStatus();
            loadLatestData();
            loadRecentJobs();
            loadSignals();
            
            // Refresh button
            document.getElementById('refresh-status').addEventListener('click', function() {
                loadSystemStatus();
                loadLatestData();
                loadRecentJobs();
                loadSignals();
            });
            
            // Functions to load data
            function loadSystemStatus() {
                fetch('/api/status')
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'ok') {
                            document.getElementById('total-records').textContent = 
                                new Intl.NumberFormat().format(data.database.total_records);
                            document.getElementById('indices-count').textContent = 
                                data.database.indices_count;
                            document.getElementById('latest-date').textContent = 
                                data.data_quality.latest_timestamp.split('T')[0];
                            
                            // Calculate data quality score
                            const qualityScore = data.data_quality.data_gaps > 0 ? 
                                'Needs Review' : 'Excellent';
                            document.getElementById('data-quality').textContent = qualityScore;
                        }
                    })
                    .catch(error => console.error('Error loading status:', error));
            }
            
            function loadLatestData() {
                // Implementation will be added
            }
            
            function loadRecentJobs() {
                // Implementation will be added
            }
            
            function loadSignals() {
                // Implementation will be added
            }
            
            // Form submissions
            document.getElementById('daily-import-form').addEventListener('submit', function(e) {
                e.preventDefault();
                // Implementation will be added
            });
            
            document.getElementById('bulk-import-form').addEventListener('submit', function(e) {
                e.preventDefault();
                // Implementation will be added
            });
        });
    </script>
</body>
</html>
