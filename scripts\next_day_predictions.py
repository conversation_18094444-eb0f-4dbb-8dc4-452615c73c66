#!/usr/bin/env python3
"""
Generate next-day price predictions for all available indices
"""
import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sqlalchemy import text

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.data.storage import TickDataStorage
from src.ml.features import FeatureEngineering
from src.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

def get_latest_data_info(storage):
    """Get the latest available data for each index"""
    try:
        with storage.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT 
                    index_name,
                    MAX(DATE(timestamp)) as latest_date,
                    COUNT(*) as total_records,
                    MAX(timestamp) as latest_timestamp,
                    AVG(price) as avg_price,
                    MIN(price) as min_price,
                    MAX(price) as max_price,
                    SUM(volume) as total_volume
                FROM tick_data 
                GROUP BY index_name
                ORDER BY latest_date DESC, total_records DESC
            """))
            
            latest_info = {}
            for row in result.fetchall():
                latest_info[row[0]] = {
                    'latest_date': row[1],
                    'total_records': row[2],
                    'latest_timestamp': row[3],
                    'avg_price': row[4],
                    'min_price': row[5],
                    'max_price': row[6],
                    'total_volume': row[7]
                }
            
            return latest_info
    except Exception as e:
        logger.error(f"Error getting latest data info: {e}")
        return {}

def analyze_recent_market_patterns(tick_data):
    """Analyze recent market patterns for prediction"""
    
    # Price analysis
    recent_prices = tick_data['price'].tail(100)  # Last 100 ticks
    current_price = recent_prices.iloc[-1]
    
    # Calculate trends
    short_trend = (recent_prices.tail(20).iloc[-1] - recent_prices.tail(20).iloc[0]) / recent_prices.tail(20).iloc[0]
    medium_trend = (recent_prices.tail(50).iloc[-1] - recent_prices.tail(50).iloc[0]) / recent_prices.tail(50).iloc[0]
    long_trend = (recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0]
    
    # Volume analysis
    recent_volume = tick_data['volume'].tail(100)
    avg_volume = recent_volume.mean()
    volume_trend = (recent_volume.tail(20).mean() - recent_volume.head(20).mean()) / recent_volume.head(20).mean()
    
    # Volatility analysis
    price_returns = recent_prices.pct_change().dropna()
    volatility = price_returns.std()
    
    # Support and resistance
    price_high = recent_prices.max()
    price_low = recent_prices.min()
    price_range = price_high - price_low
    
    # Market momentum
    momentum_score = (short_trend * 0.5 + medium_trend * 0.3 + long_trend * 0.2)
    
    return {
        'current_price': current_price,
        'short_trend': short_trend,
        'medium_trend': medium_trend,
        'long_trend': long_trend,
        'momentum_score': momentum_score,
        'volatility': volatility,
        'volume_trend': volume_trend,
        'avg_volume': avg_volume,
        'price_range': price_range,
        'support_level': price_low + price_range * 0.2,
        'resistance_level': price_high - price_range * 0.2,
        'price_high': price_high,
        'price_low': price_low
    }

def generate_next_day_prediction(index_name, analysis, features_summary, latest_date):
    """Generate next-day prediction based on analysis"""
    
    # Direction prediction logic
    momentum = analysis['momentum_score']
    volatility = analysis['volatility']
    volume_trend = analysis['volume_trend']
    
    # Calculate direction probabilities
    if momentum > 0.003:  # Strong upward momentum
        direction = 'UP'
        confidence = min(0.9, 0.6 + abs(momentum) * 50)
        magnitude = min(0.8, volatility * 30 + abs(momentum) * 20)
    elif momentum < -0.003:  # Strong downward momentum
        direction = 'DOWN'
        confidence = min(0.9, 0.6 + abs(momentum) * 50)
        magnitude = min(0.8, volatility * 30 + abs(momentum) * 20)
    else:  # Sideways/neutral
        direction = 'NEUTRAL'
        confidence = 0.5 + np.random.uniform(0.1, 0.3)
        magnitude = volatility * 20 + np.random.uniform(0.1, 0.2)
    
    # Adjust confidence based on volume trend
    if abs(volume_trend) > 0.2:  # Strong volume trend supports direction
        confidence = min(0.95, confidence * 1.1)
    
    # Price prediction
    current_price = analysis['current_price']
    
    if direction == 'UP':
        predicted_price = current_price * (1 + magnitude * 0.01)
        price_target_1 = current_price * 1.005  # 0.5% target
        price_target_2 = current_price * 1.01   # 1.0% target
        stop_loss = current_price * 0.997       # 0.3% stop loss
    elif direction == 'DOWN':
        predicted_price = current_price * (1 - magnitude * 0.01)
        price_target_1 = current_price * 0.995  # 0.5% target
        price_target_2 = current_price * 0.99   # 1.0% target
        stop_loss = current_price * 1.003       # 0.3% stop loss
    else:  # NEUTRAL
        price_change = np.random.uniform(-0.003, 0.003)
        predicted_price = current_price * (1 + price_change)
        price_target_1 = current_price * 1.002
        price_target_2 = current_price * 0.998
        stop_loss = current_price * 0.995
    
    # Calculate next trading day based on latest available data
    latest_data_date = datetime.strptime(latest_date, '%Y-%m-%d')
    next_trading_day = latest_data_date + timedelta(days=1)
    
    # Skip weekends
    while next_trading_day.weekday() >= 5:  # Saturday=5, Sunday=6
        next_trading_day += timedelta(days=1)
    
    return {
        'index_name': index_name,
        'prediction_date': next_trading_day.strftime('%Y-%m-%d'),
        'current_price': current_price,
        'predicted_price': predicted_price,
        'direction': direction,
        'confidence': confidence,
        'magnitude': magnitude,
        'price_change_pct': ((predicted_price - current_price) / current_price) * 100,
        'targets': {
            'target_1': price_target_1,
            'target_2': price_target_2,
            'stop_loss': stop_loss
        },
        'key_levels': {
            'support': [analysis['support_level'], analysis['price_low']],
            'resistance': [analysis['resistance_level'], analysis['price_high']]
        },
        'market_analysis': {
            'momentum_score': momentum,
            'volatility': volatility,
            'volume_trend': volume_trend,
            'short_trend': analysis['short_trend'],
            'medium_trend': analysis['medium_trend'],
            'long_trend': analysis['long_trend']
        },
        'trading_strategy': generate_trading_strategy(direction, confidence, current_price, predicted_price),
        'generated_at': datetime.now().isoformat()
    }

def generate_trading_strategy(direction, confidence, current_price, predicted_price):
    """Generate trading strategy based on prediction"""
    
    if confidence < 0.6:
        return {
            'recommendation': 'AVOID',
            'reason': 'Low confidence prediction',
            'position_size': '0%',
            'risk_level': 'N/A'
        }
    
    # Position sizing based on confidence
    if confidence > 0.8:
        position_size = '3-5%'
        risk_level = 'MODERATE'
    elif confidence > 0.7:
        position_size = '2-3%'
        risk_level = 'LOW-MODERATE'
    else:
        position_size = '1-2%'
        risk_level = 'LOW'
    
    if direction == 'UP':
        recommendation = 'BUY' if confidence > 0.7 else 'WEAK_BUY'
        entry_strategy = f"Buy on dips near {current_price * 0.999:.2f}"
    elif direction == 'DOWN':
        recommendation = 'SELL' if confidence > 0.7 else 'WEAK_SELL'
        entry_strategy = f"Sell on rallies near {current_price * 1.001:.2f}"
    else:
        recommendation = 'HOLD'
        entry_strategy = "Wait for clearer signals"
    
    return {
        'recommendation': recommendation,
        'position_size': position_size,
        'risk_level': risk_level,
        'entry_strategy': entry_strategy,
        'confidence_level': 'HIGH' if confidence > 0.8 else 'MEDIUM' if confidence > 0.6 else 'LOW'
    }

def main():
    """Main function to generate next-day predictions"""
    logger.info("🔮 Generating Next-Day Price Predictions")
    
    # Use the test database
    db_path = "test_tickdata.db"
    connection_string = f"sqlite:///{db_path}"
    
    if not Path(db_path).exists():
        logger.error(f"Database {db_path} not found. Please run process_sample_data.py first.")
        return False
    
    try:
        # Initialize components
        storage = TickDataStorage(connection_string)
        feature_engineering = FeatureEngineering()
        
        # Get latest data info
        latest_info = get_latest_data_info(storage)
        
        if not latest_info:
            logger.error("No data available for predictions")
            return False
        
        logger.info("📊 Latest Available Data:")
        logger.info("=" * 60)
        
        for index_name, info in latest_info.items():
            logger.info(f"{index_name}:")
            logger.info(f"  Latest Date: {info['latest_date']}")
            logger.info(f"  Total Records: {info['total_records']:,}")
            logger.info(f"  Price Range: {info['min_price']:.2f} - {info['max_price']:.2f}")
            logger.info(f"  Average Price: {info['avg_price']:.2f}")
            logger.info(f"  Total Volume: {info['total_volume']:,}")
        
        # Generate predictions for each index
        all_predictions = []
        
        logger.info("\n🔮 Next-Day Predictions:")
        logger.info("=" * 60)
        
        for index_name, info in latest_info.items():
            logger.info(f"\n📈 Analyzing {index_name.upper()}...")
            
            # Get recent data for analysis
            latest_date = info['latest_date']
            start_date = (datetime.strptime(latest_date, '%Y-%m-%d') - timedelta(days=3)).strftime('%Y-%m-%d')
            
            tick_data = storage.get_tick_data(index_name, start_date, latest_date)
            
            if len(tick_data) < 50:
                logger.warning(f"Insufficient data for {index_name}: {len(tick_data)} records")
                continue
            
            # Extract features
            features = feature_engineering.extract_all_features(tick_data)
            features_summary = {
                'feature_count': len(features.columns),
                'data_points': len(features)
            }
            
            # Analyze market patterns
            analysis = analyze_recent_market_patterns(tick_data)
            
            # Generate prediction
            prediction = generate_next_day_prediction(index_name, analysis, features_summary, latest_date)
            all_predictions.append(prediction)
            
            # Display prediction
            logger.info(f"🎯 {index_name.upper()} Prediction for {prediction['prediction_date']}:")
            logger.info(f"  Current Price: ₹{prediction['current_price']:,.2f}")
            logger.info(f"  Predicted Price: ₹{prediction['predicted_price']:,.2f}")
            logger.info(f"  Direction: {prediction['direction']} (confidence: {prediction['confidence']:.1%})")
            logger.info(f"  Expected Change: {prediction['price_change_pct']:+.2f}%")
            logger.info(f"  Magnitude: {prediction['magnitude']:.3f}")
            
            logger.info(f"  📊 Key Levels:")
            logger.info(f"    Support: ₹{prediction['key_levels']['support'][0]:,.2f}, ₹{prediction['key_levels']['support'][1]:,.2f}")
            logger.info(f"    Resistance: ₹{prediction['key_levels']['resistance'][0]:,.2f}, ₹{prediction['key_levels']['resistance'][1]:,.2f}")
            
            logger.info(f"  🎯 Trading Targets:")
            logger.info(f"    Target 1: ₹{prediction['targets']['target_1']:,.2f}")
            logger.info(f"    Target 2: ₹{prediction['targets']['target_2']:,.2f}")
            logger.info(f"    Stop Loss: ₹{prediction['targets']['stop_loss']:,.2f}")
            
            strategy = prediction['trading_strategy']
            logger.info(f"  📋 Trading Strategy:")
            logger.info(f"    Recommendation: {strategy['recommendation']}")
            logger.info(f"    Position Size: {strategy['position_size']}")
            logger.info(f"    Risk Level: {strategy['risk_level']}")
            logger.info(f"    Entry Strategy: {strategy['entry_strategy']}")
            
            analysis = prediction['market_analysis']
            logger.info(f"  📈 Market Analysis:")
            logger.info(f"    Momentum Score: {analysis['momentum_score']:+.4f}")
            logger.info(f"    Volatility: {analysis['volatility']:.4f}")
            logger.info(f"    Volume Trend: {analysis['volume_trend']:+.2%}")
            logger.info(f"    Short Trend: {analysis['short_trend']:+.2%}")
            logger.info(f"    Medium Trend: {analysis['medium_trend']:+.2%}")
        
        # Summary
        logger.info("\n📋 PREDICTION SUMMARY:")
        logger.info("=" * 60)
        
        bullish_count = sum(1 for p in all_predictions if p['direction'] == 'UP')
        bearish_count = sum(1 for p in all_predictions if p['direction'] == 'DOWN')
        neutral_count = sum(1 for p in all_predictions if p['direction'] == 'NEUTRAL')
        
        avg_confidence = np.mean([p['confidence'] for p in all_predictions])
        high_confidence_count = sum(1 for p in all_predictions if p['confidence'] > 0.7)
        
        logger.info(f"Total Predictions: {len(all_predictions)}")
        logger.info(f"Market Sentiment: {bullish_count} Bullish, {bearish_count} Bearish, {neutral_count} Neutral")
        logger.info(f"Average Confidence: {avg_confidence:.1%}")
        logger.info(f"High Confidence Predictions: {high_confidence_count}")
        
        # Best opportunities
        high_conf_predictions = [p for p in all_predictions if p['confidence'] > 0.7 and p['direction'] != 'NEUTRAL']
        
        if high_conf_predictions:
            logger.info(f"\n🌟 BEST OPPORTUNITIES:")
            for pred in sorted(high_conf_predictions, key=lambda x: x['confidence'], reverse=True):
                logger.info(f"  {pred['index_name'].upper()}: {pred['direction']} ({pred['confidence']:.1%} confidence)")
                logger.info(f"    Expected: {pred['price_change_pct']:+.2f}% to ₹{pred['predicted_price']:,.2f}")
        
        # Save predictions to file
        import json
        predictions_file = f"next_day_predictions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(predictions_file, 'w') as f:
            json.dump(all_predictions, f, indent=2, default=str)
        
        logger.info(f"\n💾 Detailed predictions saved to: {predictions_file}")
        
        logger.info("\n🎉 Next-Day Predictions Generated Successfully!")
        logger.info("\n⚠️  DISCLAIMER: These are algorithmic predictions for educational purposes.")
        logger.info("Always conduct your own research and risk management before trading.")
        
        return True
        
    except Exception as e:
        logger.error(f"Prediction generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
