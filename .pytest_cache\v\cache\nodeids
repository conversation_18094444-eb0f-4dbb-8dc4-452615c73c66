["tests/test_import_manager.py::TestDataImportManager::test_cancel_job", "tests/test_import_manager.py::TestDataImportManager::test_cleanup_old_jobs", "tests/test_import_manager.py::TestDataImportManager::test_create_bulk_import_job", "tests/test_import_manager.py::TestDataImportManager::test_create_daily_import_job", "tests/test_import_manager.py::TestDataImportManager::test_date_range_filtering", "tests/test_import_manager.py::TestDataImportManager::test_error_handling", "tests/test_import_manager.py::TestDataImportManager::test_execute_bulk_job", "tests/test_import_manager.py::TestDataImportManager::test_execute_daily_job", "tests/test_import_manager.py::TestDataImportManager::test_job_history_persistence", "tests/test_import_manager.py::TestDataImportManager::test_list_jobs", "tests/test_import_manager.py::TestDataImportManager::test_scan_directory", "tests/test_import_manager.py::TestFileInfoDataClass::test_file_info_creation", "tests/test_import_manager.py::TestImportJobDataClass::test_import_job_creation", "tests/test_integration.py::TestCompleteWorkflows::test_bulk_import_and_analysis_workflow", "tests/test_integration.py::TestCompleteWorkflows::test_complete_daily_workflow", "tests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations", "tests/test_integration.py::TestCompleteWorkflows::test_data_consistency_workflow", "tests/test_integration.py::TestCompleteWorkflows::test_error_recovery_workflow", "tests/test_integration.py::TestCompleteWorkflows::test_performance_workflow", "tests/test_integration.py::TestSystemIntegration::test_configuration_consistency", "tests/test_integration.py::TestSystemIntegration::test_database_schema_consistency", "tests/test_integration.py::TestSystemIntegration::test_logging_integration", "tests/test_interfaces.py::TestAPIEndpoints::test_analysis_endpoint", "tests/test_interfaces.py::TestAPIEndpoints::test_api_error_handling", "tests/test_interfaces.py::TestAPIEndpoints::test_data_export_endpoint", "tests/test_interfaces.py::TestAPIEndpoints::test_health_endpoint", "tests/test_interfaces.py::TestAPIEndpoints::test_import_jobs_endpoint", "tests/test_interfaces.py::TestAPIEndpoints::test_signals_endpoint", "tests/test_interfaces.py::TestAPIEndpoints::test_system_status_endpoint", "tests/test_interfaces.py::TestCLIInterface::test_cli_data_commands", "tests/test_interfaces.py::TestCLIInterface::test_cli_db_commands", "tests/test_interfaces.py::TestCLIInterface::test_cli_help_command", "tests/test_interfaces.py::TestCLIInterface::test_cli_system_commands", "tests/test_interfaces.py::TestCLIInterface::test_cli_trading_commands", "tests/test_interfaces.py::TestIntegrationWorkflows::test_cli_to_api_workflow", "tests/test_interfaces.py::TestIntegrationWorkflows::test_error_propagation", "tests/test_interfaces.py::TestPlatformManager::test_platform_manager_help", "tests/test_interfaces.py::TestPlatformManager::test_platform_start_help", "tests/test_interfaces.py::TestWebDashboard::test_dashboard_api_status", "tests/test_interfaces.py::TestWebDashboard::test_dashboard_import_jobs", "tests/test_interfaces.py::TestWebDashboard::test_dashboard_latest_data", "tests/test_interfaces.py::TestWebDashboard::test_dashboard_main_page", "tests/test_trading_analysis.py::TestFeatureEngineering::test_extract_all_features", "tests/test_trading_analysis.py::TestFeatureEngineering::test_price_features", "tests/test_trading_analysis.py::TestFeatureEngineering::test_volume_features", "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_analyze_market_structure", "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_confidence_scoring", "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_generate_trading_signals", "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_market_character_assessment", "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_multiple_indices", "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_no_data_handling", "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_order_flow_analysis", "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_risk_reward_calculation", "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_support_resistance_identification", "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_volume_breakout_analysis"]