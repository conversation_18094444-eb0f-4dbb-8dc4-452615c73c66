"""
Tests for database functionality and connectivity
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from src.data.storage import TickDataStorage
from src.database.init_db import DatabaseManager

class TestDatabaseConnectivity:
    """Test database connection and basic operations"""
    
    def test_database_connection(self, test_config):
        """Test basic database connection"""
        try:
            storage = TickDataStorage(test_config.database.connection_string)
            assert storage.engine is not None
        except Exception as e:
            pytest.fail(f"Database connection failed: {e}")
    
    def test_connection_pool(self, test_config):
        """Test connection pooling"""
        storage = TickDataStorage(
            test_config.database.connection_string,
            pool_size=5,
            max_overflow=10
        )
        
        # Test multiple concurrent connections
        connections = []
        try:
            for i in range(3):
                conn = storage.engine.connect()
                result = conn.execute(text("SELECT 1"))
                assert result.scalar() == 1
                connections.append(conn)
        finally:
            for conn in connections:
                conn.close()
    
    def test_database_manager_initialization(self):
        """Test DatabaseManager initialization"""
        try:
            db_manager = DatabaseManager()
            assert db_manager.config is not None
            assert db_manager.connection_string is not None
        except Exception as e:
            pytest.fail(f"DatabaseManager initialization failed: {e}")

class TestTickDataStorage:
    """Test TickDataStorage functionality"""
    
    def test_store_tick_data_success(self, test_storage, sample_tick_data):
        """Test successful tick data storage"""
        records_stored = test_storage.store_tick_data(sample_tick_data)
        
        assert records_stored == len(sample_tick_data)
        assert records_stored > 0
    
    def test_store_empty_dataframe(self, test_storage):
        """Test storing empty DataFrame"""
        empty_df = pd.DataFrame(columns=['timestamp', 'index_name', 'price', 'volume'])
        records_stored = test_storage.store_tick_data(empty_df)
        
        assert records_stored == 0
    
    def test_store_invalid_data_missing_columns(self, test_storage):
        """Test storing data with missing required columns"""
        invalid_data = pd.DataFrame({
            'timestamp': [datetime.now()],
            'price': [19500.0]
            # Missing 'index_name' and 'volume'
        })
        
        with pytest.raises(ValueError, match="Missing required columns"):
            test_storage.store_tick_data(invalid_data)
    
    def test_store_invalid_data_null_values(self, test_storage):
        """Test storing data with null values"""
        invalid_data = pd.DataFrame({
            'timestamp': [datetime.now(), None],
            'index_name': ['test', 'test'],
            'price': [19500.0, 19501.0],
            'volume': [1000, 1500]
        })
        
        with pytest.raises(ValueError, match="Null values found"):
            test_storage.store_tick_data(invalid_data)
    
    def test_store_invalid_data_negative_price(self, test_storage):
        """Test storing data with negative prices"""
        invalid_data = pd.DataFrame({
            'timestamp': [datetime.now()],
            'index_name': ['test'],
            'price': [-19500.0],  # Negative price
            'volume': [1000]
        })
        
        with pytest.raises(ValueError, match="price values must be positive"):
            test_storage.store_tick_data(invalid_data)
    
    def test_store_invalid_data_negative_volume(self, test_storage):
        """Test storing data with negative volume"""
        invalid_data = pd.DataFrame({
            'timestamp': [datetime.now()],
            'index_name': ['test'],
            'price': [19500.0],
            'volume': [-1000]  # Negative volume
        })
        
        with pytest.raises(ValueError, match="volume values must be non-negative"):
            test_storage.store_tick_data(invalid_data)
    
    def test_get_tick_data(self, test_storage, sample_tick_data):
        """Test retrieving tick data"""
        # Store sample data first
        test_storage.store_tick_data(sample_tick_data)
        
        # Retrieve data
        start_date = sample_tick_data['timestamp'].min().strftime('%Y-%m-%d')
        end_date = sample_tick_data['timestamp'].max().strftime('%Y-%m-%d')
        
        retrieved_data = test_storage.get_tick_data('test_nifty', start_date, end_date)
        
        assert len(retrieved_data) > 0
        assert 'timestamp' in retrieved_data.columns
        assert 'price' in retrieved_data.columns
        assert 'volume' in retrieved_data.columns
        assert 'index_name' in retrieved_data.columns
    
    def test_get_tick_data_no_results(self, test_storage):
        """Test retrieving tick data with no results"""
        retrieved_data = test_storage.get_tick_data('nonexistent', '2025-01-01', '2025-01-02')
        
        assert len(retrieved_data) == 0
    
    def test_get_tick_data_date_filtering(self, test_storage):
        """Test date filtering in tick data retrieval"""
        # Create data spanning multiple days
        base_time = datetime(2025, 7, 15, 9, 15, 0)
        
        data_day1 = pd.DataFrame({
            'timestamp': [base_time + timedelta(seconds=i) for i in range(100)],
            'index_name': ['test'] * 100,
            'price': np.random.normal(19500, 10, 100),
            'volume': np.random.randint(100, 1000, 100)
        })
        
        data_day2 = pd.DataFrame({
            'timestamp': [base_time + timedelta(days=1, seconds=i) for i in range(100)],
            'index_name': ['test'] * 100,
            'price': np.random.normal(19500, 10, 100),
            'volume': np.random.randint(100, 1000, 100)
        })
        
        # Store both days
        test_storage.store_tick_data(data_day1)
        test_storage.store_tick_data(data_day2)
        
        # Retrieve only day 1
        day1_data = test_storage.get_tick_data('test', '2025-07-15', '2025-07-15')
        
        # Should only get day 1 data
        assert len(day1_data) == 100
        assert all(day1_data['timestamp'].dt.date == datetime(2025, 7, 15).date())

class TestDatabaseSchema:
    """Test database schema operations"""
    
    def test_create_schema_sqlite(self, test_storage):
        """Test schema creation (SQLite version for testing)"""
        # For SQLite testing, we just verify tables exist
        with test_storage.engine.connect() as conn:
            # Check if tick_data table exists
            result = conn.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='tick_data'
            """))
            
            tables = [row[0] for row in result]
            assert 'tick_data' in tables

class TestDatabasePerformance:
    """Test database performance characteristics"""
    
    def test_bulk_insert_performance(self, test_storage):
        """Test performance of bulk data insertion"""
        # Create large dataset
        n_records = 10000
        large_dataset = pd.DataFrame({
            'timestamp': pd.date_range('2025-07-15 09:15:00', periods=n_records, freq='1S'),
            'index_name': ['test'] * n_records,
            'price': 19500 + np.random.normal(0, 10, n_records),
            'volume': np.random.randint(100, 1000, n_records)
        })
        
        # Measure insertion time
        import time
        start_time = time.time()
        
        records_stored = test_storage.store_tick_data(large_dataset)
        
        end_time = time.time()
        insertion_time = end_time - start_time
        
        # Verify results
        assert records_stored == n_records
        
        # Performance assertion (should process at least 1000 records per second)
        records_per_second = n_records / insertion_time
        assert records_per_second > 1000, f"Performance too slow: {records_per_second:.2f} records/sec"
    
    def test_query_performance(self, test_storage, sample_tick_data):
        """Test query performance"""
        # Store sample data
        test_storage.store_tick_data(sample_tick_data)
        
        # Measure query time
        import time
        start_time = time.time()
        
        start_date = sample_tick_data['timestamp'].min().strftime('%Y-%m-%d')
        end_date = sample_tick_data['timestamp'].max().strftime('%Y-%m-%d')
        
        result = test_storage.get_tick_data('test_nifty', start_date, end_date)
        
        end_time = time.time()
        query_time = end_time - start_time
        
        # Verify results
        assert len(result) > 0
        
        # Performance assertion (should complete in under 1 second)
        assert query_time < 1.0, f"Query too slow: {query_time:.3f} seconds"

class TestDatabaseErrorHandling:
    """Test database error handling"""
    
    def test_invalid_connection_string(self):
        """Test handling of invalid connection string"""
        with pytest.raises(Exception):
            TickDataStorage("invalid://connection/string")
    
    def test_connection_timeout_handling(self, test_config):
        """Test connection timeout handling"""
        # Create storage with very short timeout
        storage = TickDataStorage(
            test_config.database.connection_string,
            pool_size=1,
            max_overflow=0
        )
        
        # This should work normally
        assert storage.engine is not None
    
    def test_sql_injection_protection(self, test_storage):
        """Test protection against SQL injection"""
        # Attempt SQL injection in index name
        malicious_index = "test'; DROP TABLE tick_data; --"
        
        # This should not cause any issues due to parameterized queries
        result = test_storage.get_tick_data(malicious_index, '2025-07-15', '2025-07-15')
        
        # Should return empty result, not crash
        assert len(result) == 0
