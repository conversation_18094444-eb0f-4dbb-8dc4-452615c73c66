#!/usr/bin/env python3
"""
Test suite for the Data Import Manager
Tests import jobs, file processing, and job management
"""
import pytest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import json
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.data.storage import TickDataStorage
from src.data.import_manager import DataImportManager, ImportJob, FileInfo
from config import get_config

class TestDataImportManager:
    """Test suite for DataImportManager"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def test_storage(self, temp_dir):
        """Create test database storage"""
        db_path = temp_dir / "test_import.db"
        storage = TickDataStorage(f"sqlite:///{db_path}")
        storage.init_database()
        return storage
    
    @pytest.fixture
    def import_manager(self, test_storage):
        """Create import manager instance"""
        config = get_config()
        return DataImportManager(test_storage, config)
    
    @pytest.fixture
    def sample_data_files(self, temp_dir):
        """Create sample data files for testing"""
        files = {}
        
        # Create sample tick data
        sample_data = {
            'Time': ['14-07-2025 09:15:00', '14-07-2025 09:15:02', '14-07-2025 09:15:04'],
            'Last Rate': [25170.0, 25171.5, 25172.0],
            'Volume': [100, 150, 200]
        }
        df = pd.DataFrame(sample_data)
        
        # Create files for different indices
        indices = ['Nifty', 'Bank Nifty', 'Fin Nifty', 'Midcap Nifty', 'Nifty Nxt']
        date_str = '********'
        
        for index in indices:
            filename = f"{index} Ticklist {date_str}.csv"
            file_path = temp_dir / filename
            df.to_csv(file_path, index=False)
            files[index.lower().replace(' ', '_')] = file_path
        
        return files
    
    def test_scan_directory(self, import_manager, sample_data_files, temp_dir):
        """Test directory scanning functionality"""
        files = import_manager.scan_directory(temp_dir)
        
        assert len(files) == 5, f"Expected 5 files, found {len(files)}"
        
        # Check file info structure
        for file_info in files:
            assert isinstance(file_info, FileInfo)
            assert file_info.path.exists()
            assert file_info.index_name in ['nifty', 'bank_nifty', 'fin_nifty', 'midcap_nifty', 'nifty_next']
            assert file_info.date == '********'
            assert file_info.size_bytes > 0
    
    def test_create_daily_import_job(self, import_manager, sample_data_files, temp_dir):
        """Test daily import job creation"""
        date = '2025-07-14'
        indices = ['nifty', 'bank_nifty']
        
        job_id = import_manager.create_daily_import_job(
            date=date,
            source_dir=temp_dir,
            indices=indices
        )
        
        assert job_id.startswith('daily_2025-07-14')
        
        job = import_manager.get_job_status(job_id)
        assert job is not None
        assert job.job_type == 'daily'
        assert job.status == 'pending'
        assert job.files_total == 2  # Only nifty and bank_nifty
        assert job.metadata['date'] == date
        assert job.metadata['indices'] == indices
    
    def test_create_bulk_import_job(self, import_manager, sample_data_files, temp_dir):
        """Test bulk import job creation"""
        job_id = import_manager.create_bulk_import_job(
            source_dir=temp_dir,
            clean_database=True
        )
        
        assert job_id.startswith('bulk_')
        
        job = import_manager.get_job_status(job_id)
        assert job is not None
        assert job.job_type == 'bulk'
        assert job.status == 'pending'
        assert job.files_total == 5  # All indices
        assert job.metadata['clean_database'] is True
    
    def test_execute_daily_job(self, import_manager, sample_data_files, temp_dir):
        """Test daily job execution"""
        # Create job
        job_id = import_manager.create_daily_import_job(
            date='2025-07-14',
            source_dir=temp_dir,
            indices=['nifty']
        )
        
        # Execute job
        success = import_manager.execute_job(job_id)
        
        assert success is True
        
        job = import_manager.get_job_status(job_id)
        assert job.status in ['completed', 'completed_with_errors']
        assert job.files_processed == 1
        assert job.files_successful >= 0
        assert job.started_at is not None
        assert job.completed_at is not None
    
    def test_execute_bulk_job(self, import_manager, sample_data_files, temp_dir):
        """Test bulk job execution"""
        # Create job
        job_id = import_manager.create_bulk_import_job(
            source_dir=temp_dir,
            indices=['nifty', 'bank_nifty']
        )
        
        # Execute job
        success = import_manager.execute_job(job_id)
        
        assert success is True
        
        job = import_manager.get_job_status(job_id)
        assert job.status in ['completed', 'completed_with_errors']
        assert job.files_processed == 2
        assert job.files_successful >= 0
    
    def test_job_history_persistence(self, import_manager, sample_data_files, temp_dir):
        """Test job history saving and loading"""
        # Create a job
        job_id = import_manager.create_daily_import_job(
            date='2025-07-14',
            source_dir=temp_dir
        )
        
        # Create new import manager instance (simulates restart)
        new_manager = DataImportManager(import_manager.storage, import_manager.config)
        
        # Check if job history was loaded
        job = new_manager.get_job_status(job_id)
        assert job is not None
        assert job.job_id == job_id
    
    def test_list_jobs(self, import_manager, sample_data_files, temp_dir):
        """Test job listing functionality"""
        # Create multiple jobs
        job1 = import_manager.create_daily_import_job('2025-07-14', temp_dir)
        job2 = import_manager.create_bulk_import_job(temp_dir)
        
        # List all jobs
        all_jobs = import_manager.list_jobs()
        assert len(all_jobs) >= 2
        
        # List by status
        pending_jobs = import_manager.list_jobs(status_filter='pending')
        assert len(pending_jobs) >= 2
        
        # Test limit
        limited_jobs = import_manager.list_jobs(limit=1)
        assert len(limited_jobs) == 1
    
    def test_cancel_job(self, import_manager, sample_data_files, temp_dir):
        """Test job cancellation"""
        job_id = import_manager.create_daily_import_job('2025-07-14', temp_dir)
        
        # Cancel the job
        success = import_manager.cancel_job(job_id)
        assert success is True
        
        job = import_manager.get_job_status(job_id)
        assert job.status == 'cancelled'
        assert job.completed_at is not None
    
    def test_cleanup_old_jobs(self, import_manager, sample_data_files, temp_dir):
        """Test old job cleanup"""
        # Create a job and mark it as old
        job_id = import_manager.create_daily_import_job('2025-07-14', temp_dir)
        job = import_manager.get_job_status(job_id)
        
        # Manually set old creation date
        job.created_at = datetime.now() - timedelta(days=35)
        job.status = 'completed'
        import_manager._save_job_history()
        
        initial_count = len(import_manager.list_jobs())
        
        # Cleanup jobs older than 30 days
        import_manager.cleanup_old_jobs(days=30)
        
        final_count = len(import_manager.list_jobs())
        assert final_count < initial_count
    
    def test_date_range_filtering(self, import_manager, temp_dir):
        """Test date range filtering in bulk import"""
        # Create files for multiple dates
        dates = ['13072025', '********', '15072025']
        for date in dates:
            filename = f"Nifty Ticklist {date}.csv"
            file_path = temp_dir / filename
            
            sample_data = pd.DataFrame({
                'Time': [f'{date[:2]}-{date[2:4]}-{date[4:]} 09:15:00'],
                'Last Rate': [25170.0],
                'Volume': [100]
            })
            sample_data.to_csv(file_path, index=False)
        
        # Create bulk job with date range
        job_id = import_manager.create_bulk_import_job(
            source_dir=temp_dir,
            date_range=('2025-07-14', '2025-07-14')  # Only July 14
        )
        
        job = import_manager.get_job_status(job_id)
        assert job.files_total == 1  # Only one file should match
    
    def test_error_handling(self, import_manager, temp_dir):
        """Test error handling in import jobs"""
        # Create invalid CSV file
        invalid_file = temp_dir / "Nifty Ticklist ********.csv"
        with open(invalid_file, 'w') as f:
            f.write("invalid,csv,content\n")
        
        job_id = import_manager.create_daily_import_job('2025-07-14', temp_dir)
        
        # Execute job (should handle errors gracefully)
        success = import_manager.execute_job(job_id)
        
        job = import_manager.get_job_status(job_id)
        # Job should complete but may have failures
        assert job.status in ['completed', 'completed_with_errors', 'failed']
        assert job.files_processed >= 0

class TestImportJobDataClass:
    """Test the ImportJob data class"""
    
    def test_import_job_creation(self):
        """Test ImportJob creation and attributes"""
        job = ImportJob(
            job_id='test_job_123',
            job_type='daily',
            status='pending',
            created_at=datetime.now(),
            files_total=5
        )
        
        assert job.job_id == 'test_job_123'
        assert job.job_type == 'daily'
        assert job.status == 'pending'
        assert job.files_total == 5
        assert job.files_processed == 0
        assert job.files_successful == 0
        assert job.files_failed == 0

class TestFileInfoDataClass:
    """Test the FileInfo data class"""
    
    def test_file_info_creation(self, temp_dir):
        """Test FileInfo creation and attributes"""
        test_file = temp_dir / "test.csv"
        test_file.write_text("test content")
        
        file_info = FileInfo(
            path=test_file,
            index_name='nifty',
            date='********',
            size_bytes=100,
            modified_time=datetime.now()
        )
        
        assert file_info.path == test_file
        assert file_info.index_name == 'nifty'
        assert file_info.date == '********'
        assert file_info.size_bytes == 100
        assert file_info.processed is False
        assert file_info.error is None

if __name__ == '__main__':
    pytest.main([__file__, '-v'])
