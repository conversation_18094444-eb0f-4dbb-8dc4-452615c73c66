import os
import pandas as pd
from datetime import datetime
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.data.storage import TickDataStorage
from src.data.validation import TickDataValidator, validate_file_format
from src.utils.logging import get_logger, get_performance_logger, get_error_logger

logger = get_logger(__name__)
perf_logger = get_performance_logger()
error_logger = get_error_logger()

class TickDataIngestion:
    """Handles ingestion of tick data files into the database"""

    def __init__(self, db_connection, config=None):
        """
        Initialize tick data ingestion

        Args:
            db_connection: Database connection string or TickDataStorage instance
            config: Configuration object
        """
        if isinstance(db_connection, str):
            self.db = TickDataStorage(db_connection)
        else:
            self.db = db_connection

        self.config = config
        self.validator = TickDataValidator(config)

    def process_file(self, file_path: str, validate_file: bool = True) -> bool:
        """
        Process a single tick data file and store in database

        Args:
            file_path: Path to the tick data CSV file
            validate_file: Whether to validate file format before processing

        Returns:
            bool: True if successful, False otherwise
        """
        perf_logger.start_timer(f"process_file_{os.path.basename(file_path)}")

        try:
            filename = os.path.basename(file_path)
            logger.info(f"Processing file: {filename}")

            # Validate file format if requested
            if validate_file:
                file_validation = validate_file_format(file_path)
                if not file_validation.is_valid:
                    logger.error(f"File validation failed for {filename}: {file_validation.errors}")
                    return False

                if file_validation.warnings:
                    logger.warning(f"File validation warnings for {filename}: {file_validation.warnings}")

            # Extract index name and date from filename
            index_name, date_str = self._parse_filename(filename)

            # Read and preprocess the data
            df = pd.read_csv(file_path)
            processed_df = self._preprocess_tick_data(df, index_name)

            # Validate processed data
            data_validation = self.validator.validate_tick_data(processed_df, index_name)
            if not data_validation.is_valid:
                logger.error(f"Data validation failed for {filename}: {data_validation.errors}")
                return False

            if data_validation.warnings:
                logger.warning(f"Data validation warnings for {filename}: {data_validation.warnings}")

            # Store in database
            records_added = self.db.store_tick_data(processed_df)

            perf_logger.end_timer(
                f"process_file_{filename}",
                records_processed=records_added,
                file_size_mb=os.path.getsize(file_path) / (1024 * 1024)
            )

            logger.info(f"Successfully processed {records_added} records from {filename}")

            # Optional: Delete file after successful processing
            # if self.config and getattr(self.config.data, 'delete_after_processing', False):
            #     os.remove(file_path)
            #     logger.info(f"Deleted processed file: {filename}")

            return True

        except Exception as e:
            error_logger.log_exception("process_file", e, file_path=file_path)
            perf_logger.end_timer(f"process_file_{os.path.basename(file_path)}", success=False)
            return False
    
    def process_directory(self, directory_path: str) -> Dict[str, int]:
        """
        Process all tick data files in a directory
        
        Args:
            directory_path: Path to directory containing tick data files
            
        Returns:
            Dict with stats about processed files
        """
        results = {
            "total_files": 0,
            "successful": 0,
            "failed": 0,
            "records_processed": 0
        }
        
        for file in os.listdir(directory_path):
            if file.endswith('.csv') and not file.startswith('.'):
                results["total_files"] += 1
                file_path = os.path.join(directory_path, file)
                
                if self.process_file(file_path):
                    results["successful"] += 1
                else:
                    results["failed"] += 1
        
        return results
    
    def _parse_filename(self, filename: str) -> tuple:
        """Extract index name and date from filename"""
        # Example: "Nifty Ticklist May ********.csv"
        parts = filename.split()
        
        # Handle different naming patterns
        if "Nifty" in filename and "Bank" in filename:
            index_name = "bank_nifty"
        elif "Nifty" in filename and "Fin" in filename:
            index_name = "fin_nifty"
        elif "Nifty" in filename and "Midcap" in filename:
            index_name = "midcap_nifty"
        elif "Nifty" in filename and "Nxt" in filename:
            index_name = "nifty_next"
        elif "Nifty" in filename:
            index_name = "nifty"
        else:
            index_name = "unknown"
        
        # Extract date
        date_part = [p for p in parts if p.isdigit()]
        if date_part:
            date_str = date_part[0]
        else:
            # Try to find date in format like "********"
            import re
            date_match = re.search(r'(\d{8})', filename)
            if date_match:
                date_str = date_match.group(1)
            else:
                date_str = "unknown"
        
        return index_name, date_str
    
    def _preprocess_tick_data(self, df: pd.DataFrame, index_name: str) -> pd.DataFrame:
        """
        Preprocess tick data for storage
        
        Args:
            df: DataFrame containing tick data
            index_name: Name of the index
            
        Returns:
            Preprocessed DataFrame
        """
        # Rename columns to standardized format
        df = df.rename(columns={
            'Time': 'timestamp',
            'Last Rate': 'price',
            'Volume': 'volume'
        })
        
        # Add index name column
        df['index_name'] = index_name
        
        # Convert timestamp to datetime
        df['timestamp'] = pd.to_datetime(df['timestamp'], format='%d-%m-%Y %H:%M:%S')
        
        # Sort by timestamp
        df = df.sort_values('timestamp')
        
        return df