#!/usr/bin/env python3
"""
Simple demonstration script for the Tick Data Analysis Platform
"""
import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sqlite3
from sqlalchemy import text, create_engine

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import get_config
from src.data.storage import TickDataStorage
from src.ml.features import FeatureEngineering
from src.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

def create_demo_data():
    """Create demonstration tick data"""
    logger.info("Creating demonstration tick data...")
    
    # Create realistic tick data for multiple indices
    indices = ['nifty', 'bank_nifty', 'fin_nifty']
    base_prices = {'nifty': 19500, 'bank_nifty': 45000, 'fin_nifty': 20000}
    
    all_data = []
    start_time = datetime(2025, 7, 15, 9, 15, 0)
    n_ticks = 1000  # 1000 ticks per index
    
    for index_name in indices:
        logger.info(f"Generating data for {index_name}")
        
        # Generate timestamps (every 2 seconds during market hours)
        timestamps = [start_time + timedelta(seconds=i*2) for i in range(n_ticks)]
        
        # Generate realistic price movement using random walk
        base_price = base_prices[index_name]
        price_changes = np.random.normal(0, 0.05, n_ticks)  # 0.05% volatility per tick
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change/100)
            prices.append(new_price)
        
        # Generate realistic volume (log-normal distribution)
        volumes = np.random.lognormal(mean=6, sigma=0.5, size=n_ticks).astype(int)
        volumes = np.clip(volumes, 100, 5000)  # Realistic volume range
        
        # Create DataFrame for this index
        index_data = pd.DataFrame({
            'timestamp': timestamps,
            'index_name': index_name,
            'price': prices,
            'volume': volumes
        })
        
        all_data.append(index_data)
    
    # Combine all data
    combined_data = pd.concat(all_data, ignore_index=True)
    logger.info(f"Generated {len(combined_data)} total tick records")
    
    return combined_data

def setup_demo_database():
    """Setup SQLite database for demonstration"""
    logger.info("Setting up demonstration database...")
    
    # Create SQLite database
    db_path = "demo_tickdata.db"
    connection_string = f"sqlite:///{db_path}"
    
    # Create storage instance
    storage = TickDataStorage(connection_string)
    
    # Create tables manually for SQLite
    engine = storage.engine
    with engine.connect() as conn:
        # Create tick_data table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS tick_data (
            timestamp DATETIME NOT NULL,
            index_name VARCHAR(50) NOT NULL,
            price DECIMAL(10, 2) NOT NULL,
            volume INTEGER NOT NULL
        )
        """))
        
        # Create index for better performance
        conn.execute(text("""
        CREATE INDEX IF NOT EXISTS idx_tick_data_timestamp_index 
        ON tick_data (timestamp, index_name)
        """))
        
        conn.commit()
    
    logger.info(f"Created SQLite database: {db_path}")
    return storage, connection_string

def demonstrate_feature_engineering(tick_data):
    """Demonstrate feature engineering capabilities"""
    logger.info("Demonstrating feature engineering...")
    
    # Initialize feature engineering
    fe = FeatureEngineering()
    
    # Extract features for each index
    results = {}
    
    for index_name in tick_data['index_name'].unique():
        logger.info(f"Extracting features for {index_name}")
        
        # Get data for this index
        index_data = tick_data[tick_data['index_name'] == index_name].copy()
        index_data = index_data.sort_values('timestamp').reset_index(drop=True)
        
        # Extract all features
        features = fe.extract_all_features(index_data)
        
        # Store results
        results[index_name] = {
            'tick_count': len(index_data),
            'feature_count': len(features.columns),
            'feature_names': list(features.columns),
            'sample_features': features.head()
        }
        
        logger.info(f"Extracted {len(features.columns)} features from {len(index_data)} ticks")
    
    return results

def demonstrate_data_storage(storage, tick_data):
    """Demonstrate data storage capabilities"""
    logger.info("Demonstrating data storage...")
    
    # Store tick data
    records_stored = storage.store_tick_data(tick_data)
    logger.info(f"Stored {records_stored} tick data records")
    
    # Demonstrate data retrieval
    logger.info("Demonstrating data retrieval...")
    
    # Get data for each index
    for index_name in tick_data['index_name'].unique():
        retrieved_data = storage.get_tick_data(index_name, '2025-07-15', '2025-07-15')
        logger.info(f"Retrieved {len(retrieved_data)} records for {index_name}")
    
    # Get database statistics
    with storage.engine.connect() as conn:
        result = conn.execute(text("""
            SELECT 
                index_name,
                COUNT(*) as record_count,
                MIN(timestamp) as first_tick,
                MAX(timestamp) as last_tick,
                AVG(price) as avg_price,
                SUM(volume) as total_volume
            FROM tick_data 
            GROUP BY index_name
        """))
        
        stats = result.fetchall()
        
        logger.info("Database Statistics:")
        for row in stats:
            logger.info(f"  {row[0]}: {row[1]} records, avg price: {row[4]:.2f}, total volume: {row[5]}")

def main():
    """Main demonstration function"""
    logger.info("🚀 Starting Tick Data Analysis Platform Demonstration")
    
    try:
        # Step 1: Setup database
        logger.info("Step 1: Setting up demonstration database")
        storage, connection_string = setup_demo_database()
        
        # Step 2: Create demonstration data
        logger.info("Step 2: Creating demonstration tick data")
        tick_data = create_demo_data()
        
        # Step 3: Demonstrate data storage
        logger.info("Step 3: Demonstrating data storage")
        demonstrate_data_storage(storage, tick_data)
        
        # Step 4: Demonstrate feature engineering
        logger.info("Step 4: Demonstrating feature engineering")
        feature_results = demonstrate_feature_engineering(tick_data)
        
        # Step 5: Show results summary
        logger.info("Step 5: Results Summary")
        logger.info("=" * 50)
        
        logger.info("📊 Data Summary:")
        logger.info(f"  Total tick records: {len(tick_data):,}")
        logger.info(f"  Indices processed: {len(tick_data['index_name'].unique())}")
        logger.info(f"  Time range: {tick_data['timestamp'].min()} to {tick_data['timestamp'].max()}")
        
        logger.info("\n🔧 Feature Engineering Results:")
        for index_name, results in feature_results.items():
            logger.info(f"  {index_name}:")
            logger.info(f"    Tick count: {results['tick_count']:,}")
            logger.info(f"    Features extracted: {results['feature_count']}")
            logger.info(f"    Sample features: {results['feature_names'][:5]}...")
        
        logger.info("\n✅ Demonstration completed successfully!")
        logger.info("\nNext steps:")
        logger.info("1. Set up PostgreSQL with TimescaleDB for production")
        logger.info("2. Process real tick data files")
        logger.info("3. Train machine learning models")
        logger.info("4. Generate trading strategies")
        
        return True
        
    except Exception as e:
        logger.error(f"Demonstration failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
