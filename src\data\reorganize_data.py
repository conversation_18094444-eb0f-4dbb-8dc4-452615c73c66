import os
import shutil
import re
from pathlib import Path

def reorganize_tick_data():
    """
    Reorganize tick data files from sampledata folder to the new structure
    """
    # Create new directory structure
    base_dir = Path("tick_data")
    
    # Create subdirectories for each index
    indices = ["nifty", "bank_nifty", "fin_nifty", "midcap_nifty", "nifty_next"]
    for index in indices:
        os.makedirs(base_dir / index, exist_ok=True)
    
    # Source directory
    source_dir = Path("sampledata")
    
    # Skip if source directory doesn't exist
    if not source_dir.exists():
        print(f"Source directory {source_dir} does not exist.")
        return
    
    # Process each file in the source directory
    for file_path in source_dir.glob("*.csv"):
        filename = file_path.name
        
        # Determine index type
        if "Nifty Nxt" in filename:
            dest_dir = base_dir / "nifty_next"
        elif "Bank Nifty" in filename:
            dest_dir = base_dir / "bank_nifty"
        elif "Fin Nifty" in filename:
            dest_dir = base_dir / "fin_nifty"
        elif "Midcap Nifty" in filename:
            dest_dir = base_dir / "midcap_nifty"
        elif "Nifty Ticklist" in filename:
            dest_dir = base_dir / "nifty"
        else:
            print(f"Unknown index type for file: {filename}")
            continue
        
        # Copy file to destination
        shutil.copy2(file_path, dest_dir / filename)
        print(f"Copied {filename} to {dest_dir}")
    
    print("Data reorganization complete.")
    print("You can now delete the original 'sampledata' directory after verifying the files.")

if __name__ == "__main__":
    reorganize_tick_data()