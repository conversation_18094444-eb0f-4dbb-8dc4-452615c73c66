"""
Logging utilities for the tick data analysis platform
"""
import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional
import structlog
from datetime import datetime

class ColoredFormatter(logging.Formatter):
    """Colored formatter for console output"""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        """Format log record with colors"""
        log_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset_color = self.COLORS['RESET']
        
        # Add color to level name
        record.levelname = f"{log_color}{record.levelname}{reset_color}"
        
        return super().format(record)

class StructuredLogger:
    """Structured logger using structlog"""
    
    def __init__(self, name: str, level: str = "INFO"):
        """
        Initialize structured logger
        
        Args:
            name: Logger name
            level: Logging level
        """
        self.name = name
        self.level = level
        self._setup_structlog()
        self.logger = structlog.get_logger(name)
    
    def _setup_structlog(self):
        """Setup structlog configuration"""
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="ISO"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message"""
        self.logger.critical(message, **kwargs)

class LoggingManager:
    """Manages logging configuration for the entire application"""
    
    def __init__(self, config=None):
        """
        Initialize logging manager
        
        Args:
            config: Configuration object
        """
        self.config = config
        self._loggers = {}
        self._setup_root_logger()
    
    def _setup_root_logger(self):
        """Setup root logger configuration"""
        # Clear any existing handlers
        root_logger = logging.getLogger()
        root_logger.handlers.clear()
        
        # Set level
        level = getattr(logging, self.config.logging.level.upper() if self.config else "INFO")
        root_logger.setLevel(level)
        
        # Add handlers
        handlers = self._create_handlers()
        for handler in handlers:
            root_logger.addHandler(handler)
    
    def _create_handlers(self) -> list:
        """Create logging handlers based on configuration"""
        handlers = []
        
        # Console handler
        if not self.config or self.config.logging.console_enabled:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(ColoredFormatter(
                self.config.logging.format if self.config else 
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            ))
            handlers.append(console_handler)
        
        # File handler
        if self.config and self.config.logging.file_enabled:
            # Ensure log directory exists
            log_file = Path(self.config.logging.log_file)
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Create rotating file handler
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=self.config.logging.max_file_size_mb * 1024 * 1024,
                backupCount=self.config.logging.backup_count
            )
            
            file_handler.setFormatter(logging.Formatter(
                self.config.logging.format
            ))
            handlers.append(file_handler)
        
        return handlers
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        Get logger instance
        
        Args:
            name: Logger name
            
        Returns:
            Logger instance
        """
        if name not in self._loggers:
            self._loggers[name] = logging.getLogger(name)
        
        return self._loggers[name]
    
    def get_structured_logger(self, name: str) -> StructuredLogger:
        """
        Get structured logger instance
        
        Args:
            name: Logger name
            
        Returns:
            StructuredLogger instance
        """
        level = self.config.logging.level if self.config else "INFO"
        return StructuredLogger(name, level)

class PerformanceLogger:
    """Logger for performance metrics and monitoring"""
    
    def __init__(self, logger_name: str = "performance"):
        """
        Initialize performance logger
        
        Args:
            logger_name: Name of the logger
        """
        self.logger = logging.getLogger(logger_name)
        self._start_times = {}
    
    def start_timer(self, operation: str):
        """
        Start timing an operation
        
        Args:
            operation: Name of the operation
        """
        self._start_times[operation] = datetime.now()
        self.logger.debug(f"Started operation: {operation}")
    
    def end_timer(self, operation: str, **kwargs):
        """
        End timing an operation and log the duration
        
        Args:
            operation: Name of the operation
            **kwargs: Additional context to log
        """
        if operation not in self._start_times:
            self.logger.warning(f"No start time found for operation: {operation}")
            return
        
        start_time = self._start_times.pop(operation)
        duration = (datetime.now() - start_time).total_seconds()
        
        self.logger.info(
            f"Operation completed: {operation}",
            extra={
                'operation': operation,
                'duration_seconds': duration,
                **kwargs
            }
        )
    
    def log_metric(self, metric_name: str, value: float, **kwargs):
        """
        Log a performance metric
        
        Args:
            metric_name: Name of the metric
            value: Metric value
            **kwargs: Additional context
        """
        self.logger.info(
            f"Metric: {metric_name} = {value}",
            extra={
                'metric_name': metric_name,
                'metric_value': value,
                **kwargs
            }
        )

class ErrorLogger:
    """Specialized logger for error tracking and debugging"""
    
    def __init__(self, logger_name: str = "errors"):
        """
        Initialize error logger
        
        Args:
            logger_name: Name of the logger
        """
        self.logger = logging.getLogger(logger_name)
    
    def log_exception(self, operation: str, exception: Exception, **kwargs):
        """
        Log an exception with context
        
        Args:
            operation: Operation that failed
            exception: Exception that occurred
            **kwargs: Additional context
        """
        self.logger.error(
            f"Exception in {operation}: {str(exception)}",
            extra={
                'operation': operation,
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
                **kwargs
            },
            exc_info=True
        )
    
    def log_validation_error(self, data_type: str, error_details: dict):
        """
        Log data validation errors
        
        Args:
            data_type: Type of data being validated
            error_details: Details about the validation error
        """
        self.logger.warning(
            f"Data validation error for {data_type}",
            extra={
                'data_type': data_type,
                'validation_errors': error_details
            }
        )

# Global logging manager instance
_logging_manager: Optional[LoggingManager] = None

def setup_logging(config=None):
    """
    Setup global logging configuration
    
    Args:
        config: Configuration object
    """
    global _logging_manager
    _logging_manager = LoggingManager(config)

def get_logger(name: str) -> logging.Logger:
    """
    Get logger instance
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    if _logging_manager is None:
        setup_logging()
    
    return _logging_manager.get_logger(name)

def get_structured_logger(name: str) -> StructuredLogger:
    """
    Get structured logger instance
    
    Args:
        name: Logger name
        
    Returns:
        StructuredLogger instance
    """
    if _logging_manager is None:
        setup_logging()
    
    return _logging_manager.get_structured_logger(name)

def get_performance_logger(name: str = "performance") -> PerformanceLogger:
    """
    Get performance logger instance
    
    Args:
        name: Logger name
        
    Returns:
        PerformanceLogger instance
    """
    return PerformanceLogger(name)

def get_error_logger(name: str = "errors") -> ErrorLogger:
    """
    Get error logger instance
    
    Args:
        name: Logger name
        
    Returns:
        ErrorLogger instance
    """
    return ErrorLogger(name)
