import pandas as pd
import logging
from typing import List, Dict, Any, Optional
from sqlalchemy import create_engine, text, MetaData, Table, Column, DateTime, String, Numeric, Integer, Index
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.pool import QueuePool
import sys
from pathlib import Path

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.logging import get_logger, get_performance_logger, get_error_logger

logger = get_logger(__name__)
perf_logger = get_performance_logger()
error_logger = get_error_logger()

class TickDataStorage:
    """Handles storage and retrieval of tick data in the database"""

    def __init__(self, db_connection_string, pool_size=10, max_overflow=20):
        """
        Initialize database connection

        Args:
            db_connection_string: Database connection string
            pool_size: Connection pool size
            max_overflow: Maximum overflow connections
        """
        try:
            self.engine = create_engine(
                db_connection_string,
                poolclass=QueuePool,
                pool_size=pool_size,
                max_overflow=max_overflow,
                pool_timeout=30,
                pool_recycle=3600,
                echo=False  # Set to True for SQL debugging
            )

            # Test connection on initialization
            self._test_connection()
            logger.info("Database connection initialized successfully")

        except Exception as e:
            error_logger.log_exception("database_initialization", e)
            raise

    def init_database(self):
        """Initialize database by creating tables"""
        self.create_tables()
        logger.info("Database initialized successfully")

    def create_tables(self):
        """Create database tables for SQLite"""
        try:
            with self.engine.connect() as conn:
                # Create tick_data table
                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS tick_data (
                    timestamp DATETIME NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    price DECIMAL(10, 2) NOT NULL,
                    volume INTEGER NOT NULL
                );
                """))

                # Create indexes
                conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_tick_data_timestamp ON tick_data (timestamp);
                """))
                conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_tick_data_index_name ON tick_data (index_name);
                """))
                conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_tick_data_composite ON tick_data (index_name, timestamp);
                """))

                # Create derived_features table
                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS derived_features (
                    timestamp DATETIME NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    feature_name VARCHAR(100) NOT NULL,
                    feature_value FLOAT NOT NULL
                );
                """))

                # Create predictions table
                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS predictions (
                    generated_at DATETIME NOT NULL,
                    target_date DATE NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    prediction_type VARCHAR(20) NOT NULL,
                    direction VARCHAR(10) NOT NULL,
                    magnitude FLOAT NOT NULL,
                    confidence FLOAT NOT NULL,
                    key_levels TEXT,
                    actual_outcome TEXT
                );
                """))

                # Create professional trading analysis tables
                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS market_structure (
                    analysis_date DATE NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    support_levels TEXT NOT NULL,
                    resistance_levels TEXT NOT NULL,
                    key_levels_strength INTEGER NOT NULL,
                    volume_profile TEXT NOT NULL,
                    order_flow_imbalance FLOAT NOT NULL,
                    PRIMARY KEY (analysis_date, index_name)
                );
                """))

                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS trading_signals (
                    generated_at DATETIME NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    signal_type VARCHAR(30) NOT NULL,
                    entry_price FLOAT NOT NULL,
                    target_price FLOAT NOT NULL,
                    stop_loss FLOAT NOT NULL,
                    confidence FLOAT NOT NULL,
                    reason TEXT NOT NULL,
                    status VARCHAR(20) DEFAULT 'active',
                    actual_outcome TEXT
                );
                """))

                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS volume_analysis (
                    timestamp DATETIME NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    volume_spike_ratio FLOAT,
                    exhaustion_signal BOOLEAN DEFAULT FALSE,
                    buying_pressure FLOAT,
                    selling_pressure FLOAT,
                    order_flow_imbalance FLOAT,
                    momentum_shift VARCHAR(20),
                    PRIMARY KEY (timestamp, index_name)
                );
                """))

                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS level_formation (
                    level_date DATE NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    level_price FLOAT NOT NULL,
                    level_type VARCHAR(20) NOT NULL,
                    strength_score FLOAT NOT NULL,
                    touch_count INTEGER NOT NULL,
                    volume_at_level FLOAT NOT NULL,
                    formation_period_days INTEGER NOT NULL,
                    PRIMARY KEY (level_date, index_name, level_price, level_type)
                );
                """))

                conn.commit()
                logger.info("Database tables created successfully")

        except Exception as e:
            error_logger.log_exception("create_tables", e)
            raise

    def _test_connection(self):
        """Test database connection"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.debug("Database connection test successful")
        except Exception as e:
            error_logger.log_exception("database_connection_test", e)
            raise
        
    def store_tick_data(self, df: pd.DataFrame) -> int:
        """
        Store tick data in the database

        Args:
            df: DataFrame containing preprocessed tick data

        Returns:
            Number of records stored
        """
        if df.empty:
            logger.warning("Attempted to store empty DataFrame")
            return 0

        perf_logger.start_timer("store_tick_data")

        try:
            # Validate data before storing
            self._validate_tick_data(df)

            # Use the TimescaleDB hypertable for efficient time-series storage
            records_stored = len(df)

            df.to_sql(
                'tick_data',
                self.engine,
                if_exists='append',
                index=False,
                method='multi',
                chunksize=500  # Reduced to avoid SQLite variable limits
            )

            perf_logger.end_timer("store_tick_data", records_count=records_stored)
            logger.info(f"Stored {records_stored} tick data records")

            return records_stored

        except Exception as e:
            error_logger.log_exception("store_tick_data", e, records_count=len(df))
            raise

    def _validate_tick_data(self, df: pd.DataFrame):
        """
        Validate tick data before storage

        Args:
            df: DataFrame to validate
        """
        required_columns = ['timestamp', 'index_name', 'price', 'volume']
        missing_columns = set(required_columns) - set(df.columns)

        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Check for null values in critical columns
        null_counts = df[required_columns].isnull().sum()
        if null_counts.any():
            error_details = null_counts[null_counts > 0].to_dict()
            error_logger.log_validation_error("tick_data", {"null_values": error_details})
            raise ValueError(f"Null values found in required columns: {error_details}")

        # Validate data types and ranges
        if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
            raise ValueError("timestamp column must be datetime type")

        if (df['price'] <= 0).any():
            raise ValueError("price values must be positive")

        if (df['volume'] < 0).any():
            raise ValueError("volume values must be non-negative")

        logger.debug("Tick data validation passed")
    
    def get_tick_data(self, index_name: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Retrieve tick data for a specific index and date range

        Args:
            index_name: Name of the index
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format

        Returns:
            DataFrame containing tick data
        """
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT timestamp, price, volume, index_name
                    FROM tick_data
                    WHERE index_name = :index_name
                    AND DATE(timestamp) BETWEEN :start_date AND :end_date
                    ORDER BY timestamp
                """), {
                    'index_name': index_name,
                    'start_date': start_date,
                    'end_date': end_date
                })

                # Convert to DataFrame
                rows = result.fetchall()
                if rows:
                    df = pd.DataFrame(rows, columns=['timestamp', 'price', 'volume', 'index_name'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    return df
                else:
                    return pd.DataFrame(columns=['timestamp', 'price', 'volume', 'index_name'])

        except Exception as e:
            logger.error(f"Database error retrieving tick data: {str(e)}")
            raise
    
    def create_schema(self):
        """Create the database schema if it doesn't exist"""
        try:
            # Create tick_data hypertable optimized for time-series data
            with self.engine.connect() as conn:
                # Create extension if it doesn't exist (PostgreSQL only)
                if 'postgresql' in str(self.engine.url):
                    conn.execute(text("CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;"))
                
                # Create tick_data table
                if 'sqlite' in str(self.engine.url):
                    # SQLite-compatible schema
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS tick_data (
                        timestamp TEXT NOT NULL,
                        index_name TEXT NOT NULL,
                        price REAL NOT NULL,
                        volume INTEGER NOT NULL,
                        PRIMARY KEY (timestamp, index_name)
                    );
                    """))
                else:
                    # PostgreSQL schema
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS tick_data (
                        timestamp TIMESTAMPTZ NOT NULL,
                        index_name VARCHAR(50) NOT NULL,
                        price DECIMAL(10, 2) NOT NULL,
                        volume INTEGER NOT NULL,
                        PRIMARY KEY (timestamp, index_name)
                    );
                    """))
                
                # Convert to hypertable (PostgreSQL/TimescaleDB only)
                if 'postgresql' in str(self.engine.url):
                    conn.execute(text("""
                    SELECT create_hypertable('tick_data', 'timestamp',
                                            if_not_exists => TRUE,
                                            create_default_indexes => TRUE);
                    """))
                
                # Create indexes
                conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_tick_data_index_name ON tick_data (index_name);
                """))
                
                # Create derived_features table
                if 'sqlite' in str(self.engine.url):
                    # SQLite-compatible schema
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS derived_features (
                        timestamp TEXT NOT NULL,
                        index_name TEXT NOT NULL,
                        feature_name TEXT NOT NULL,
                        feature_value REAL NOT NULL
                    );
                    """))
                else:
                    # PostgreSQL schema
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS derived_features (
                        timestamp TIMESTAMPTZ NOT NULL,
                        index_name VARCHAR(50) NOT NULL,
                        feature_name VARCHAR(100) NOT NULL,
                        feature_value FLOAT NOT NULL
                    );
                    """))
                
                # Convert to hypertable (PostgreSQL/TimescaleDB only)
                if 'postgresql' in str(self.engine.url):
                    conn.execute(text("""
                    SELECT create_hypertable('derived_features', 'timestamp',
                                            if_not_exists => TRUE,
                                            create_default_indexes => TRUE);
                    """))
                
                # Create predictions table
                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS predictions (
                    generated_at TIMESTAMPTZ NOT NULL,
                    target_date DATE NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    prediction_type VARCHAR(20) NOT NULL,
                    direction VARCHAR(10) NOT NULL,
                    magnitude FLOAT NOT NULL,
                    confidence FLOAT NOT NULL,
                    key_levels JSONB,
                    actual_outcome JSONB
                );
                """))
                
                # Create index on target_date
                conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_predictions_target_date 
                ON predictions (target_date);
                """))
                
                conn.commit()
                
            logger.info("Database schema created successfully")
            return True
            
        except SQLAlchemyError as e:
            logger.error(f"Error creating database schema: {str(e)}")
            return False
    
    def optimize_database(self):
        """Run optimization routines for the database"""
        try:
            with self.engine.connect() as conn:
                # Set chunk time interval for better performance with our data pattern
                conn.execute(text("""
                SELECT set_chunk_time_interval('tick_data', INTERVAL '1 day');
                """))
                
                # Add compression for older data
                conn.execute(text("""
                ALTER TABLE tick_data SET (
                    timescaledb.compress,
                    timescaledb.compress_segmentby = 'index_name'
                );
                """))
                
                # Create compression policy (compress data older than 7 days)
                conn.execute(text("""
                SELECT add_compression_policy('tick_data', INTERVAL '7 days');
                """))
                
                conn.commit()

            logger.info("Database optimization completed")
        except Exception as e:
            error_logger.log_exception("optimize_database", e)

    def get_database_stats(self):
        """Get database statistics"""
        try:
            with self.engine.connect() as conn:
                # Get total records
                result = conn.execute(text("SELECT COUNT(*) FROM tick_data")).fetchone()
                total_records = result[0] if result else 0

                # Get date range
                result = conn.execute(text("""
                    SELECT MIN(timestamp), MAX(timestamp) FROM tick_data
                """)).fetchone()

                if result and result[0] and result[1]:
                    # Handle both datetime objects and strings
                    start_date = result[0]
                    end_date = result[1]

                    if hasattr(start_date, 'strftime'):
                        start_str = start_date.strftime('%Y-%m-%d')
                    else:
                        start_str = str(start_date)[:10]  # Take first 10 chars for date

                    if hasattr(end_date, 'strftime'):
                        end_str = end_date.strftime('%Y-%m-%d')
                    else:
                        end_str = str(end_date)[:10]  # Take first 10 chars for date

                    date_range = f"{start_str} to {end_str}"
                else:
                    date_range = "No data"

                # Get indices count
                result = conn.execute(text("SELECT COUNT(DISTINCT index_name) FROM tick_data")).fetchone()
                indices_count = result[0] if result else 0

                return {
                    'total_records': total_records,
                    'date_range': date_range,
                    'indices_count': indices_count
                }
        except Exception as e:
            error_logger.log_exception("get_database_stats", e)
            return {'total_records': 0, 'date_range': 'Error', 'indices_count': 0}

    def get_data_quality_stats(self):
        """Get data quality statistics"""
        try:
            with self.engine.connect() as conn:
                # Get records per index
                result = conn.execute(text("""
                    SELECT index_name, COUNT(*)
                    FROM tick_data
                    GROUP BY index_name
                """)).fetchall()

                records_per_index = {row[0]: row[1] for row in result}

                # Get latest timestamp
                result = conn.execute(text("SELECT MAX(timestamp) FROM tick_data")).fetchone()
                latest_timestamp = result[0].isoformat() if result and result[0] else "No data"

                # Simple data gaps check (count of distinct dates vs expected)
                result = conn.execute(text("""
                    SELECT COUNT(DISTINCT DATE(timestamp)) FROM tick_data
                """)).fetchone()
                unique_dates = result[0] if result else 0

                return {
                    'records_per_index': records_per_index,
                    'latest_timestamp': latest_timestamp,
                    'data_gaps': 0,  # Simplified for now
                    'unique_dates': unique_dates
                }
        except Exception as e:
            error_logger.log_exception("get_data_quality_stats", e)
            return {'records_per_index': {}, 'latest_timestamp': 'Error', 'data_gaps': 0}

    def get_latest_data(self, limit=1000):
        """Get latest data from all indices"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT timestamp, index_name, price, volume
                    FROM tick_data
                    ORDER BY timestamp DESC
                    LIMIT :limit
                """), {'limit': limit}).fetchall()

                if result:
                    df = pd.DataFrame(result, columns=['timestamp', 'index_name', 'price', 'volume'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    return df
                else:
                    return pd.DataFrame(columns=['timestamp', 'index_name', 'price', 'volume'])
        except Exception as e:
            error_logger.log_exception("get_latest_data", e)
            return pd.DataFrame(columns=['timestamp', 'index_name', 'price', 'volume'])

    def get_table_names(self):
        """Get list of table names in the database"""
        try:
            with self.engine.connect() as conn:
                if 'sqlite' in str(self.engine.url):
                    result = conn.execute(text("""
                        SELECT name FROM sqlite_master WHERE type='table'
                    """)).fetchall()
                else:
                    result = conn.execute(text("""
                        SELECT table_name FROM information_schema.tables
                        WHERE table_schema = 'public'
                    """)).fetchall()

                return [row[0] for row in result]
        except Exception as e:
            error_logger.log_exception("get_table_names", e)
            return []

    def clear_all_data(self):
        """Clear all data from the database"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text("DELETE FROM tick_data"))
                conn.execute(text("DELETE FROM derived_features"))
                conn.commit()
                logger.info("All data cleared from database")
        except Exception as e:
            error_logger.log_exception("clear_all_data", e)
            raise

    def count_records_before_date(self, date_str):
        """Count records before a specific date"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT COUNT(*) FROM tick_data
                    WHERE DATE(timestamp) < :date
                """), {'date': date_str}).fetchone()

                return result[0] if result else 0
        except Exception as e:
            error_logger.log_exception("count_records_before_date", e)
            return 0

    def delete_records_before_date(self, date_str):
        """Delete records before a specific date"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    DELETE FROM tick_data
                    WHERE DATE(timestamp) < :date
                """), {'date': date_str})

                deleted_count = result.rowcount
                conn.commit()
                logger.info(f"Deleted {deleted_count} records before {date_str}")
                return deleted_count
        except Exception as e:
            error_logger.log_exception("delete_records_before_date", e)
            return 0

    def optimize_database(self):
        """Optimize database performance"""
        try:
            with self.engine.connect() as conn:
                if 'sqlite' in str(self.engine.url):
                    conn.execute(text("VACUUM"))
                    conn.execute(text("ANALYZE"))
                else:
                    conn.execute(text("VACUUM ANALYZE tick_data"))
                    conn.execute(text("VACUUM ANALYZE derived_features"))

                conn.commit()
                logger.info("Database optimization completed")
        except Exception as e:
            error_logger.log_exception("optimize_database", e)
            return True
            
        except SQLAlchemyError as e:
            logger.error(f"Error optimizing database: {str(e)}")
            return False