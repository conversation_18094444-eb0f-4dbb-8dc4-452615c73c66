import pandas as pd
import logging
from typing import List, Dict, Any, Optional
from sqlalchemy import create_engine, text, MetaData, Table, Column, DateTime, String, Numeric, Integer, Index
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.pool import QueuePool
import sys
from pathlib import Path

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.logging import get_logger, get_performance_logger, get_error_logger

logger = get_logger(__name__)
perf_logger = get_performance_logger()
error_logger = get_error_logger()

class TickDataStorage:
    """Handles storage and retrieval of tick data in the database"""

    def __init__(self, db_connection_string, pool_size=10, max_overflow=20):
        """
        Initialize database connection

        Args:
            db_connection_string: Database connection string
            pool_size: Connection pool size
            max_overflow: Maximum overflow connections
        """
        try:
            self.engine = create_engine(
                db_connection_string,
                poolclass=Queue<PERSON>ool,
                pool_size=pool_size,
                max_overflow=max_overflow,
                pool_timeout=30,
                pool_recycle=3600,
                echo=False  # Set to True for SQL debugging
            )

            # Test connection on initialization
            self._test_connection()
            logger.info("Database connection initialized successfully")

        except Exception as e:
            error_logger.log_exception("database_initialization", e)
            raise

    def create_tables(self):
        """Create database tables for SQLite"""
        try:
            with self.engine.connect() as conn:
                # Create tick_data table
                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS tick_data (
                    timestamp DATETIME NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    price DECIMAL(10, 2) NOT NULL,
                    volume INTEGER NOT NULL
                );
                """))

                # Create indexes
                conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_tick_data_timestamp ON tick_data (timestamp);
                """))
                conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_tick_data_index_name ON tick_data (index_name);
                """))
                conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_tick_data_composite ON tick_data (index_name, timestamp);
                """))

                # Create derived_features table
                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS derived_features (
                    timestamp DATETIME NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    feature_name VARCHAR(100) NOT NULL,
                    feature_value FLOAT NOT NULL
                );
                """))

                # Create predictions table
                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS predictions (
                    generated_at DATETIME NOT NULL,
                    target_date DATE NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    prediction_type VARCHAR(20) NOT NULL,
                    direction VARCHAR(10) NOT NULL,
                    magnitude FLOAT NOT NULL,
                    confidence FLOAT NOT NULL,
                    key_levels TEXT,
                    actual_outcome TEXT
                );
                """))

                # Create professional trading analysis tables
                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS market_structure (
                    analysis_date DATE NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    support_levels TEXT NOT NULL,
                    resistance_levels TEXT NOT NULL,
                    key_levels_strength INTEGER NOT NULL,
                    volume_profile TEXT NOT NULL,
                    order_flow_imbalance FLOAT NOT NULL,
                    PRIMARY KEY (analysis_date, index_name)
                );
                """))

                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS trading_signals (
                    generated_at DATETIME NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    signal_type VARCHAR(30) NOT NULL,
                    entry_price FLOAT NOT NULL,
                    target_price FLOAT NOT NULL,
                    stop_loss FLOAT NOT NULL,
                    confidence FLOAT NOT NULL,
                    reason TEXT NOT NULL,
                    status VARCHAR(20) DEFAULT 'active',
                    actual_outcome TEXT
                );
                """))

                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS volume_analysis (
                    timestamp DATETIME NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    volume_spike_ratio FLOAT,
                    exhaustion_signal BOOLEAN DEFAULT FALSE,
                    buying_pressure FLOAT,
                    selling_pressure FLOAT,
                    order_flow_imbalance FLOAT,
                    momentum_shift VARCHAR(20),
                    PRIMARY KEY (timestamp, index_name)
                );
                """))

                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS level_formation (
                    level_date DATE NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    level_price FLOAT NOT NULL,
                    level_type VARCHAR(20) NOT NULL,
                    strength_score FLOAT NOT NULL,
                    touch_count INTEGER NOT NULL,
                    volume_at_level FLOAT NOT NULL,
                    formation_period_days INTEGER NOT NULL,
                    PRIMARY KEY (level_date, index_name, level_price, level_type)
                );
                """))

                conn.commit()
                logger.info("Database tables created successfully")

        except Exception as e:
            error_logger.log_exception("create_tables", e)
            raise

    def _test_connection(self):
        """Test database connection"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.debug("Database connection test successful")
        except Exception as e:
            error_logger.log_exception("database_connection_test", e)
            raise
        
    def store_tick_data(self, df: pd.DataFrame) -> int:
        """
        Store tick data in the database

        Args:
            df: DataFrame containing preprocessed tick data

        Returns:
            Number of records stored
        """
        if df.empty:
            logger.warning("Attempted to store empty DataFrame")
            return 0

        perf_logger.start_timer("store_tick_data")

        try:
            # Validate data before storing
            self._validate_tick_data(df)

            # Use the TimescaleDB hypertable for efficient time-series storage
            records_stored = len(df)

            df.to_sql(
                'tick_data',
                self.engine,
                if_exists='append',
                index=False,
                method='multi',
                chunksize=500  # Reduced to avoid SQLite variable limits
            )

            perf_logger.end_timer("store_tick_data", records_count=records_stored)
            logger.info(f"Stored {records_stored} tick data records")

            return records_stored

        except Exception as e:
            error_logger.log_exception("store_tick_data", e, records_count=len(df))
            raise

    def _validate_tick_data(self, df: pd.DataFrame):
        """
        Validate tick data before storage

        Args:
            df: DataFrame to validate
        """
        required_columns = ['timestamp', 'index_name', 'price', 'volume']
        missing_columns = set(required_columns) - set(df.columns)

        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Check for null values in critical columns
        null_counts = df[required_columns].isnull().sum()
        if null_counts.any():
            error_details = null_counts[null_counts > 0].to_dict()
            error_logger.log_validation_error("tick_data", {"null_values": error_details})
            raise ValueError(f"Null values found in required columns: {error_details}")

        # Validate data types and ranges
        if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
            raise ValueError("timestamp column must be datetime type")

        if (df['price'] <= 0).any():
            raise ValueError("price values must be positive")

        if (df['volume'] < 0).any():
            raise ValueError("volume values must be non-negative")

        logger.debug("Tick data validation passed")
    
    def get_tick_data(self, index_name: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Retrieve tick data for a specific index and date range

        Args:
            index_name: Name of the index
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format

        Returns:
            DataFrame containing tick data
        """
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT timestamp, price, volume, index_name
                    FROM tick_data
                    WHERE index_name = :index_name
                    AND DATE(timestamp) BETWEEN :start_date AND :end_date
                    ORDER BY timestamp
                """), {
                    'index_name': index_name,
                    'start_date': start_date,
                    'end_date': end_date
                })

                # Convert to DataFrame
                rows = result.fetchall()
                if rows:
                    df = pd.DataFrame(rows, columns=['timestamp', 'price', 'volume', 'index_name'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    return df
                else:
                    return pd.DataFrame(columns=['timestamp', 'price', 'volume', 'index_name'])

        except Exception as e:
            logger.error(f"Database error retrieving tick data: {str(e)}")
            raise
    
    def create_schema(self):
        """Create the database schema if it doesn't exist"""
        try:
            # Create tick_data hypertable optimized for time-series data
            with self.engine.connect() as conn:
                # Create extension if it doesn't exist
                conn.execute(text("CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;"))
                
                # Create tick_data table
                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS tick_data (
                    timestamp TIMESTAMPTZ NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    price DECIMAL(10, 2) NOT NULL,
                    volume INTEGER NOT NULL
                );
                """))
                
                # Convert to hypertable
                conn.execute(text("""
                SELECT create_hypertable('tick_data', 'timestamp', 
                                        if_not_exists => TRUE,
                                        create_default_indexes => TRUE);
                """))
                
                # Create indexes
                conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_tick_data_index_name ON tick_data (index_name);
                """))
                
                # Create derived_features table
                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS derived_features (
                    timestamp TIMESTAMPTZ NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    feature_name VARCHAR(100) NOT NULL,
                    feature_value FLOAT NOT NULL
                );
                """))
                
                # Convert to hypertable
                conn.execute(text("""
                SELECT create_hypertable('derived_features', 'timestamp', 
                                        if_not_exists => TRUE,
                                        create_default_indexes => TRUE);
                """))
                
                # Create predictions table
                conn.execute(text("""
                CREATE TABLE IF NOT EXISTS predictions (
                    generated_at TIMESTAMPTZ NOT NULL,
                    target_date DATE NOT NULL,
                    index_name VARCHAR(50) NOT NULL,
                    prediction_type VARCHAR(20) NOT NULL,
                    direction VARCHAR(10) NOT NULL,
                    magnitude FLOAT NOT NULL,
                    confidence FLOAT NOT NULL,
                    key_levels JSONB,
                    actual_outcome JSONB
                );
                """))
                
                # Create index on target_date
                conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_predictions_target_date 
                ON predictions (target_date);
                """))
                
                conn.commit()
                
            logger.info("Database schema created successfully")
            return True
            
        except SQLAlchemyError as e:
            logger.error(f"Error creating database schema: {str(e)}")
            return False
    
    def optimize_database(self):
        """Run optimization routines for the database"""
        try:
            with self.engine.connect() as conn:
                # Set chunk time interval for better performance with our data pattern
                conn.execute(text("""
                SELECT set_chunk_time_interval('tick_data', INTERVAL '1 day');
                """))
                
                # Add compression for older data
                conn.execute(text("""
                ALTER TABLE tick_data SET (
                    timescaledb.compress,
                    timescaledb.compress_segmentby = 'index_name'
                );
                """))
                
                # Create compression policy (compress data older than 7 days)
                conn.execute(text("""
                SELECT add_compression_policy('tick_data', INTERVAL '7 days');
                """))
                
                conn.commit()
                
            logger.info("Database optimization completed")
            return True
            
        except SQLAlchemyError as e:
            logger.error(f"Error optimizing database: {str(e)}")
            return False