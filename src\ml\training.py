"""
Model training pipeline for tick data analysis
"""
import pandas as pd
import numpy as np
import torch
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path
import pickle
import json
from datetime import datetime, timedelta
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from config import get_config
from src.data.storage import TickDataStorage
from src.ml.features import FeatureEngineering
from src.ml.models.lstm import TickDataPredictor
from src.utils.logging import get_logger, get_performance_logger, get_error_logger

logger = get_logger(__name__)
perf_logger = get_performance_logger()
error_logger = get_error_logger()

class ModelTrainingPipeline:
    """Complete pipeline for training tick data prediction models"""
    
    def __init__(self, config=None, db_connection_string=None):
        """
        Initialize training pipeline

        Args:
            config: Configuration object
            db_connection_string: Override database connection string
        """
        self.config = config or get_config()

        # Use override connection string if provided
        connection_string = db_connection_string or self.config.database.connection_string

        self.storage = TickDataStorage(connection_string)
        self.feature_engineering = FeatureEngineering()
        
        # Training parameters
        self.sequence_length = self.config.ml.sequence_length
        self.batch_size = self.config.ml.batch_size
        self.epochs = self.config.ml.epochs
        
        # Model configurations for different prediction tasks
        self.model_configs = {
            'next_tick_price': {
                'input_dim': 20,  # Will be updated based on features
                'hidden_dim': self.config.ml.lstm_hidden_dim,
                'num_layers': self.config.ml.lstm_num_layers,
                'output_dim': 1,
                'dropout': self.config.ml.lstm_dropout,
                'learning_rate': self.config.ml.learning_rate,
                'weight_decay': self.config.ml.weight_decay,
                'task': 'regression'
            },
            'direction_prediction': {
                'input_dim': 20,
                'hidden_dim': self.config.ml.lstm_hidden_dim,
                'num_layers': self.config.ml.lstm_num_layers,
                'output_dim': 3,  # UP, DOWN, NEUTRAL
                'dropout': self.config.ml.lstm_dropout,
                'learning_rate': self.config.ml.learning_rate,
                'weight_decay': self.config.ml.weight_decay,
                'task': 'classification'
            }
        }
        
        self.trained_models = {}
    
    def prepare_training_data(self, index_name: str, start_date: str, end_date: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Prepare training data for a specific index
        
        Args:
            index_name: Name of the index
            start_date: Start date for training data
            end_date: End date for training data
            
        Returns:
            Tuple of (features_df, targets_df)
        """
        logger.info(f"Preparing training data for {index_name} from {start_date} to {end_date}")
        perf_logger.start_timer(f"prepare_data_{index_name}")
        
        try:
            # Get tick data
            tick_data = self.storage.get_tick_data(index_name, start_date, end_date)
            
            if len(tick_data) == 0:
                raise ValueError(f"No tick data found for {index_name} in date range {start_date} to {end_date}")
            
            logger.info(f"Retrieved {len(tick_data)} tick records for {index_name}")
            
            # Extract features
            features = self.feature_engineering.extract_all_features(tick_data)
            
            # Create targets
            targets = self._create_targets(tick_data)
            
            # Combine features with basic tick data
            combined_features = pd.concat([
                tick_data[['timestamp', 'price', 'volume']],
                features
            ], axis=1)
            
            # Remove rows with NaN values
            valid_indices = ~(combined_features.isnull().any(axis=1) | targets.isnull().any(axis=1))
            combined_features = combined_features[valid_indices]
            targets = targets[valid_indices]
            
            logger.info(f"Prepared {len(combined_features)} valid training samples")
            
            perf_logger.end_timer(
                f"prepare_data_{index_name}",
                samples_count=len(combined_features),
                features_count=len(combined_features.columns)
            )
            
            return combined_features, targets
            
        except Exception as e:
            error_logger.log_exception("prepare_training_data", e, index_name=index_name)
            raise
    
    def _create_targets(self, tick_data: pd.DataFrame) -> pd.DataFrame:
        """
        Create target variables for training
        
        Args:
            tick_data: DataFrame with tick data
            
        Returns:
            DataFrame with target variables
        """
        targets = pd.DataFrame(index=tick_data.index)
        
        # Next tick price (regression target)
        targets['next_price'] = tick_data['price'].shift(-1)
        
        # Price direction (classification target)
        price_change = tick_data['price'].diff().shift(-1)
        targets['direction'] = pd.cut(
            price_change,
            bins=[-np.inf, -0.01, 0.01, np.inf],
            labels=[0, 1, 2]  # DOWN, NEUTRAL, UP
        ).astype(float)
        
        # Price change magnitude
        targets['price_change_pct'] = (tick_data['price'].shift(-1) / tick_data['price'] - 1) * 100
        
        return targets
    
    def train_model(self, index_name: str, model_type: str, features: pd.DataFrame, targets: pd.DataFrame) -> Dict[str, Any]:
        """
        Train a specific model
        
        Args:
            index_name: Name of the index
            model_type: Type of model to train
            features: Features DataFrame
            targets: Targets DataFrame
            
        Returns:
            Training results dictionary
        """
        logger.info(f"Training {model_type} model for {index_name}")
        perf_logger.start_timer(f"train_model_{index_name}_{model_type}")
        
        try:
            # Get model configuration
            if model_type not in self.model_configs:
                raise ValueError(f"Unknown model type: {model_type}")
            
            config = self.model_configs[model_type].copy()
            
            # Prepare features for training
            feature_columns = [col for col in features.columns if col not in ['timestamp', 'price', 'volume']]
            X = features[feature_columns].values
            
            # Update input dimension
            config['input_dim'] = X.shape[1]
            
            # Prepare targets
            if model_type == 'next_tick_price':
                y = targets['next_price'].values
            elif model_type == 'direction_prediction':
                y = targets['direction'].values
            else:
                raise ValueError(f"No target mapping for model type: {model_type}")
            
            # Remove NaN values
            valid_mask = ~(np.isnan(X).any(axis=1) | np.isnan(y))
            X = X[valid_mask]
            y = y[valid_mask]
            
            logger.info(f"Training with {len(X)} samples and {X.shape[1]} features")
            
            # Create and train model
            predictor = TickDataPredictor(config)
            
            # Prepare sequences
            X_seq, y_seq = predictor.prepare_data(
                pd.DataFrame(X), 
                pd.Series(y), 
                self.sequence_length
            )
            
            # Train model
            history = predictor.train(X_seq, y_seq, self.epochs, self.batch_size)
            
            # Save model
            model_path = self.config.data.models_path / f"{index_name}_{model_type}.pth"
            predictor.save_model(str(model_path))
            
            # Store trained model
            model_key = f"{index_name}_{model_type}"
            self.trained_models[model_key] = {
                'predictor': predictor,
                'config': config,
                'feature_columns': feature_columns,
                'model_path': str(model_path),
                'training_history': history
            }
            
            # Calculate final metrics
            final_train_loss = history['loss'][-1] if history['loss'] else None
            final_val_loss = history['val_loss'][-1] if history['val_loss'] else None
            
            results = {
                'model_type': model_type,
                'index_name': index_name,
                'training_samples': len(X),
                'features_count': X.shape[1],
                'epochs_trained': len(history['loss']),
                'final_train_loss': final_train_loss,
                'final_val_loss': final_val_loss,
                'model_path': str(model_path),
                'feature_columns': feature_columns
            }
            
            perf_logger.end_timer(
                f"train_model_{index_name}_{model_type}",
                **results
            )
            
            logger.info(f"Successfully trained {model_type} for {index_name}")
            return results
            
        except Exception as e:
            error_logger.log_exception("train_model", e, index_name=index_name, model_type=model_type)
            raise
    
    def train_all_models(self, indices: List[str], start_date: str, end_date: str) -> Dict[str, Any]:
        """
        Train all models for all indices
        
        Args:
            indices: List of index names
            start_date: Start date for training
            end_date: End date for training
            
        Returns:
            Training results summary
        """
        logger.info(f"Starting training for {len(indices)} indices")
        perf_logger.start_timer("train_all_models")
        
        results = {
            'training_start': datetime.now().isoformat(),
            'indices': indices,
            'date_range': {'start': start_date, 'end': end_date},
            'model_results': {},
            'summary': {}
        }
        
        successful_trainings = 0
        failed_trainings = 0
        
        try:
            for index_name in indices:
                logger.info(f"Processing index: {index_name}")
                
                try:
                    # Prepare data
                    features, targets = self.prepare_training_data(index_name, start_date, end_date)
                    
                    # Train each model type
                    index_results = {}
                    for model_type in self.model_configs.keys():
                        try:
                            model_result = self.train_model(index_name, model_type, features, targets)
                            index_results[model_type] = model_result
                            successful_trainings += 1
                            
                        except Exception as e:
                            logger.error(f"Failed to train {model_type} for {index_name}: {e}")
                            index_results[model_type] = {'error': str(e)}
                            failed_trainings += 1
                    
                    results['model_results'][index_name] = index_results
                    
                except Exception as e:
                    logger.error(f"Failed to process index {index_name}: {e}")
                    results['model_results'][index_name] = {'error': str(e)}
                    failed_trainings += len(self.model_configs)
            
            # Create summary
            results['summary'] = {
                'total_models': len(indices) * len(self.model_configs),
                'successful_trainings': successful_trainings,
                'failed_trainings': failed_trainings,
                'success_rate': successful_trainings / (successful_trainings + failed_trainings) if (successful_trainings + failed_trainings) > 0 else 0
            }
            
            results['training_end'] = datetime.now().isoformat()
            
            # Save training results
            results_path = self.config.data.models_path / f"training_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(results_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            perf_logger.end_timer("train_all_models", **results['summary'])
            
            logger.info(f"Training completed: {successful_trainings} successful, {failed_trainings} failed")
            return results
            
        except Exception as e:
            error_logger.log_exception("train_all_models", e)
            raise
    
    def load_trained_model(self, index_name: str, model_type: str) -> Optional[Dict[str, Any]]:
        """
        Load a previously trained model
        
        Args:
            index_name: Name of the index
            model_type: Type of model
            
        Returns:
            Model dictionary or None if not found
        """
        model_key = f"{index_name}_{model_type}"
        
        if model_key in self.trained_models:
            return self.trained_models[model_key]
        
        # Try to load from disk
        model_path = self.config.data.models_path / f"{index_name}_{model_type}.pth"
        
        if model_path.exists():
            try:
                config = self.model_configs[model_type].copy()
                predictor = TickDataPredictor(config)
                predictor.load_model(str(model_path))
                
                # Load metadata if available
                metadata_path = model_path.with_suffix('.json')
                metadata = {}
                if metadata_path.exists():
                    with open(metadata_path, 'r') as f:
                        metadata = json.load(f)
                
                model_dict = {
                    'predictor': predictor,
                    'config': config,
                    'model_path': str(model_path),
                    **metadata
                }
                
                self.trained_models[model_key] = model_dict
                return model_dict
                
            except Exception as e:
                logger.error(f"Failed to load model {model_key}: {e}")
                return None
        
        return None
