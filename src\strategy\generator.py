import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class StrategyGenerator:
    """Generate trading strategies based on predictions and analysis"""
    
    def __init__(self, db_connection):
        """
        Initialize strategy generator
        
        Args:
            db_connection: Database connection
        """
        self.db = db_connection
        self.confidence_threshold = 0.7  # Minimum confidence for strategy generation
    
    def generate_next_day_strategy(self, index_name: str, prediction_date: str) -> Dict[str, Any]:
        """
        Generate strategy for the next trading day
        
        Args:
            index_name: Name of the index
            prediction_date: Date for which to generate strategy
            
        Returns:
            Dictionary with strategy details
        """
        # Get latest prediction for the specified date
        prediction = self._get_latest_prediction(index_name, prediction_date)
        
        if not prediction or prediction['confidence'] < self.confidence_threshold:
            return {
                'index': index_name,
                'date': prediction_date,
                'strategy': 'NEUTRAL',
                'confidence': prediction.get('confidence', 0) if prediction else 0,
                'reason': 'Insufficient confidence in prediction'
            }
        
        # Generate strategy based on prediction
        strategy = {
            'index': index_name,
            'date': prediction_date,
            'prediction': prediction,
            'confidence': prediction['confidence'],
            'key_levels': prediction['key_levels']
        }
        
        # Determine strategy type
        if prediction['direction'] == 'UP' and prediction['magnitude'] > 0.5:
            strategy['strategy'] = 'STRONG_BUY'
            strategy['entry_points'] = self._calculate_entry_points(index_name, prediction, 'BUY')
            strategy['stop_loss'] = strategy['entry_points']['primary'] * 0.995  # 0.5% below entry
            strategy['targets'] = [
                strategy['entry_points']['primary'] * 1.005,  # Target 1: 0.5% above entry
                strategy['entry_points']['primary'] * 1.01    # Target 2: 1% above entry
            ]
        
        elif prediction['direction'] == 'UP':
            strategy['strategy'] = 'BUY'
            strategy['entry_points'] = self._calculate_entry_points(index_name, prediction, 'BUY')
            strategy['stop_loss'] = strategy['entry_points']['primary'] * 0.997  # 0.3% below entry
            strategy['targets'] = [
                strategy['entry_points']['primary'] * 1.003,  # Target 1: 0.3% above entry
                strategy['entry_points']['primary'] * 1.006   # Target 2: 0.6% above entry
            ]
        
        elif prediction['direction'] == 'DOWN' and prediction['magnitude'] > 0.5:
            strategy['strategy'] = 'STRONG_SELL'
            strategy['entry_points'] = self._calculate_entry_points(index_name, prediction, 'SELL')
            strategy['stop_loss'] = strategy['entry_points']['primary'] * 1.005  # 0.5% above entry
            strategy['targets'] = [
                strategy['entry_points']['primary'] * 0.995,  # Target 1: 0.5% below entry
                strategy['entry_points']['primary'] * 0.99    # Target 2: 1% below entry
            ]
        
        elif prediction['direction'] == 'DOWN':
            strategy['strategy'] = 'SELL'
            strategy['entry_points'] = self._calculate_entry_points(index_name, prediction, 'SELL')
            strategy['stop_loss'] = strategy['entry_points']['primary'] * 1.003  # 0.3% above entry
            strategy['targets'] = [
                strategy['entry_points']['primary'] * 0.997,  # Target 1: 0.3% below entry
                strategy['entry_points']['primary'] * 0.994   # Target 2: 0.6% below entry
            ]
        
        else:
            strategy['strategy'] = 'NEUTRAL'
            strategy['reason'] = 'Unclear directional bias'
        
        # Add volume conditions
        strategy['volume_conditions'] = self._generate_volume_conditions(index_name)
        
        # Add time-based filters
        strategy['time_filters'] = {
            'avoid_first_15min': True,
            'preferred_entry_window': '10:30-14:00',
            'close_positions_by': '15:15'
        }
        
        return strategy
    
    def generate_weekly_outlook(self, index_name: str, start_date: str) -> Dict[str, Any]:
        """
        Generate weekly market outlook
        
        Args:
            index_name: Name of the index
            start_date: Start date for the week
            
        Returns:
            Dictionary with weekly outlook
        """
        # Convert start_date to datetime
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        
        # Generate dates for the week
        dates = [(start_dt + timedelta(days=i)).strftime('%Y-%m-%d') for i in range(5)]  # Monday to Friday
        
        # Get predictions for each day
        daily_predictions = {}
        for date in dates:
            prediction = self._get_latest_prediction(index_name, date)
            if prediction:
                daily_predictions[date] = prediction
        
        # If we don't have enough predictions, return limited outlook
        if len(daily_predictions) < 3:
            return {
                'index': index_name,
                'week_starting': start_date,
                'outlook': 'INSUFFICIENT_DATA',
                'confidence': 0,
                'reason': 'Not enough daily predictions available'
            }
        
        # Analyze weekly trend
        up_days = sum(1 for p in daily_predictions.values() if p['direction'] == 'UP')
        down_days = sum(1 for p in daily_predictions.values() if p['direction'] == 'DOWN')
        
        # Calculate average confidence and magnitude
        avg_confidence = sum(p['confidence'] for p in daily_predictions.values()) / len(daily_predictions)
        avg_magnitude = sum(p['magnitude'] for p in daily_predictions.values()) / len(daily_predictions)
        
        # Determine weekly outlook
        if up_days > down_days and avg_confidence > 0.6:
            outlook = 'BULLISH'
            bias = 'UP'
        elif down_days > up_days and avg_confidence > 0.6:
            outlook = 'BEARISH'
            bias = 'DOWN'
        else:
            outlook = 'NEUTRAL'
            bias = 'SIDEWAYS'
        
        # Generate key levels for the week
        key_levels = self._generate_weekly_key_levels(index_name, daily_predictions)
        
        # Create weekly outlook
        weekly_outlook = {
            'index': index_name,
            'week_starting': start_date,
            'outlook': outlook,
            'bias': bias,
            'confidence': avg_confidence,
            'magnitude': avg_magnitude,
            'key_levels': key_levels,
            'daily_predictions': daily_predictions
        }
        
        # Add expiry effect if applicable
        expiry_effect = self._analyze_expiry_effect(index_name, dates)
        if expiry_effect:
            weekly_outlook['expiry_effect'] = expiry_effect
        
        return weekly_outlook
    
    def _get_latest_prediction(self, index_name: str, prediction_date: str) -> Dict[str, Any]:
        """
        Get the latest prediction for a specific date
        
        Args:
            index_name: Name of the index
            prediction_date: Date for prediction
            
        Returns:
            Dictionary with prediction details
        """
        # This would typically query the database for the latest prediction
        # For now, we'll return a mock prediction
        
        # In a real implementation, this would be:
        # query = f"""
        # SELECT * FROM predictions 
        # WHERE index_name = '{index_name}' AND target_date = '{prediction_date}'
        # ORDER BY generated_at DESC LIMIT 1
        # """
        # return pd.read_sql(query, self.db).to_dict('records')[0]
        
        # Mock prediction for demonstration
        import random
        
        directions = ['UP', 'DOWN']
        direction = random.choice(directions)
        
        return {
            'index_name': index_name,
            'target_date': prediction_date,
            'direction': direction,
            'magnitude': random.uniform(0.2, 0.8),
            'confidence': random.uniform(0.6, 0.9),
            'key_levels': {
                'support': [
                    random.uniform(18000, 19000),
                    random.uniform(17500, 18000)
                ],
                'resistance': [
                    random.uniform(19500, 20000),
                    random.uniform(20000, 20500)
                ]
            }
        }
    
    def _calculate_entry_points(self, index_name: str, prediction: Dict[str, Any], direction: str) -> Dict[str, float]:
        """
        Calculate entry points based on prediction
        
        Args:
            index_name: Name of the index
            prediction: Prediction dictionary
            direction: 'BUY' or 'SELL'
            
        Returns:
            Dictionary with entry points
        """
        # Get the last closing price
        last_price = self._get_last_price(index_name)
        
        # Calculate entry points
        if direction == 'BUY':
            primary_entry = last_price * 1.001  # Slightly above last price
            aggressive_entry = last_price * 0.998  # Slight pullback
            conservative_entry = prediction['key_levels']['support'][0]  # First support level
        else:  # SELL
            primary_entry = last_price * 0.999  # Slightly below last price
            aggressive_entry = last_price * 1.002  # Slight bounce
            conservative_entry = prediction['key_levels']['resistance'][0]  # First resistance level
        
        return {
            'primary': primary_entry,
            'aggressive': aggressive_entry,
            'conservative': conservative_entry
        }
    
    def _get_last_price(self, index_name: str) -> float:
        """
        Get the last price for an index
        
        Args:
            index_name: Name of the index
            
        Returns:
            Last price
        """
        # This would typically query the database for the last price
        # For now, we'll return a mock price
        
        # Mock prices for demonstration
        prices = {
            'nifty': 19500.0,
            'bank_nifty': 45000.0,
            'fin_nifty': 20000.0,
            'midcap_nifty': 8500.0,
            'nifty_next': 46000.0
        }
        
        return prices.get(index_name, 10000.0)
    
    def _generate_volume_conditions(self, index_name: str) -> Dict[str, Any]:
        """
        Generate volume conditions for the strategy
        
        Args:
            index_name: Name of the index
            
        Returns:
            Dictionary with volume conditions
        """
        # Calculate average volume
        avg_volume = self._calculate_average_volume(index_name)
        
        return {
            'min_tick_volume': avg_volume * 0.8,
            'confirmation_volume': avg_volume * 1.5,
            'breakout_volume': avg_volume * 2.0
        }
    
    def _calculate_average_volume(self, index_name: str) -> float:
        """
        Calculate average volume for an index
        
        Args:
            index_name: Name of the index
            
        Returns:
            Average volume
        """
        # This would typically query the database for average volume
        # For now, we'll return mock values
        
        # Mock average volumes for demonstration
        volumes = {
            'nifty': 1000.0,
            'bank_nifty': 800.0,
            'fin_nifty': 500.0,
            'midcap_nifty': 300.0,
            'nifty_next': 400.0
        }
        
        return volumes.get(index_name, 500.0)
    
    def _generate_weekly_key_levels(self, index_name: str, daily_predictions: Dict[str, Dict[str, Any]]) -> Dict[str, List[float]]:
        """
        Generate key levels for the week
        
        Args:
            index_name: Name of the index
            daily_predictions: Dictionary with daily predictions
            
        Returns:
            Dictionary with key levels
        """
        # Collect all support and resistance levels
        all_supports = []
        all_resistances = []
        
        for prediction in daily_predictions.values():
            all_supports.extend(prediction['key_levels']['support'])
            all_resistances.extend(prediction['key_levels']['resistance'])
        
        # Cluster similar levels
        supports = self._cluster_price_levels(all_supports)
        resistances = self._cluster_price_levels(all_resistances)
        
        return {
            'support': supports,
            'resistance': resistances
        }
    
    def _cluster_price_levels(self, levels: List[float], threshold: float = 0.002) -> List[float]:
        """
        Cluster similar price levels
        
        Args:
            levels: List of price levels
            threshold: Threshold for clustering (as percentage)
            
        Returns:
            List of clustered price levels
        """
        if not levels:
            return []
        
        # Sort levels
        sorted_levels = sorted(levels)
        
        # Cluster similar levels
        clusters = []
        current_cluster = [sorted_levels[0]]
        
        for level in sorted_levels[1:]:
            # If level is within threshold of the average of current cluster
            if abs(level - sum(current_cluster) / len(current_cluster)) / level < threshold:
                current_cluster.append(level)
            else:
                # Add average of current cluster to results
                clusters.append(sum(current_cluster) / len(current_cluster))
                # Start new cluster
                current_cluster = [level]
        
        # Add the last cluster
        if current_cluster:
            clusters.append(sum(current_cluster) / len(current_cluster))
        
        return clusters
    
    def _analyze_expiry_effect(self, index_name: str, dates: List[str]) -> Dict[str, Any]:
        """
        Analyze expiry effect for the week
        
        Args:
            index_name: Name of the index
            dates: List of dates in the week
            
        Returns:
            Dictionary with expiry effect details or None
        """
        # Determine if there's an expiry in this week
        # This is a simplified version - would need to be customized for actual expiry calendar
        
        # For demonstration, we'll assume Thursday is always expiry day
        for date in dates:
            dt = datetime.strptime(date, '%Y-%m-%d')
            if dt.weekday() == 3:  # Thursday
                # Check if it's a monthly or weekly expiry
                is_monthly = dt.day > 22  # Simplified check for monthly expiry
                
                return {
                    'expiry_date': date,
                    'expiry_type': 'MONTHLY' if is_monthly else 'WEEKLY',
                    'expected_volatility': 'HIGH' if is_monthly else 'MODERATE',
                    'pre_expiry_bias': 'Time decay acceleration',
                    'expiry_day_pattern': 'Higher volatility in last hour'
                }
        
        return None