import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any
import logging

logger = logging.getLogger(__name__)

class TickLSTM(nn.Module):
    """LSTM model for tick data sequence prediction"""
    
    def __init__(self, input_dim: int, hidden_dim: int, num_layers: int, output_dim: int, dropout: float = 0.2):
        """
        Initialize LSTM model
        
        Args:
            input_dim: Number of input features
            hidden_dim: Number of hidden units
            num_layers: Number of LSTM layers
            output_dim: Number of output dimensions
            dropout: Dropout probability
        """
        super(TickLSTM, self).__init__()
        
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # LSTM layers
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )
        
        # Attention mechanism
        self.attention = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.Tanh(),
            nn.Linear(hidden_dim, 1),
            nn.Softmax(dim=1)
        )
        
        # Output layers
        self.fc = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, output_dim)
        )
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass
        
        Args:
            x: Input tensor of shape (batch_size, seq_len, input_dim)
            
        Returns:
            Tuple of (output, attention_weights)
        """
        # LSTM forward pass
        lstm_out, _ = self.lstm(x)  # (batch_size, seq_len, hidden_dim)
        
        # Apply attention
        attention_weights = self.attention(lstm_out)  # (batch_size, seq_len, 1)
        context_vector = torch.sum(attention_weights * lstm_out, dim=1)  # (batch_size, hidden_dim)
        
        # Output layer
        output = self.fc(context_vector)  # (batch_size, output_dim)
        
        return output, attention_weights

class TickDataPredictor:
    """Wrapper class for tick data prediction using LSTM"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize predictor
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Initialize model
        self.model = TickLSTM(
            input_dim=config['input_dim'],
            hidden_dim=config['hidden_dim'],
            num_layers=config['num_layers'],
            output_dim=config['output_dim'],
            dropout=config['dropout']
        ).to(self.device)
        
        # Initialize optimizer
        self.optimizer = torch.optim.Adam(
            self.model.parameters(),
            lr=config['learning_rate'],
            weight_decay=config['weight_decay']
        )
        
        # Initialize scheduler
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=0.5,
            patience=5,
            verbose=True
        )
        
        # Initialize loss function
        if config['task'] == 'regression':
            self.criterion = nn.MSELoss()
        elif config['task'] == 'classification':
            self.criterion = nn.CrossEntropyLoss()
        else:
            raise ValueError(f"Unsupported task: {config['task']}")
    
    def prepare_data(self, features: pd.DataFrame, target: pd.Series, seq_len: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Prepare data for LSTM
        
        Args:
            features: DataFrame with features
            target: Series with target values
            seq_len: Sequence length
            
        Returns:
            Tuple of (X, y) tensors
        """
        X, y = [], []
        
        for i in range(len(features) - seq_len):
            X.append(features.iloc[i:i+seq_len].values)
            y.append(target.iloc[i+seq_len])
        
        X = torch.tensor(np.array(X), dtype=torch.float32).to(self.device)
        y = torch.tensor(np.array(y), dtype=torch.float32).to(self.device)
        
        return X, y
    
    def train(self, X: torch.Tensor, y: torch.Tensor, epochs: int, batch_size: int) -> Dict[str, List[float]]:
        """
        Train the model
        
        Args:
            X: Input tensor
            y: Target tensor
            epochs: Number of epochs
            batch_size: Batch size
            
        Returns:
            Dictionary with training history
        """
        n_samples = X.shape[0]
        history = {'loss': [], 'val_loss': []}
        
        # Split data into train and validation
        val_size = int(0.2 * n_samples)
        X_train, X_val = X[:-val_size], X[-val_size:]
        y_train, y_val = y[:-val_size], y[-val_size:]
        
        for epoch in range(epochs):
            self.model.train()
            epoch_loss = 0
            
            # Create batches
            indices = torch.randperm(X_train.shape[0])
            X_train = X_train[indices]
            y_train = y_train[indices]
            
            for i in range(0, X_train.shape[0], batch_size):
                # Get batch
                X_batch = X_train[i:i+batch_size]
                y_batch = y_train[i:i+batch_size]
                
                # Forward pass
                self.optimizer.zero_grad()
                y_pred, _ = self.model(X_batch)
                
                # Reshape if needed
                if self.config['task'] == 'regression':
                    y_pred = y_pred.squeeze()
                
                # Calculate loss
                loss = self.criterion(y_pred, y_batch)
                
                # Backward pass
                loss.backward()
                self.optimizer.step()
                
                epoch_loss += loss.item() * X_batch.shape[0]
            
            # Calculate average loss
            epoch_loss /= X_train.shape[0]
            history['loss'].append(epoch_loss)
            
            # Validation
            self.model.eval()
            with torch.no_grad():
                y_pred, _ = self.model(X_val)
                if self.config['task'] == 'regression':
                    y_pred = y_pred.squeeze()
                val_loss = self.criterion(y_pred, y_val).item()
                history['val_loss'].append(val_loss)
            
            # Update scheduler
            self.scheduler.step(val_loss)
            
            logger.info(f"Epoch {epoch+1}/{epochs}, Loss: {epoch_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        return history
    
    def predict(self, X: torch.Tensor) -> np.ndarray:
        """
        Make predictions
        
        Args:
            X: Input tensor
            
        Returns:
            Numpy array with predictions
        """
        self.model.eval()
        with torch.no_grad():
            y_pred, _ = self.model(X)
            return y_pred.cpu().numpy()
    
    def save_model(self, path: str):
        """
        Save model to file
        
        Args:
            path: Path to save model
        """
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': self.config
        }, path)
        logger.info(f"Model saved to {path}")
    
    def load_model(self, path: str):
        """
        Load model from file
        
        Args:
            path: Path to load model from
        """
        checkpoint = torch.load(path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        logger.info(f"Model loaded from {path}")