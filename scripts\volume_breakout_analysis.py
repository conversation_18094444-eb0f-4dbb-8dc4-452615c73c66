#!/usr/bin/env python3
"""
Analyze 5-minute candle volumes for breakout thresholds
"""
import pandas as pd
import numpy as np
from datetime import datetime
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.data.storage import TickDataStorage
from src.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

def analyze_5min_volume_patterns(index_name: str, date: str, storage: TickDataStorage):
    """
    Analyze 5-minute volume patterns for breakout thresholds
    """
    logger.info(f"📊 Analyzing 5-minute volume patterns for {index_name} on {date}")
    
    # Get tick data
    tick_data = storage.get_tick_data(index_name, date, date)
    
    if len(tick_data) < 100:
        logger.error(f"Insufficient data: {len(tick_data)} ticks")
        return None
    
    logger.info(f"Loaded {len(tick_data):,} tick records")
    
    # Convert to 5-minute candles
    tick_data.set_index('timestamp', inplace=True)
    
    candles_5min = tick_data.resample('5T').agg({
        'price': ['first', 'max', 'min', 'last'],
        'volume': 'sum'
    }).dropna()
    
    # Flatten column names
    candles_5min.columns = ['open', 'high', 'low', 'close', 'volume']
    
    logger.info(f"Created {len(candles_5min)} 5-minute candles")
    
    # Volume statistics
    avg_volume = candles_5min['volume'].mean()
    median_volume = candles_5min['volume'].median()
    max_volume = candles_5min['volume'].max()
    
    # Volume percentiles for breakout thresholds
    vol_50th = candles_5min['volume'].quantile(0.50)
    vol_75th = candles_5min['volume'].quantile(0.75)
    vol_90th = candles_5min['volume'].quantile(0.90)
    vol_95th = candles_5min['volume'].quantile(0.95)
    
    logger.info(f"📈 5-MINUTE VOLUME STATISTICS:")
    logger.info(f"  Average volume: {avg_volume:,.0f}")
    logger.info(f"  Median volume: {median_volume:,.0f}")
    logger.info(f"  Maximum volume: {max_volume:,.0f}")
    
    logger.info(f"🎯 VOLUME PERCENTILES:")
    logger.info(f"  50th percentile: {vol_50th:,.0f}")
    logger.info(f"  75th percentile: {vol_75th:,.0f}")
    logger.info(f"  90th percentile: {vol_90th:,.0f}")
    logger.info(f"  95th percentile: {vol_95th:,.0f}")
    
    # Identify key price levels
    current_price = tick_data['price'].iloc[-1]
    day_high = tick_data['price'].max()
    day_low = tick_data['price'].min()
    day_open = tick_data['price'].iloc[0]
    
    # Find resistance levels (swing highs with volume)
    resistance_levels = []
    for i in range(2, len(candles_5min)-2):
        if (candles_5min['high'].iloc[i] >= candles_5min['high'].iloc[i-2:i+3].max() and
            candles_5min['high'].iloc[i] > current_price):
            resistance_levels.append({
                'price': candles_5min['high'].iloc[i],
                'volume': candles_5min['volume'].iloc[i],
                'time': candles_5min.index[i],
                'volume_ratio': candles_5min['volume'].iloc[i] / avg_volume
            })
    
    # Sort by proximity to current price
    resistance_levels = sorted(resistance_levels, key=lambda x: x['price'])[:3]
    
    # Find support levels (swing lows with volume)
    support_levels = []
    for i in range(2, len(candles_5min)-2):
        if (candles_5min['low'].iloc[i] <= candles_5min['low'].iloc[i-2:i+3].min() and
            candles_5min['low'].iloc[i] < current_price):
            support_levels.append({
                'price': candles_5min['low'].iloc[i],
                'volume': candles_5min['volume'].iloc[i],
                'time': candles_5min.index[i],
                'volume_ratio': candles_5min['volume'].iloc[i] / avg_volume
            })
    
    # Sort by proximity to current price
    support_levels = sorted(support_levels, key=lambda x: x['price'], reverse=True)[:3]
    
    logger.info(f"📊 KEY PRICE LEVELS:")
    logger.info(f"  Current price: ₹{current_price:,.2f}")
    logger.info(f"  Day open: ₹{day_open:,.2f}")
    logger.info(f"  Day high: ₹{day_high:,.2f}")
    logger.info(f"  Day low: ₹{day_low:,.2f}")
    logger.info(f"  Day range: {((day_high - day_low) / day_open * 100):.2f}%")
    
    if resistance_levels:
        logger.info(f"🔴 RESISTANCE LEVELS:")
        for i, level in enumerate(resistance_levels, 1):
            logger.info(f"  {i}. ₹{level['price']:,.2f} (Vol: {level['volume']:,}, "
                       f"Ratio: {level['volume_ratio']:.1f}x) at {level['time'].strftime('%H:%M')}")
    
    if support_levels:
        logger.info(f"🟢 SUPPORT LEVELS:")
        for i, level in enumerate(support_levels, 1):
            logger.info(f"  {i}. ₹{level['price']:,.2f} (Vol: {level['volume']:,}, "
                       f"Ratio: {level['volume_ratio']:.1f}x) at {level['time'].strftime('%H:%M')}")
    
    # Calculate breakout volume requirements
    logger.info(f"🚀 BREAKOUT VOLUME REQUIREMENTS:")
    
    # For upside breakout
    next_resistance = resistance_levels[0]['price'] if resistance_levels else day_high
    logger.info(f"For UPSIDE breakout above ₹{next_resistance:,.2f}:")
    logger.info(f"  Normal breakout: {vol_75th:,.0f} volume (75th percentile)")
    logger.info(f"  Strong breakout: {vol_90th:,.0f} volume (90th percentile)")
    logger.info(f"  Explosive breakout: {vol_95th:,.0f} volume (95th percentile)")
    
    # For downside breakdown
    next_support = support_levels[0]['price'] if support_levels else day_low
    logger.info(f"For DOWNSIDE breakdown below ₹{next_support:,.2f}:")
    logger.info(f"  Normal breakdown: {vol_75th:,.0f} volume (75th percentile)")
    logger.info(f"  Strong breakdown: {vol_90th:,.0f} volume (90th percentile)")
    logger.info(f"  Panic selling: {vol_95th:,.0f} volume (95th percentile)")
    
    # Generate professional trading recommendations
    logger.info(f"📋 PROFESSIONAL TRADING SETUP:")
    
    # Upside setup
    upside_entry = next_resistance + (next_resistance * 0.0005)  # 0.05% above resistance
    upside_target = next_resistance + (next_resistance * 0.005)   # 0.5% target
    upside_stop = current_price - (current_price * 0.003)        # 0.3% stop
    
    logger.info(f"LONG SETUP (Upside Breakout):")
    logger.info(f"  Entry: Above ₹{upside_entry:,.2f} with {vol_75th:,.0f}+ volume")
    logger.info(f"  Target: ₹{upside_target:,.2f} (+{((upside_target - upside_entry) / upside_entry * 100):.2f}%)")
    logger.info(f"  Stop Loss: ₹{upside_stop:,.2f} (-{((upside_entry - upside_stop) / upside_entry * 100):.2f}%)")
    logger.info(f"  Risk/Reward: 1:{((upside_target - upside_entry) / (upside_entry - upside_stop)):.1f}")
    
    # Downside setup
    downside_entry = next_support - (next_support * 0.0005)  # 0.05% below support
    downside_target = next_support - (next_support * 0.005)   # 0.5% target
    downside_stop = current_price + (current_price * 0.003)   # 0.3% stop
    
    logger.info(f"SHORT SETUP (Downside Breakdown):")
    logger.info(f"  Entry: Below ₹{downside_entry:,.2f} with {vol_75th:,.0f}+ volume")
    logger.info(f"  Target: ₹{downside_target:,.2f} (-{((downside_entry - downside_target) / downside_entry * 100):.2f}%)")
    logger.info(f"  Stop Loss: ₹{downside_stop:,.2f} (+{((downside_stop - downside_entry) / downside_entry * 100):.2f}%)")
    logger.info(f"  Risk/Reward: 1:{((downside_entry - downside_target) / (downside_stop - downside_entry)):.1f}")
    
    # Volume confirmation levels
    logger.info(f"📊 VOLUME CONFIRMATION LEVELS:")
    logger.info(f"  Weak signal: {vol_50th:,.0f} - {vol_75th:,.0f} volume")
    logger.info(f"  Good signal: {vol_75th:,.0f} - {vol_90th:,.0f} volume")
    logger.info(f"  Strong signal: {vol_90th:,.0f} - {vol_95th:,.0f} volume")
    logger.info(f"  Explosive signal: {vol_95th:,.0f}+ volume")
    
    return {
        'volume_stats': {
            'average': avg_volume,
            'median': median_volume,
            'percentiles': {
                '50th': vol_50th,
                '75th': vol_75th,
                '90th': vol_90th,
                '95th': vol_95th
            }
        },
        'price_levels': {
            'current': current_price,
            'resistance': resistance_levels,
            'support': support_levels,
            'day_high': day_high,
            'day_low': day_low
        },
        'trading_setups': {
            'long': {
                'entry': upside_entry,
                'target': upside_target,
                'stop': upside_stop,
                'volume_required': vol_75th
            },
            'short': {
                'entry': downside_entry,
                'target': downside_target,
                'stop': downside_stop,
                'volume_required': vol_75th
            }
        }
    }

def main():
    """Main analysis function"""
    logger.info("🎯 5-Minute Volume Breakout Analysis")
    
    # Use the corrected database
    db_path = "clean_tickdata.db"
    connection_string = f"sqlite:///{db_path}"
    
    if not Path(db_path).exists():
        logger.error(f"Database {db_path} not found")
        return False
    
    try:
        storage = TickDataStorage(connection_string)
        
        # Analyze all indices for July 14, 2025
        analysis_date = "2025-07-14"
        indices = ['nifty', 'bank_nifty', 'fin_nifty', 'midcap_nifty', 'nifty_next']
        
        all_analyses = {}
        
        for index_name in indices:
            logger.info(f"\n{'='*60}")
            logger.info(f"📈 ANALYZING {index_name.upper()}")
            logger.info(f"{'='*60}")
            
            result = analyze_5min_volume_patterns(index_name, analysis_date, storage)
            
            if result:
                all_analyses[index_name] = result
            else:
                logger.error(f"Failed to analyze {index_name}")
        
        # Summary of all indices
        logger.info(f"\n{'='*60}")
        logger.info(f"📋 SUMMARY - NEXT DAY TRADING SETUPS (JULY 15, 2025)")
        logger.info(f"{'='*60}")
        
        for index_name, analysis in all_analyses.items():
            current_price = analysis['price_levels']['current']
            long_setup = analysis['trading_setups']['long']
            short_setup = analysis['trading_setups']['short']
            vol_75th = analysis['volume_stats']['percentiles']['75th']
            
            logger.info(f"\n{index_name.upper()}:")
            logger.info(f"  Current: ₹{current_price:,.2f}")
            logger.info(f"  LONG above ₹{long_setup['entry']:,.2f} (Vol: {vol_75th:,.0f}+) → Target: ₹{long_setup['target']:,.2f}")
            logger.info(f"  SHORT below ₹{short_setup['entry']:,.2f} (Vol: {vol_75th:,.0f}+) → Target: ₹{short_setup['target']:,.2f}")
        
        logger.info(f"\n🎉 Volume Breakout Analysis Complete!")
        
        return True
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
