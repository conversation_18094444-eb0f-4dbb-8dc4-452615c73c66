#!/usr/bin/env python3
"""
Unified Platform Startup Script
Starts all interfaces: CLI, Web Dashboard, and API Server
"""
import os
import sys
import time
import signal
import threading
import subprocess
from pathlib import Path
from datetime import datetime
import click

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from src.web.dashboard import TradingDashboard
from src.api.endpoints import create_api_app
from src.utils.logging import setup_logging, get_logger
from config import get_config

# Setup logging
setup_logging()
logger = get_logger(__name__)

class PlatformManager:
    """Manages all platform services"""
    
    def __init__(self):
        self.config = get_config()
        self.services = {}
        self.running = False
        
        # Service configurations
        self.service_configs = {
            'dashboard': {
                'host': '127.0.0.1',
                'port': 5000,
                'name': 'Web Dashboard'
            },
            'api': {
                'host': '0.0.0.0',
                'port': 8000,
                'name': 'REST API Server'
            }
        }
    
    def start_dashboard(self):
        """Start the web dashboard"""
        try:
            dashboard = TradingDashboard(self.config)
            config = self.service_configs['dashboard']
            
            logger.info(f"Starting {config['name']} on {config['host']}:{config['port']}")
            dashboard.run(host=config['host'], port=config['port'], debug=False)
            
        except Exception as e:
            logger.error(f"Dashboard startup error: {e}")
    
    def start_api(self):
        """Start the API server"""
        try:
            app = create_api_app(self.config)
            config = self.service_configs['api']
            
            logger.info(f"Starting {config['name']} on {config['host']}:{config['port']}")
            app.run(host=config['host'], port=config['port'], debug=False)
            
        except Exception as e:
            logger.error(f"API server startup error: {e}")
    
    def start_all_services(self):
        """Start all services in separate threads"""
        self.running = True
        
        # Start dashboard in thread
        dashboard_thread = threading.Thread(
            target=self.start_dashboard,
            name='Dashboard',
            daemon=True
        )
        dashboard_thread.start()
        self.services['dashboard'] = dashboard_thread
        
        # Start API server in thread
        api_thread = threading.Thread(
            target=self.start_api,
            name='API',
            daemon=True
        )
        api_thread.start()
        self.services['api'] = api_thread
        
        # Wait a moment for services to start
        time.sleep(2)
        
        # Display startup information
        self.display_startup_info()
        
        # Keep main thread alive
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.shutdown()
    
    def display_startup_info(self):
        """Display startup information and URLs"""
        print("\n" + "="*80)
        print("🚀 INDIAN MARKET TICK DATA ANALYSIS PLATFORM")
        print("="*80)
        print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🗄️  Database: {self.config.database.connection_string}")
        print()
        
        print("🌐 AVAILABLE INTERFACES:")
        print("-"*40)
        
        # Web Dashboard
        dashboard_config = self.service_configs['dashboard']
        dashboard_url = f"http://{dashboard_config['host']}:{dashboard_config['port']}"
        print(f"📊 Web Dashboard: {dashboard_url}")
        print(f"   - Real-time monitoring and analysis")
        print(f"   - Data import management")
        print(f"   - Trading signals visualization")
        print()
        
        # API Server
        api_config = self.service_configs['api']
        api_url = f"http://{api_config['host']}:{api_config['port']}"
        print(f"🔌 REST API Server: {api_url}")
        print(f"   - API Documentation: {api_url}/docs/")
        print(f"   - Health Check: {api_url}/api/system/health")
        print(f"   - System Status: {api_url}/api/system/status")
        print()
        
        # CLI Commands
        print("💻 CLI COMMANDS:")
        print("-"*40)
        print("   python cli.py --help                    # Show all commands")
        print("   python cli.py db init                   # Initialize database")
        print("   python cli.py data daily-import         # Import today's data")
        print("   python cli.py data bulk-import          # Bulk import all data")
        print("   python cli.py trading analyze           # Run trading analysis")
        print("   python cli.py trading signals           # Generate trading signals")
        print("   python cli.py system status             # Show system status")
        print()
        
        print("📈 QUICK START EXAMPLES:")
        print("-"*40)
        print("1. Import sample data:")
        print("   python cli.py data bulk-import --clean")
        print()
        print("2. Generate trading signals:")
        print("   python cli.py trading signals --confidence 0.7")
        print()
        print("3. Run professional analysis:")
        print("   python cli.py trading analyze --output analysis.json")
        print()
        
        print("🛑 To stop the platform: Press Ctrl+C")
        print("="*80)
        print()
    
    def shutdown(self):
        """Shutdown all services"""
        logger.info("Shutting down platform...")
        self.running = False
        
        # Services will stop automatically as they are daemon threads
        print("\n🛑 Platform shutdown complete")

@click.group()
def cli():
    """Platform management commands"""
    pass

@cli.command()
@click.option('--dashboard-only', is_flag=True, help='Start only the web dashboard')
@click.option('--api-only', is_flag=True, help='Start only the API server')
@click.option('--dashboard-port', default=5000, help='Dashboard port (default: 5000)')
@click.option('--api-port', default=8000, help='API server port (default: 8000)')
def start(dashboard_only, api_only, dashboard_port, api_port):
    """Start the platform services"""
    manager = PlatformManager()
    
    # Update ports if specified
    manager.service_configs['dashboard']['port'] = dashboard_port
    manager.service_configs['api']['port'] = api_port
    
    if dashboard_only:
        print("🚀 Starting Web Dashboard only...")
        manager.start_dashboard()
    elif api_only:
        print("🚀 Starting API Server only...")
        manager.start_api()
    else:
        print("🚀 Starting all platform services...")
        manager.start_all_services()

@cli.command()
def status():
    """Check platform status"""
    import requests
    
    services = [
        ('Web Dashboard', 'http://127.0.0.1:5000'),
        ('API Server', 'http://0.0.0.0:8000/api/system/health')
    ]
    
    print("🔍 Platform Status Check:")
    print("-" * 40)
    
    for name, url in services:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: Running")
            else:
                print(f"⚠️  {name}: Responding but with status {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {name}: Not running")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")

@cli.command()
def demo():
    """Run a quick demonstration of platform features"""
    print("🎯 Platform Demonstration")
    print("=" * 50)
    
    try:
        # Check if data exists
        from src.data.storage import TickDataStorage
        from config import get_config
        
        config = get_config()
        storage = TickDataStorage(config.database.connection_string)
        stats = storage.get_database_stats()
        
        print(f"📊 Database Status:")
        print(f"   Total Records: {stats.get('total_records', 0):,}")
        print(f"   Date Range: {stats.get('date_range', 'N/A')}")
        print(f"   Indices: {stats.get('indices_count', 0)}")
        print()
        
        if stats.get('total_records', 0) == 0:
            print("⚠️  No data found. Run data import first:")
            print("   python cli.py data bulk-import --clean")
            return
        
        # Run quick analysis
        print("🔍 Running Quick Analysis...")
        from src.strategy.professional_trader import ProfessionalTradingStrategy
        
        strategy = ProfessionalTradingStrategy(storage)
        
        # Get latest date
        latest_data = storage.get_latest_data()
        if not latest_data.empty:
            date = latest_data['timestamp'].dt.date.iloc[0].strftime('%Y-%m-%d')
            
            print(f"📅 Analysis Date: {date}")
            
            # Analyze Nifty
            analysis = strategy.analyze_market_structure('nifty', date)
            if analysis:
                print("📈 Nifty Analysis:")
                if 'price_levels' in analysis:
                    levels = analysis['price_levels']
                    print(f"   Current Price: ₹{levels.get('current_price', 'N/A')}")
                    print(f"   Support: ₹{levels.get('support', 'N/A')}")
                    print(f"   Resistance: ₹{levels.get('resistance', 'N/A')}")
                
                if 'volume_analysis' in analysis:
                    vol = analysis['volume_analysis']
                    print(f"   Volume Threshold (75th): {vol.get('percentile_75', 'N/A'):,}")
                
                # Generate signals
                signals = strategy.generate_trading_signals(analysis)
                if signals:
                    print(f"🎯 Trading Signals: {len(signals)} found")
                    for signal in signals[:2]:  # Show first 2 signals
                        direction = signal.get('direction', 'Unknown')
                        entry = signal.get('entry_price', 'N/A')
                        confidence = signal.get('confidence', 0)
                        print(f"   {direction.upper()}: Entry ₹{entry}, Confidence {confidence:.1%}")
        
        print("\n✅ Demo completed successfully!")
        print("🌐 Access the web dashboard at: http://127.0.0.1:5000")
        print("🔌 API documentation at: http://0.0.0.0:8000/docs/")
        
    except Exception as e:
        print(f"❌ Demo error: {e}")

@cli.command()
def install():
    """Install required dependencies"""
    print("📦 Installing platform dependencies...")
    
    requirements = [
        'flask',
        'flask-socketio',
        'flask-restx',
        'flask-cors',
        'requests',
        'psutil'
    ]
    
    for package in requirements:
        print(f"Installing {package}...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} installed")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")

if __name__ == '__main__':
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        print("\n🛑 Shutting down platform...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    cli()
