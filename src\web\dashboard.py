#!/usr/bin/env python3
"""
Web Dashboard Interface for the Tick Data Analysis Platform
Real-time monitoring, analysis, and trading signal management
"""
from flask import Flask, render_template, jsonify, request, send_from_directory
from flask_socketio import Socket<PERSON>, emit
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
import threading
import time
from typing import Dict, List, Any

# Add project root to path
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.data.storage import TickDataStorage
from src.data.import_manager import DataImportManager
from src.strategy.professional_trader import ProfessionalTradingStrategy
from src.utils.logging import get_logger
from config import get_config

logger = get_logger(__name__)

class TradingDashboard:
    """Web dashboard for the trading platform"""
    
    def __init__(self, config=None):
        """Initialize the dashboard"""
        self.config = config or get_config()
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        self.app.config['SECRET_KEY'] = 'trading-dashboard-secret-key'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # Initialize components
        self.storage = TickDataStorage(self.config.database.connection_string)
        self.import_manager = DataImportManager(self.storage, self.config)
        self.strategy = ProfessionalTradingStrategy(self.storage)
        
        # Background monitoring
        self.monitoring_active = False
        self.monitoring_thread = None
        
        # Setup routes
        self._setup_routes()
        self._setup_socketio_events()
    
    def _setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            """Main dashboard page"""
            return render_template('dashboard.html')
        
        @self.app.route('/api/status')
        def api_status():
            """Get system status"""
            try:
                stats = self.storage.get_database_stats()
                quality_stats = self.storage.get_data_quality_stats()
                
                return jsonify({
                    'status': 'ok',
                    'database': {
                        'total_records': stats.get('total_records', 0),
                        'date_range': stats.get('date_range', 'N/A'),
                        'indices_count': stats.get('indices_count', 0)
                    },
                    'data_quality': {
                        'records_per_index': quality_stats.get('records_per_index', {}),
                        'latest_timestamp': quality_stats.get('latest_timestamp', 'N/A'),
                        'data_gaps': quality_stats.get('data_gaps', 0)
                    },
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                return jsonify({'status': 'error', 'message': str(e)}), 500
        
        @self.app.route('/api/import/jobs')
        def api_import_jobs():
            """Get import jobs"""
            try:
                status_filter = request.args.get('status')
                limit = int(request.args.get('limit', 20))
                
                jobs = self.import_manager.list_jobs(status_filter, limit)
                
                job_data = []
                for job in jobs:
                    job_data.append({
                        'job_id': job.job_id,
                        'job_type': job.job_type,
                        'status': job.status,
                        'created_at': job.created_at.isoformat(),
                        'started_at': job.started_at.isoformat() if job.started_at else None,
                        'completed_at': job.completed_at.isoformat() if job.completed_at else None,
                        'files_total': job.files_total,
                        'files_processed': job.files_processed,
                        'files_successful': job.files_successful,
                        'files_failed': job.files_failed,
                        'error_message': job.error_message,
                        'progress': (job.files_processed / job.files_total * 100) if job.files_total > 0 else 0
                    })
                
                return jsonify({'jobs': job_data})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/import/daily', methods=['POST'])
        def api_daily_import():
            """Create daily import job"""
            try:
                data = request.get_json()
                date = data.get('date', datetime.now().strftime('%Y-%m-%d'))
                source_dir = Path(data.get('source_dir', 'sampledata'))
                indices = data.get('indices')  # Optional list
                
                job_id = self.import_manager.create_daily_import_job(
                    date=date,
                    source_dir=source_dir,
                    indices=indices
                )
                
                # Execute job in background
                def execute_job():
                    self.import_manager.execute_job(job_id)
                    # Emit completion event
                    self.socketio.emit('job_completed', {'job_id': job_id})
                
                threading.Thread(target=execute_job, daemon=True).start()
                
                return jsonify({
                    'job_id': job_id,
                    'status': 'created',
                    'message': f'Daily import job created for {date}'
                })
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/import/bulk', methods=['POST'])
        def api_bulk_import():
            """Create bulk import job"""
            try:
                data = request.get_json()
                source_dir = Path(data.get('source_dir', 'sampledata'))
                date_range = data.get('date_range')  # Optional tuple
                indices = data.get('indices')  # Optional list
                clean_database = data.get('clean_database', False)
                
                job_id = self.import_manager.create_bulk_import_job(
                    source_dir=source_dir,
                    date_range=date_range,
                    indices=indices,
                    clean_database=clean_database
                )
                
                # Execute job in background
                def execute_job():
                    self.import_manager.execute_job(job_id)
                    self.socketio.emit('job_completed', {'job_id': job_id})
                
                threading.Thread(target=execute_job, daemon=True).start()
                
                return jsonify({
                    'job_id': job_id,
                    'status': 'created',
                    'message': 'Bulk import job created'
                })
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/analysis/<index_name>')
        def api_analysis(index_name):
            """Get trading analysis for an index"""
            try:
                date = request.args.get('date')
                if not date:
                    # Get latest date
                    latest_data = self.storage.get_latest_data()
                    if latest_data.empty:
                        return jsonify({'error': 'No data available'}), 404
                    date = latest_data['timestamp'].dt.date.iloc[0].strftime('%Y-%m-%d')
                
                analysis = self.strategy.analyze_market_structure(index_name, date)
                
                if not analysis:
                    return jsonify({'error': f'No analysis data for {index_name} on {date}'}), 404
                
                return jsonify({
                    'index_name': index_name,
                    'date': date,
                    'analysis': analysis,
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/signals')
        def api_signals():
            """Get trading signals"""
            try:
                date = request.args.get('date')
                indices = request.args.get('indices', '').split(',') if request.args.get('indices') else None
                confidence = float(request.args.get('confidence', 0.7))
                
                if not date:
                    # Get latest date
                    latest_data = self.storage.get_latest_data()
                    if latest_data.empty:
                        return jsonify({'error': 'No data available'}), 404
                    date = latest_data['timestamp'].dt.date.iloc[0].strftime('%Y-%m-%d')
                
                if not indices:
                    indices = ['nifty', 'bank_nifty', 'fin_nifty', 'midcap_nifty', 'nifty_next']
                
                all_signals = {}
                
                for index_name in indices:
                    try:
                        analysis = self.strategy.analyze_market_structure(index_name, date)
                        if analysis:
                            signals = self.strategy.generate_trading_signals(
                                analysis, confidence_threshold=confidence
                            )
                            if signals:
                                all_signals[index_name] = signals
                    except Exception as e:
                        logger.error(f"Error generating signals for {index_name}: {e}")
                
                return jsonify({
                    'date': date,
                    'confidence_threshold': confidence,
                    'signals': all_signals,
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/data/latest')
        def api_latest_data():
            """Get latest data summary"""
            try:
                latest_data = self.storage.get_latest_data()
                
                if latest_data.empty:
                    return jsonify({'error': 'No data available'}), 404
                
                # Group by index and get summary
                summary = {}
                for index_name in latest_data['index_name'].unique():
                    index_data = latest_data[latest_data['index_name'] == index_name]
                    
                    summary[index_name] = {
                        'latest_price': float(index_data['price'].iloc[0]),
                        'latest_volume': int(index_data['volume'].iloc[0]),
                        'latest_timestamp': index_data['timestamp'].iloc[0].isoformat(),
                        'record_count': len(index_data)
                    }
                
                return jsonify({
                    'summary': summary,
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                return jsonify({'error': str(e)}), 500
    
    def _setup_socketio_events(self):
        """Setup SocketIO events for real-time updates"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection"""
            logger.info("Client connected to dashboard")
            emit('connected', {'message': 'Connected to trading dashboard'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection"""
            logger.info("Client disconnected from dashboard")
        
        @self.socketio.on('start_monitoring')
        def handle_start_monitoring():
            """Start real-time monitoring"""
            if not self.monitoring_active:
                self.monitoring_active = True
                self.monitoring_thread = threading.Thread(
                    target=self._monitoring_loop, daemon=True
                )
                self.monitoring_thread.start()
                emit('monitoring_started', {'status': 'Monitoring started'})
        
        @self.socketio.on('stop_monitoring')
        def handle_stop_monitoring():
            """Stop real-time monitoring"""
            self.monitoring_active = False
            emit('monitoring_stopped', {'status': 'Monitoring stopped'})
    
    def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.monitoring_active:
            try:
                # Get current status
                stats = self.storage.get_database_stats()
                
                # Emit status update
                self.socketio.emit('status_update', {
                    'total_records': stats.get('total_records', 0),
                    'timestamp': datetime.now().isoformat()
                })
                
                # Check for running jobs
                running_jobs = self.import_manager.list_jobs(status_filter='running')
                if running_jobs:
                    job_updates = []
                    for job in running_jobs:
                        progress = (job.files_processed / job.files_total * 100) if job.files_total > 0 else 0
                        job_updates.append({
                            'job_id': job.job_id,
                            'progress': progress,
                            'files_processed': job.files_processed,
                            'files_total': job.files_total
                        })
                    
                    self.socketio.emit('job_progress', {'jobs': job_updates})
                
                time.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(10)  # Wait longer on error
    
    def run(self, host='127.0.0.1', port=5000, debug=False):
        """Run the dashboard server"""
        logger.info(f"Starting trading dashboard on {host}:{port}")
        self.socketio.run(self.app, host=host, port=port, debug=debug)

def create_app(config=None):
    """Factory function to create Flask app"""
    dashboard = TradingDashboard(config)
    return dashboard.app, dashboard.socketio

if __name__ == '__main__':
    dashboard = TradingDashboard()
    dashboard.run(debug=True)
