"""
Pytest configuration and fixtures for the tick data analysis platform
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import shutil
from sqlalchemy import create_engine
from sqlalchemy.exc import SQLAlchemyError

# Add project root to path
import sys
sys.path.append(str(Path(__file__).parent.parent))

from config import Config
from src.data.storage import TickDataStorage

@pytest.fixture(scope="session")
def test_config():
    """Create test configuration"""
    # Use in-memory SQLite for testing
    config = Config()
    config.database.connection_string = "sqlite:///:memory:"
    config.data.data_root = Path(tempfile.mkdtemp())
    config.data.models_path = Path(tempfile.mkdtemp())
    config.data.logs_path = Path(tempfile.mkdtemp())
    return config

@pytest.fixture(scope="session")
def test_db_engine(test_config):
    """Create test database engine"""
    engine = create_engine(test_config.database.connection_string)
    yield engine
    engine.dispose()

@pytest.fixture(scope="function")
def test_storage(test_db_engine):
    """Create test storage instance with clean database"""
    # Create tables for each test
    storage = TickDataStorage(test_db_engine.url)
    
    # Create basic tables (skip TimescaleDB specific features for SQLite)
    with test_db_engine.connect() as conn:
        conn.execute("""
        CREATE TABLE IF NOT EXISTS tick_data (
            timestamp DATETIME NOT NULL,
            index_name VARCHAR(50) NOT NULL,
            price DECIMAL(10, 2) NOT NULL,
            volume INTEGER NOT NULL
        )
        """)
        
        conn.execute("""
        CREATE TABLE IF NOT EXISTS derived_features (
            timestamp DATETIME NOT NULL,
            index_name VARCHAR(50) NOT NULL,
            feature_name VARCHAR(100) NOT NULL,
            feature_value FLOAT NOT NULL
        )
        """)
        
        conn.execute("""
        CREATE TABLE IF NOT EXISTS predictions (
            generated_at DATETIME NOT NULL,
            target_date DATE NOT NULL,
            index_name VARCHAR(50) NOT NULL,
            prediction_type VARCHAR(20) NOT NULL,
            direction VARCHAR(10) NOT NULL,
            magnitude FLOAT NOT NULL,
            confidence FLOAT NOT NULL,
            key_levels TEXT,
            actual_outcome TEXT
        )
        """)
        
        conn.commit()
    
    yield storage
    
    # Clean up tables after each test
    with test_db_engine.connect() as conn:
        conn.execute("DROP TABLE IF EXISTS tick_data")
        conn.execute("DROP TABLE IF EXISTS derived_features")
        conn.execute("DROP TABLE IF EXISTS predictions")
        conn.commit()

@pytest.fixture
def sample_tick_data():
    """Generate sample tick data for testing"""
    # Create realistic tick data
    start_time = datetime(2025, 7, 15, 9, 15, 0)
    n_ticks = 1000
    
    timestamps = [start_time + timedelta(seconds=i*2) for i in range(n_ticks)]
    
    # Generate realistic price movement
    initial_price = 19500.0
    price_changes = np.random.normal(0, 0.1, n_ticks)
    prices = [initial_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change/100)
        prices.append(new_price)
    
    # Generate realistic volume
    volumes = np.random.lognormal(mean=6, sigma=0.5, size=n_ticks).astype(int)
    volumes = np.clip(volumes, 100, 5000)  # Realistic volume range
    
    return pd.DataFrame({
        'timestamp': timestamps,
        'index_name': 'test_nifty',
        'price': prices,
        'volume': volumes
    })

@pytest.fixture
def sample_multi_index_data():
    """Generate sample data for multiple indices"""
    indices = ['nifty', 'bank_nifty', 'fin_nifty']
    base_prices = {'nifty': 19500, 'bank_nifty': 45000, 'fin_nifty': 20000}
    
    all_data = []
    start_time = datetime(2025, 7, 15, 9, 15, 0)
    n_ticks = 500
    
    for index_name in indices:
        timestamps = [start_time + timedelta(seconds=i*2) for i in range(n_ticks)]
        
        # Generate correlated price movements
        base_price = base_prices[index_name]
        price_changes = np.random.normal(0, 0.1, n_ticks)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change/100)
            prices.append(new_price)
        
        volumes = np.random.lognormal(mean=6, sigma=0.5, size=n_ticks).astype(int)
        volumes = np.clip(volumes, 100, 5000)
        
        index_data = pd.DataFrame({
            'timestamp': timestamps,
            'index_name': index_name,
            'price': prices,
            'volume': volumes
        })
        
        all_data.append(index_data)
    
    return pd.concat(all_data, ignore_index=True)

@pytest.fixture
def sample_features_data():
    """Generate sample features data for testing"""
    n_samples = 100
    feature_names = [
        'volume_delta', 'relative_volume', 'price_momentum', 
        'volatility', 'tick_direction', 'support_distance'
    ]
    
    data = []
    base_time = datetime(2025, 7, 15, 9, 15, 0)
    
    for i in range(n_samples):
        timestamp = base_time + timedelta(seconds=i*10)
        
        for feature_name in feature_names:
            data.append({
                'timestamp': timestamp,
                'index_name': 'test_nifty',
                'feature_name': feature_name,
                'feature_value': np.random.normal(0, 1)
            })
    
    return pd.DataFrame(data)

@pytest.fixture
def sample_csv_file(tmp_path, sample_tick_data):
    """Create a sample CSV file for testing file ingestion"""
    csv_file = tmp_path / "Nifty Ticklist 15072025.csv"
    
    # Convert to the expected CSV format
    csv_data = sample_tick_data.copy()
    csv_data['Time'] = csv_data['timestamp'].dt.strftime('%d-%m-%Y %H:%M:%S')
    csv_data['Last Rate'] = csv_data['price']
    csv_data['Volume'] = csv_data['volume']
    
    csv_data[['Time', 'Last Rate', 'Volume']].to_csv(csv_file, index=False)
    
    return csv_file

@pytest.fixture
def mock_model_config():
    """Mock configuration for ML models"""
    return {
        'input_dim': 10,
        'hidden_dim': 64,
        'num_layers': 2,
        'output_dim': 1,
        'dropout': 0.2,
        'learning_rate': 0.001,
        'weight_decay': 1e-5,
        'task': 'regression'
    }

@pytest.fixture
def sample_prediction_data():
    """Generate sample prediction data"""
    return {
        'index_name': 'test_nifty',
        'target_date': '2025-07-16',
        'direction': 'UP',
        'magnitude': 0.6,
        'confidence': 0.8,
        'key_levels': {
            'support': [19400, 19300],
            'resistance': [19600, 19700]
        }
    }

@pytest.fixture(scope="session", autouse=True)
def cleanup_test_files():
    """Clean up test files after all tests"""
    yield
    
    # Clean up any temporary directories created during testing
    import tempfile
    temp_dir = Path(tempfile.gettempdir())
    for item in temp_dir.glob("tmp*"):
        if item.is_dir():
            try:
                shutil.rmtree(item)
            except:
                pass  # Ignore cleanup errors

# Utility functions for tests
def assert_dataframe_equal(df1, df2, check_dtype=False):
    """Assert that two DataFrames are equal with better error messages"""
    try:
        pd.testing.assert_frame_equal(df1, df2, check_dtype=check_dtype)
    except AssertionError as e:
        print(f"DataFrames are not equal:")
        print(f"DF1 shape: {df1.shape}, DF2 shape: {df2.shape}")
        print(f"DF1 columns: {list(df1.columns)}")
        print(f"DF2 columns: {list(df2.columns)}")
        raise e

def assert_series_equal(s1, s2, check_dtype=False):
    """Assert that two Series are equal with better error messages"""
    try:
        pd.testing.assert_series_equal(s1, s2, check_dtype=check_dtype)
    except AssertionError as e:
        print(f"Series are not equal:")
        print(f"S1 shape: {s1.shape}, S2 shape: {s2.shape}")
        print(f"S1 dtype: {s1.dtype}, S2 dtype: {s2.dtype}")
        raise e
