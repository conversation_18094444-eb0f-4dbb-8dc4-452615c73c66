#!/usr/bin/env python3
"""
Test suite for Interface Systems
Tests CLI commands, API endpoints, and web dashboard functionality
"""
import pytest
import tempfile
import shutil
import json
import subprocess
import sys
from pathlib import Path
from datetime import datetime
import pandas as pd
from unittest.mock import patch, MagicMock
import requests

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.data.storage import TickDataStorage
from src.data.import_manager import DataImportManager
from src.api.endpoints import create_api_app
from src.web.dashboard import TradingDashboard
from config import get_config

class TestCLIInterface:
    """Test suite for CLI interface"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def test_storage(self, temp_dir):
        """Create test database storage"""
        db_path = temp_dir / "test_cli.db"
        storage = TickDataStorage(f"sqlite:///{db_path}")
        storage.init_database()
        return storage
    
    @pytest.fixture
    def sample_data_file(self, temp_dir):
        """Create sample data file for CLI testing"""
        sample_data = pd.DataFrame({
            'Time': ['14-07-2025 09:15:00', '14-07-2025 09:15:02'],
            'Last Rate': [25170.0, 25171.5],
            'Volume': [100, 150]
        })
        
        file_path = temp_dir / "Nifty Ticklist 14072025.csv"
        sample_data.to_csv(file_path, index=False)
        return file_path
    
    def test_cli_help_command(self):
        """Test CLI help command"""
        result = subprocess.run([
            sys.executable, 'cli.py', '--help'
        ], capture_output=True, text=True, cwd=Path(__file__).parent.parent)
        
        assert result.returncode == 0
        assert 'Tick Data Analysis Platform CLI' in result.stdout
        assert 'db' in result.stdout  # Database commands
        assert 'data' in result.stdout  # Data commands
        assert 'trading' in result.stdout  # Trading commands
        assert 'system' in result.stdout  # System commands
    
    def test_cli_db_commands(self):
        """Test database CLI commands"""
        # Test db help
        result = subprocess.run([
            sys.executable, 'cli.py', 'db', '--help'
        ], capture_output=True, text=True, cwd=Path(__file__).parent.parent)
        
        assert result.returncode == 0
        assert 'init' in result.stdout
        assert 'check' in result.stdout
        assert 'stats' in result.stdout
    
    def test_cli_data_commands(self):
        """Test data management CLI commands"""
        # Test data help
        result = subprocess.run([
            sys.executable, 'cli.py', 'data', '--help'
        ], capture_output=True, text=True, cwd=Path(__file__).parent.parent)
        
        assert result.returncode == 0
        assert 'daily-import' in result.stdout
        assert 'bulk-import' in result.stdout
        assert 'job-status' in result.stdout
        assert 'export' in result.stdout
    
    def test_cli_trading_commands(self):
        """Test trading CLI commands"""
        # Test trading help
        result = subprocess.run([
            sys.executable, 'cli.py', 'trading', '--help'
        ], capture_output=True, text=True, cwd=Path(__file__).parent.parent)
        
        assert result.returncode == 0
        assert 'analyze' in result.stdout
        assert 'signals' in result.stdout
    
    def test_cli_system_commands(self):
        """Test system management CLI commands"""
        # Test system help
        result = subprocess.run([
            sys.executable, 'cli.py', 'system', '--help'
        ], capture_output=True, text=True, cwd=Path(__file__).parent.parent)
        
        assert result.returncode == 0
        assert 'status' in result.stdout
        assert 'cleanup' in result.stdout
        assert 'monitor' in result.stdout

class TestAPIEndpoints:
    """Test suite for REST API endpoints"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def test_storage(self, temp_dir):
        """Create test database storage"""
        db_path = temp_dir / "test_api.db"
        storage = TickDataStorage(f"sqlite:///{db_path}")
        storage.init_database()
        
        # Add sample data
        sample_data = pd.DataFrame({
            'timestamp': pd.date_range('2025-07-14 09:15:00', periods=100, freq='2S'),
            'index_name': 'nifty',
            'price': 25170 + pd.Series(range(100)) * 0.1,
            'volume': [100 + i for i in range(100)]
        })
        storage.store_tick_data(sample_data)
        
        return storage
    
    @pytest.fixture
    def api_app(self, test_storage):
        """Create API app for testing"""
        # Mock the storage creation in the API
        with patch('src.api.endpoints.TickDataStorage') as mock_storage:
            mock_storage.return_value = test_storage
            app = create_api_app()
            app.config['TESTING'] = True
            return app.test_client()
    
    def test_health_endpoint(self, api_app):
        """Test health check endpoint"""
        response = api_app.get('/api/system/health')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == 'healthy'
        assert 'timestamp' in data
        assert 'version' in data
    
    def test_system_status_endpoint(self, api_app):
        """Test system status endpoint"""
        response = api_app.get('/api/system/status')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == 'ok'
        assert 'database' in data
        assert 'data_quality' in data
        assert 'timestamp' in data
        
        # Check database stats
        db_stats = data['database']
        assert 'total_records' in db_stats
        assert 'indices_count' in db_stats
    
    def test_analysis_endpoint(self, api_app):
        """Test market analysis endpoint"""
        response = api_app.get('/api/analysis/nifty?date=2025-07-14')
        
        # Should return analysis data or 404 if no analysis available
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            data = json.loads(response.data)
            assert 'index_name' in data
            assert 'date' in data
            assert 'analysis' in data
            assert data['index_name'] == 'nifty'
    
    def test_signals_endpoint(self, api_app):
        """Test trading signals endpoint"""
        response = api_app.get('/api/signals/?confidence=0.7')
        
        # Should return signals data or empty signals
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert 'date' in data
        assert 'confidence_threshold' in data
        assert 'signals' in data
        assert data['confidence_threshold'] == 0.7
    
    def test_data_export_endpoint(self, api_app):
        """Test data export endpoint"""
        response = api_app.get('/api/data/export?index=nifty&start_date=2025-07-14&end_date=2025-07-14')
        
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            data = json.loads(response.data)
            assert 'index_name' in data
            assert 'record_count' in data
            assert 'data' in data
            assert data['index_name'] == 'nifty'
    
    def test_import_jobs_endpoint(self, api_app):
        """Test import jobs listing endpoint"""
        response = api_app.get('/api/data/jobs')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'jobs' in data
        assert isinstance(data['jobs'], list)
    
    def test_api_error_handling(self, api_app):
        """Test API error handling"""
        # Test with invalid index
        response = api_app.get('/api/analysis/invalid_index')
        assert response.status_code == 404
        
        # Test with missing required parameters
        response = api_app.get('/api/data/export')
        assert response.status_code == 400

class TestWebDashboard:
    """Test suite for Web Dashboard"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def test_storage(self, temp_dir):
        """Create test database storage"""
        db_path = temp_dir / "test_dashboard.db"
        storage = TickDataStorage(f"sqlite:///{db_path}")
        storage.init_database()
        return storage
    
    @pytest.fixture
    def dashboard_app(self, test_storage):
        """Create dashboard app for testing"""
        # Mock the storage creation in the dashboard
        with patch('src.web.dashboard.TickDataStorage') as mock_storage:
            mock_storage.return_value = test_storage
            dashboard = TradingDashboard()
            dashboard.app.config['TESTING'] = True
            return dashboard.app.test_client()
    
    def test_dashboard_main_page(self, dashboard_app):
        """Test dashboard main page"""
        response = dashboard_app.get('/')
        
        assert response.status_code == 200
        assert b'Indian Market Tick Data Analysis' in response.data
        assert b'dashboard' in response.data.lower()
    
    def test_dashboard_api_status(self, dashboard_app):
        """Test dashboard API status endpoint"""
        response = dashboard_app.get('/api/status')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'status' in data
        assert 'database' in data
        assert 'timestamp' in data
    
    def test_dashboard_import_jobs(self, dashboard_app):
        """Test dashboard import jobs endpoint"""
        response = dashboard_app.get('/api/import/jobs')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'jobs' in data
        assert isinstance(data['jobs'], list)
    
    def test_dashboard_latest_data(self, dashboard_app):
        """Test dashboard latest data endpoint"""
        response = dashboard_app.get('/api/data/latest')
        
        # Should return data or 404 if no data
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            data = json.loads(response.data)
            assert 'summary' in data
            assert 'timestamp' in data

class TestPlatformManager:
    """Test suite for Platform Manager"""
    
    def test_platform_manager_help(self):
        """Test platform manager help command"""
        result = subprocess.run([
            sys.executable, 'start_platform.py', '--help'
        ], capture_output=True, text=True, cwd=Path(__file__).parent.parent)
        
        assert result.returncode == 0
        assert 'Platform management commands' in result.stdout
        assert 'start' in result.stdout
        assert 'status' in result.stdout
        assert 'demo' in result.stdout
    
    def test_platform_start_help(self):
        """Test platform start command help"""
        result = subprocess.run([
            sys.executable, 'start_platform.py', 'start', '--help'
        ], capture_output=True, text=True, cwd=Path(__file__).parent.parent)
        
        assert result.returncode == 0
        assert 'Start the platform services' in result.stdout
        assert '--dashboard-only' in result.stdout
        assert '--api-only' in result.stdout

class TestIntegrationWorkflows:
    """Test integration between different interface components"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for integration tests"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    def test_cli_to_api_workflow(self, temp_dir):
        """Test workflow from CLI import to API access"""
        # This would be a more complex integration test
        # For now, just test that components can be imported together
        from src.data.import_manager import DataImportManager
        from src.api.endpoints import create_api_app
        from src.web.dashboard import TradingDashboard
        
        # Test that all components can be instantiated
        config = get_config()
        db_path = temp_dir / "integration_test.db"
        storage = TickDataStorage(f"sqlite:///{db_path}")
        storage.init_database()
        
        import_manager = DataImportManager(storage, config)
        api_app = create_api_app(config)
        dashboard = TradingDashboard(config)
        
        # Basic functionality test
        assert import_manager is not None
        assert api_app is not None
        assert dashboard is not None
    
    def test_error_propagation(self):
        """Test that errors are properly handled across interfaces"""
        # Test with invalid database path
        with pytest.raises(Exception):
            storage = TickDataStorage("invalid://path")
            storage.init_database()

if __name__ == '__main__':
    pytest.main([__file__, '-v'])
