"""
Setup script for the Indian Market Tick Data Analysis Platform
"""
import sys
import subprocess
import os
from pathlib import Path
import click
import logging

# Setup basic logging for setup script
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 9):
        logger.error("Python 3.9 or higher is required")
        return False
    logger.info(f"Python version: {sys.version}")
    return True

def install_dependencies():
    """Install required dependencies"""
    logger.info("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logger.info("Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        "tick_data",
        "tick_data/nifty",
        "tick_data/bank_nifty", 
        "tick_data/fin_nifty",
        "tick_data/midcap_nifty",
        "tick_data/nifty_next",
        "models",
        "logs",
        "data/processed",
        "data/features",
        "reports"
    ]
    
    logger.info("Creating directories...")
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")
    
    return True

def setup_environment_file():
    """Setup environment configuration file"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        logger.info("Creating .env file from template...")
        env_file.write_text(env_example.read_text())
        logger.info("Created .env file. Please update it with your configuration.")
    elif env_file.exists():
        logger.info(".env file already exists")
    else:
        logger.warning("No .env.example file found")
    
    return True

def test_configuration():
    """Test basic configuration"""
    try:
        from config import get_config
        config = get_config()
        logger.info("Configuration loaded successfully")
        
        # Test database configuration
        logger.info(f"Database host: {config.database.host}")
        logger.info(f"Database name: {config.database.database}")
        
        return True
    except Exception as e:
        logger.error(f"Configuration test failed: {e}")
        return False

def run_basic_tests():
    """Run basic tests to verify setup"""
    logger.info("Running basic tests...")
    try:
        # Test imports
        from src.data.ingestion import TickDataIngestion
        from src.ml.features import FeatureEngineering
        from src.data.storage import TickDataStorage
        
        logger.info("All imports successful")
        
        # Test feature engineering with dummy data
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        # Create dummy data
        dummy_data = pd.DataFrame({
            'timestamp': pd.date_range('2025-07-15 09:15:00', periods=100, freq='2S'),
            'price': 19500 + np.random.normal(0, 10, 100),
            'volume': np.random.randint(100, 1000, 100),
            'index_name': ['test'] * 100
        })
        
        # Test feature engineering
        fe = FeatureEngineering()
        features = fe.extract_all_features(dummy_data)
        
        if len(features) > 0:
            logger.info("Feature engineering test passed")
        else:
            logger.warning("Feature engineering test returned empty results")
        
        return True
        
    except Exception as e:
        logger.error(f"Basic tests failed: {e}")
        return False

@click.group()
def cli():
    """Setup and management commands for the Tick Data Analysis Platform"""
    pass

@cli.command()
def install():
    """Install and setup the platform"""
    logger.info("Starting platform installation...")
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Installing dependencies", install_dependencies),
        ("Creating directories", create_directories),
        ("Setting up environment file", setup_environment_file),
        ("Testing configuration", test_configuration),
        ("Running basic tests", run_basic_tests)
    ]
    
    for step_name, step_func in steps:
        logger.info(f"Step: {step_name}")
        if not step_func():
            logger.error(f"Setup failed at step: {step_name}")
            sys.exit(1)
    
    logger.info("✅ Platform installation completed successfully!")
    logger.info("\nNext steps:")
    logger.info("1. Update .env file with your database configuration")
    logger.info("2. Setup TimescaleDB database")
    logger.info("3. Run: python src/database/init_db.py --init")
    logger.info("4. Start processing data with: python -m src.data.ingestion")

@cli.command()
@click.option('--check-db', is_flag=True, help='Check database connection')
@click.option('--check-config', is_flag=True, help='Check configuration')
@click.option('--check-deps', is_flag=True, help='Check dependencies')
def check(check_db, check_config, check_deps):
    """Check system status"""
    
    if check_config or not any([check_db, check_deps]):
        logger.info("Checking configuration...")
        if test_configuration():
            logger.info("✅ Configuration OK")
        else:
            logger.error("❌ Configuration failed")
    
    if check_deps or not any([check_db, check_config]):
        logger.info("Checking dependencies...")
        try:
            import pandas, numpy, torch, sqlalchemy
            logger.info("✅ Core dependencies OK")
        except ImportError as e:
            logger.error(f"❌ Missing dependency: {e}")
    
    if check_db or not any([check_config, check_deps]):
        logger.info("Checking database connection...")
        try:
            from src.database.init_db import DatabaseManager
            db_manager = DatabaseManager()
            if db_manager.check_connection():
                logger.info("✅ Database connection OK")
                if db_manager.check_timescaledb_extension():
                    logger.info("✅ TimescaleDB extension OK")
                else:
                    logger.warning("⚠️  TimescaleDB extension not available")
            else:
                logger.error("❌ Database connection failed")
        except Exception as e:
            logger.error(f"❌ Database check failed: {e}")

@cli.command()
def clean():
    """Clean up generated files and directories"""
    logger.info("Cleaning up...")
    
    cleanup_paths = [
        "logs/*.log",
        "models/*.pkl",
        "models/*.pth",
        "data/processed/*",
        "data/features/*",
        "__pycache__",
        "*.pyc",
        ".pytest_cache"
    ]
    
    import glob
    import shutil
    
    for pattern in cleanup_paths:
        for path in glob.glob(pattern, recursive=True):
            path_obj = Path(path)
            try:
                if path_obj.is_file():
                    path_obj.unlink()
                    logger.info(f"Removed file: {path}")
                elif path_obj.is_dir():
                    shutil.rmtree(path)
                    logger.info(f"Removed directory: {path}")
            except Exception as e:
                logger.warning(f"Could not remove {path}: {e}")
    
    logger.info("Cleanup completed")

@cli.command()
def dev_setup():
    """Setup development environment"""
    logger.info("Setting up development environment...")
    
    # Install development dependencies
    dev_deps = [
        "pytest>=7.1.0",
        "pytest-cov>=3.0.0",
        "black>=22.6.0",
        "flake8>=5.0.0",
        "mypy>=0.971",
        "jupyter>=1.0.0",
        "ipykernel>=6.15.0"
    ]
    
    for dep in dev_deps:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            logger.info(f"Installed: {dep}")
        except subprocess.CalledProcessError:
            logger.warning(f"Failed to install: {dep}")
    
    # Setup pre-commit hooks (if available)
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pre-commit"])
        logger.info("Installed pre-commit")
    except subprocess.CalledProcessError:
        logger.warning("Could not install pre-commit")
    
    logger.info("Development environment setup completed")

if __name__ == "__main__":
    cli()
