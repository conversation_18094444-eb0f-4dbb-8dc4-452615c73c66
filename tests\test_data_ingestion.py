"""
Tests for data ingestion functionality
"""
import pytest
import pandas as pd
from pathlib import Path
import tempfile
import os

from src.data.ingestion import TickDataIngestion

class TestTickDataIngestion:
    """Test cases for TickDataIngestion class"""
    
    def test_parse_filename_nifty(self):
        """Test filename parsing for Nifty files"""
        ingestion = TickDataIngestion(None)
        
        # Test various Nifty filename formats
        test_cases = [
            ("Nifty Ticklist ********.csv", ("nifty", "********")),
            ("Nifty Ticklist May ********.csv", ("nifty", "********")),
            ("Nifty Ticklist Jun ********.csv", ("nifty", "********")),
        ]
        
        for filename, expected in test_cases:
            result = ingestion._parse_filename(filename)
            assert result == expected, f"Failed for {filename}: got {result}, expected {expected}"
    
    def test_parse_filename_bank_nifty(self):
        """Test filename parsing for Bank Nifty files"""
        ingestion = TickDataIngestion(None)
        
        test_cases = [
            ("Bank Nifty Ticklist ********.csv", ("bank_nifty", "********")),
            ("Bank Nifty Ticklist Jul ********.csv", ("bank_nifty", "********")),
        ]
        
        for filename, expected in test_cases:
            result = ingestion._parse_filename(filename)
            assert result == expected, f"Failed for {filename}: got {result}, expected {expected}"
    
    def test_parse_filename_other_indices(self):
        """Test filename parsing for other indices"""
        ingestion = TickDataIngestion(None)
        
        test_cases = [
            ("Fin Nifty Ticklist ********.csv", ("fin_nifty", "********")),
            ("Midcap Nifty Ticklist ********.csv", ("midcap_nifty", "********")),
            ("Nifty Nxt Ticklist ********.csv", ("nifty_next", "********")),
        ]
        
        for filename, expected in test_cases:
            result = ingestion._parse_filename(filename)
            assert result == expected, f"Failed for {filename}: got {result}, expected {expected}"
    
    def test_preprocess_tick_data(self):
        """Test tick data preprocessing"""
        ingestion = TickDataIngestion(None)
        
        # Create sample raw data
        raw_data = pd.DataFrame({
            'Time': ['15-07-2025 09:15:01', '15-07-2025 09:15:03', '15-07-2025 09:15:05'],
            'Last Rate': [19500.0, 19501.5, 19499.0],
            'Volume': [1000, 1500, 800]
        })
        
        # Preprocess the data
        processed = ingestion._preprocess_tick_data(raw_data, 'test_nifty')
        
        # Check column names
        expected_columns = ['timestamp', 'price', 'volume', 'index_name']
        assert list(processed.columns) == expected_columns
        
        # Check data types
        assert processed['timestamp'].dtype == 'datetime64[ns]'
        assert processed['index_name'].iloc[0] == 'test_nifty'
        
        # Check sorting
        assert processed['timestamp'].is_monotonic_increasing
        
        # Check values
        assert len(processed) == 3
        assert processed['price'].iloc[0] == 19500.0
        assert processed['volume'].iloc[0] == 1000
    
    def test_process_file_success(self, test_storage, sample_csv_file):
        """Test successful file processing"""
        ingestion = TickDataIngestion(test_storage.engine.url)
        
        # Process the file
        result = ingestion.process_file(str(sample_csv_file))
        
        # Check result
        assert result is True
        
        # Verify data was stored
        stored_data = test_storage.get_tick_data('nifty', '2025-07-15', '2025-07-15')
        assert len(stored_data) > 0
        assert 'timestamp' in stored_data.columns
        assert 'price' in stored_data.columns
        assert 'volume' in stored_data.columns
    
    def test_process_file_nonexistent(self, test_storage):
        """Test processing non-existent file"""
        ingestion = TickDataIngestion(test_storage.engine.url)
        
        # Try to process non-existent file
        result = ingestion.process_file("nonexistent_file.csv")
        
        # Should return False
        assert result is False
    
    def test_process_directory(self, test_storage, tmp_path):
        """Test processing directory with multiple files"""
        ingestion = TickDataIngestion(test_storage.engine.url)
        
        # Create multiple test files
        test_files = [
            "Nifty Ticklist ********.csv",
            "Bank Nifty Ticklist ********.csv",
            "Fin Nifty Ticklist ********.csv"
        ]
        
        # Create sample data for each file
        sample_data = pd.DataFrame({
            'Time': ['15-07-2025 09:15:01', '15-07-2025 09:15:03'],
            'Last Rate': [19500.0, 19501.5],
            'Volume': [1000, 1500]
        })
        
        for filename in test_files:
            file_path = tmp_path / filename
            sample_data.to_csv(file_path, index=False)
        
        # Process directory
        results = ingestion.process_directory(str(tmp_path))
        
        # Check results
        assert results['total_files'] == 3
        assert results['successful'] == 3
        assert results['failed'] == 0
    
    def test_process_directory_empty(self, test_storage, tmp_path):
        """Test processing empty directory"""
        ingestion = TickDataIngestion(test_storage.engine.url)
        
        # Process empty directory
        results = ingestion.process_directory(str(tmp_path))
        
        # Check results
        assert results['total_files'] == 0
        assert results['successful'] == 0
        assert results['failed'] == 0
    
    def test_process_directory_mixed_files(self, test_storage, tmp_path):
        """Test processing directory with mixed file types"""
        ingestion = TickDataIngestion(test_storage.engine.url)
        
        # Create CSV file
        csv_file = tmp_path / "Nifty Ticklist ********.csv"
        sample_data = pd.DataFrame({
            'Time': ['15-07-2025 09:15:01'],
            'Last Rate': [19500.0],
            'Volume': [1000]
        })
        sample_data.to_csv(csv_file, index=False)
        
        # Create non-CSV file
        txt_file = tmp_path / "readme.txt"
        txt_file.write_text("This is not a CSV file")
        
        # Process directory
        results = ingestion.process_directory(str(tmp_path))
        
        # Should only process CSV files
        assert results['total_files'] == 1
        assert results['successful'] == 1
        assert results['failed'] == 0
    
    def test_preprocess_invalid_timestamp(self):
        """Test preprocessing with invalid timestamp format"""
        ingestion = TickDataIngestion(None)
        
        # Create data with invalid timestamp
        raw_data = pd.DataFrame({
            'Time': ['invalid-timestamp', '15-07-2025 09:15:03'],
            'Last Rate': [19500.0, 19501.5],
            'Volume': [1000, 1500]
        })
        
        # Should raise an exception or handle gracefully
        with pytest.raises(Exception):
            ingestion._preprocess_tick_data(raw_data, 'test_nifty')
    
    def test_preprocess_missing_columns(self):
        """Test preprocessing with missing columns"""
        ingestion = TickDataIngestion(None)
        
        # Create data with missing columns
        raw_data = pd.DataFrame({
            'Time': ['15-07-2025 09:15:01'],
            'Last Rate': [19500.0]
            # Missing Volume column
        })
        
        # Should raise an exception
        with pytest.raises(KeyError):
            ingestion._preprocess_tick_data(raw_data, 'test_nifty')
    
    def test_preprocess_empty_dataframe(self):
        """Test preprocessing with empty DataFrame"""
        ingestion = TickDataIngestion(None)
        
        # Create empty DataFrame with correct columns
        raw_data = pd.DataFrame(columns=['Time', 'Last Rate', 'Volume'])
        
        # Process empty data
        processed = ingestion._preprocess_tick_data(raw_data, 'test_nifty')
        
        # Should return empty DataFrame with correct columns
        assert len(processed) == 0
        assert list(processed.columns) == ['timestamp', 'price', 'volume', 'index_name']
