#!/usr/bin/env python3
"""
Simple MVP Test - Core Functionality Validation
Tests the essential components needed for MVP
"""
import sys
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
import pandas as pd

# Add project root to path
sys.path.append(str(Path(__file__).parent))

def test_core_imports():
    """Test that core components can be imported"""
    print("🔍 Testing core imports...")
    
    try:
        from src.data.storage import TickDataStorage
        print("✅ TickDataStorage imported")
        
        from src.data.import_manager import DataImportManager
        print("✅ DataImportManager imported")
        
        from src.strategy.professional_trader import ProfessionalTradingStrategy
        print("✅ ProfessionalTradingStrategy imported")
        
        from src.ml.features import FeatureEngineering
        print("✅ FeatureEngineering imported")
        
        from config import get_config
        print("✅ Config imported")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_database_operations():
    """Test basic database operations"""
    print("\n🗄️  Testing database operations...")
    
    try:
        from src.data.storage import TickDataStorage
        
        # Create temporary database
        temp_dir = Path(tempfile.mkdtemp())
        db_path = temp_dir / "test.db"
        
        storage = TickDataStorage(f"sqlite:///{db_path}")
        storage.init_database()
        print("✅ Database initialized")
        
        # Test basic stats
        stats = storage.get_database_stats()
        print(f"✅ Database stats: {stats['total_records']} records")

        # Close database connection
        storage.engine.dispose()

        # Cleanup
        shutil.rmtree(temp_dir)
        
        return True
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_data_import():
    """Test data import functionality"""
    print("\n📁 Testing data import...")
    
    try:
        from src.data.storage import TickDataStorage
        from src.data.import_manager import DataImportManager
        from config import get_config
        
        # Create temporary environment
        temp_dir = Path(tempfile.mkdtemp())
        db_path = temp_dir / "test.db"
        
        # Create sample data file
        sample_data = pd.DataFrame({
            'Time': ['14-07-2025 09:15:00', '14-07-2025 09:15:02', '14-07-2025 09:15:04'],
            'Last Rate': [25170.0, 25171.5, 25172.0],
            'Volume': [100, 150, 200]
        })
        
        data_file = temp_dir / "Nifty Ticklist 14072025.csv"
        sample_data.to_csv(data_file, index=False)
        print("✅ Sample data file created")
        
        # Test import
        config = get_config()
        storage = TickDataStorage(f"sqlite:///{db_path}")
        storage.init_database()
        
        import_manager = DataImportManager(storage, config)
        job_id = import_manager.create_daily_import_job('2025-07-14', temp_dir)
        success = import_manager.execute_job(job_id)
        
        if success:
            print("✅ Data import successful")
            
            # Verify data was imported
            stats = storage.get_database_stats()
            if stats['total_records'] > 0:
                print(f"✅ Data verified: {stats['total_records']} records imported")
                result = True
            else:
                print("❌ No data found after import")
                result = False
        else:
            print("❌ Data import failed")
            result = False

        # Close database connection
        storage.engine.dispose()

        # Cleanup
        shutil.rmtree(temp_dir)
        
        return result
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_trading_analysis():
    """Test trading analysis functionality"""
    print("\n📈 Testing trading analysis...")
    
    try:
        from src.data.storage import TickDataStorage
        from src.strategy.professional_trader import ProfessionalTradingStrategy
        import numpy as np
        
        # Create temporary database with sample data
        temp_dir = Path(tempfile.mkdtemp())
        db_path = temp_dir / "test.db"
        
        storage = TickDataStorage(f"sqlite:///{db_path}")
        storage.init_database()
        
        # Create realistic sample data
        timestamps = pd.date_range('2025-07-14 09:15:00', periods=100, freq='2S')
        prices = 25170 + np.cumsum(np.random.normal(0, 0.1, 100))
        volumes = np.random.randint(50, 500, 100)
        
        sample_data = pd.DataFrame({
            'timestamp': timestamps,
            'index_name': 'nifty',
            'price': prices,
            'volume': volumes
        })
        
        storage.store_tick_data(sample_data)
        print("✅ Sample tick data stored")
        
        # Test analysis
        strategy = ProfessionalTradingStrategy(storage)
        analysis = strategy.analyze_market_structure('nifty', '2025-07-14')
        
        if analysis and 'volume_analysis' in analysis:
            print("✅ Market analysis successful")
            
            # Test signal generation
            signals = strategy.generate_trading_signals(analysis)
            print(f"✅ Signal generation successful: {len(signals) if signals else 0} signals")
            
            result = True
        else:
            print("❌ Market analysis failed")
            result = False
        
        # Cleanup
        shutil.rmtree(temp_dir)
        
        return result
    except Exception as e:
        print(f"❌ Analysis test failed: {e}")
        return False

def test_web_components():
    """Test web component imports"""
    print("\n🌐 Testing web components...")
    
    try:
        from src.api.endpoints import create_api_app
        print("✅ API endpoints imported")
        
        from src.web.dashboard import TradingDashboard
        print("✅ Web dashboard imported")
        
        return True
    except Exception as e:
        print(f"❌ Web components test failed: {e}")
        return False

def run_mvp_validation():
    """Run complete MVP validation"""
    print("🚀 SIMPLE MVP VALIDATION")
    print("=" * 50)
    
    tests = [
        ("Core Imports", test_core_imports),
        ("Database Operations", test_database_operations),
        ("Data Import", test_data_import),
        ("Trading Analysis", test_trading_analysis),
        ("Web Components", test_web_components)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 MVP VALIDATION SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 MVP READY - All core functionality working!")
        mvp_status = "READY"
    elif passed >= total * 0.8:  # 80% pass rate
        print("⚠️  MVP READY WITH WARNINGS - Most functionality working")
        mvp_status = "READY_WITH_WARNINGS"
    else:
        print("❌ NOT MVP READY - Critical functionality failing")
        mvp_status = "NOT_READY"
    
    # Recommendations
    print("\n📋 RECOMMENDATIONS:")
    
    if not results.get("Core Imports", False):
        print("🚨 CRITICAL: Fix import errors - missing dependencies")
    
    if not results.get("Database Operations", False):
        print("🚨 CRITICAL: Fix database connectivity issues")
    
    if not results.get("Data Import", False):
        print("🚨 CRITICAL: Fix data import functionality")
    
    if not results.get("Trading Analysis", False):
        print("⚠️  HIGH: Fix trading analysis - core business logic")
    
    if not results.get("Web Components", False):
        print("⚠️  MEDIUM: Fix web interface components")
    
    if mvp_status == "READY":
        print("✅ Platform is ready for MVP deployment")
        print("💡 Next steps: Performance testing, user acceptance testing")
    elif mvp_status == "READY_WITH_WARNINGS":
        print("⚠️  Platform is mostly ready - address warnings before production")
    else:
        print("❌ Platform needs significant fixes before MVP")
    
    print("=" * 50)
    
    return mvp_status == "READY" or mvp_status == "READY_WITH_WARNINGS"

if __name__ == '__main__':
    success = run_mvp_validation()
    sys.exit(0 if success else 1)
