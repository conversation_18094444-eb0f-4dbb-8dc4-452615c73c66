#!/usr/bin/env python3
"""
Script to process sample data and train initial models
"""
import sys
from pathlib import Path
import pandas as pd
from datetime import datetime
import sqlite3
from sqlalchemy import text

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import get_config
from src.data.ingestion import TickDataIngestion
from src.data.storage import TickDataStorage
from src.ml.training import ModelTrainingPipeline
from src.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

def create_sqlite_storage():
    """Create SQLite storage for testing without PostgreSQL"""
    # Create in-memory SQLite database for testing
    db_path = "test_tickdata.db"
    connection_string = f"sqlite:///{db_path}"
    
    # Create storage instance
    storage = TickDataStorage(connection_string)
    
    # Create tables manually for SQLite
    engine = storage.engine
    with engine.connect() as conn:
        # Create tick_data table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS tick_data (
            timestamp DATETIME NOT NULL,
            index_name VARCHAR(50) NOT NULL,
            price DECIMAL(10, 2) NOT NULL,
            volume INTEGER NOT NULL
        )
        """))

        # Create index for better performance
        conn.execute(text("""
        CREATE INDEX IF NOT EXISTS idx_tick_data_timestamp_index
        ON tick_data (timestamp, index_name)
        """))

        conn.commit()
    
    logger.info(f"Created SQLite database: {db_path}")
    return storage, connection_string

def process_sample_files(storage, connection_string):
    """Process all sample CSV files"""
    sample_dir = Path("sampledata")
    
    if not sample_dir.exists():
        logger.error("Sample data directory not found")
        return False
    
    # Get configuration
    config = get_config()
    
    # Create ingestion instance
    ingestion = TickDataIngestion(connection_string, config)
    
    # Process all CSV files
    csv_files = list(sample_dir.glob("*.csv"))
    logger.info(f"Found {len(csv_files)} CSV files to process")
    
    successful = 0
    failed = 0
    
    for csv_file in csv_files:
        logger.info(f"Processing: {csv_file.name}")
        
        try:
            if ingestion.process_file(str(csv_file), validate_file=True):
                successful += 1
                logger.info(f"✅ Successfully processed {csv_file.name}")
            else:
                failed += 1
                logger.error(f"❌ Failed to process {csv_file.name}")
        
        except Exception as e:
            failed += 1
            logger.error(f"❌ Error processing {csv_file.name}: {e}")
    
    logger.info(f"Processing complete: {successful} successful, {failed} failed")
    return successful > 0

def get_data_date_range(storage):
    """Get the date range of available data"""
    try:
        with storage.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT
                    MIN(DATE(timestamp)) as min_date,
                    MAX(DATE(timestamp)) as max_date,
                    COUNT(*) as total_records,
                    COUNT(DISTINCT index_name) as unique_indices
                FROM tick_data
            """))

            row = result.fetchone()
            if row:
                return {
                    'min_date': row[0],
                    'max_date': row[1],
                    'total_records': row[2],
                    'unique_indices': row[3]
                }
    except Exception as e:
        logger.error(f"Error getting data range: {e}")
    
    return None

def train_sample_models(connection_string):
    """Train models on sample data"""
    logger.info("Starting model training on sample data")
    
    # Create training pipeline
    config = get_config()
    # Override database connection for SQLite
    config.database.connection_string = connection_string
    
    pipeline = ModelTrainingPipeline(config)
    
    # Get available indices
    storage = TickDataStorage(connection_string)
    
    try:
        with storage.engine.connect() as conn:
            result = conn.execute(text("SELECT DISTINCT index_name FROM tick_data"))
            indices = [row[0] for row in result.fetchall()]
        
        logger.info(f"Found indices: {indices}")
        
        if not indices:
            logger.error("No indices found in database")
            return False
        
        # Get date range
        data_info = get_data_date_range(storage)
        if not data_info:
            logger.error("Could not determine data date range")
            return False
        
        logger.info(f"Data range: {data_info['min_date']} to {data_info['max_date']}")
        logger.info(f"Total records: {data_info['total_records']}")
        
        # Train models for a subset of indices (to save time)
        sample_indices = indices[:2]  # Train on first 2 indices
        
        # Use available date range
        start_date = data_info['min_date']
        end_date = data_info['max_date']
        
        # Train models
        results = pipeline.train_all_models(sample_indices, start_date, end_date)
        
        logger.info("Training results:")
        logger.info(f"Success rate: {results['summary']['success_rate']:.2%}")
        logger.info(f"Successful trainings: {results['summary']['successful_trainings']}")
        logger.info(f"Failed trainings: {results['summary']['failed_trainings']}")
        
        return results['summary']['successful_trainings'] > 0
        
    except Exception as e:
        logger.error(f"Error during model training: {e}")
        return False

def main():
    """Main function"""
    logger.info("Starting sample data processing and model training")
    
    try:
        # Step 1: Create SQLite storage
        logger.info("Step 1: Creating SQLite storage for testing")
        storage, connection_string = create_sqlite_storage()
        
        # Step 2: Process sample files
        logger.info("Step 2: Processing sample CSV files")
        if not process_sample_files(storage, connection_string):
            logger.error("Failed to process sample files")
            return False
        
        # Step 3: Show data summary
        logger.info("Step 3: Data summary")
        data_info = get_data_date_range(storage)
        if data_info:
            logger.info(f"📊 Data Summary:")
            logger.info(f"  Date range: {data_info['min_date']} to {data_info['max_date']}")
            logger.info(f"  Total records: {data_info['total_records']:,}")
            logger.info(f"  Unique indices: {data_info['unique_indices']}")
        
        # Step 4: Train sample models
        logger.info("Step 4: Training sample models")
        if train_sample_models(connection_string):
            logger.info("✅ Sample model training completed successfully")
        else:
            logger.error("❌ Sample model training failed")
            return False
        
        logger.info("🎉 All steps completed successfully!")
        logger.info("\nNext steps:")
        logger.info("1. Set up PostgreSQL with TimescaleDB for production")
        logger.info("2. Run: python cli.py db init")
        logger.info("3. Process full dataset: python cli.py data ingest-dir sampledata")
        logger.info("4. Train production models with full data")
        
        return True
        
    except Exception as e:
        logger.error(f"Script failed with error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
