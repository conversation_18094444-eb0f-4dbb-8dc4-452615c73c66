#!/usr/bin/env python3
"""
Clean import of all tick data from sampledata directory
"""
import sys
from pathlib import Path
import pandas as pd
import os
import re
from datetime import datetime
from sqlalchemy import create_engine, text
import glob

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.data.storage import TickDataStorage
from src.data.ingestion import TickDataIngestion
from src.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

def clear_database(storage):
    """Clear all existing data from the database"""
    logger.info("🗑️ Clearing existing database...")
    
    try:
        with storage.engine.connect() as conn:
            # Drop and recreate tables
            conn.execute(text("DROP TABLE IF EXISTS tick_data"))
            conn.commit()
        
        # Recreate tables
        storage.create_tables()
        logger.info("✅ Database cleared and tables recreated")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to clear database: {e}")
        return False

def parse_filename(filename):
    """Parse filename to extract index name and date"""
    
    # Remove extension
    name = filename.replace('.csv', '')
    
    # Extract index name
    if 'Bank Nifty' in name:
        index_name = 'bank_nifty'
    elif 'Fin Nifty' in name:
        index_name = 'fin_nifty'
    elif 'Midcap Nifty' in name:
        index_name = 'midcap_nifty'
    elif 'Nifty Nxt' in name:
        index_name = 'nifty_next'
    elif 'Nifty Ticklist' in name:
        index_name = 'nifty'
    else:
        return None, None
    
    # Extract date - handle different formats
    date_patterns = [
        r'(\d{2})(\d{2})(\d{4})',  # DDMMYYYY
        r'(\d{1,2})(\d{2})(\d{4})', # D/DDMMYYYY
    ]
    
    for pattern in date_patterns:
        matches = re.findall(pattern, name)
        if matches:
            day, month, year = matches[0]
            try:
                date_obj = datetime(int(year), int(month), int(day))
                return index_name, date_obj.strftime('%Y-%m-%d')
            except ValueError:
                continue
    
    return index_name, None

def get_all_files():
    """Get all CSV files from sampledata directory"""
    sampledata_dir = Path("sampledata")
    
    if not sampledata_dir.exists():
        logger.error("❌ sampledata directory not found")
        return []
    
    csv_files = list(sampledata_dir.glob("*.csv"))
    
    # Parse and sort files
    file_info = []
    for file_path in csv_files:
        index_name, date_str = parse_filename(file_path.name)
        if index_name and date_str:
            file_info.append({
                'path': file_path,
                'filename': file_path.name,
                'index_name': index_name,
                'date': date_str,
                'date_obj': datetime.strptime(date_str, '%Y-%m-%d')
            })
    
    # Sort by date, then by index
    file_info.sort(key=lambda x: (x['date_obj'], x['index_name']))
    
    return file_info

def validate_file_data(file_path):
    """Validate a single file and return basic stats"""
    try:
        df = pd.read_csv(file_path)
        
        if len(df) == 0:
            return None, "Empty file"
        
        # Check required columns - handle different column name formats
        expected_cols = {
            'time': ['Time', 'time', 'TIME'],
            'price': ['Last Rate', 'LTP', 'Price', 'price', 'PRICE'],
            'volume': ['Volume', 'volume', 'VOLUME']
        }

        # Map actual columns to expected
        col_mapping = {}
        for expected, possible in expected_cols.items():
            found = False
            for col in df.columns:
                if col in possible:
                    col_mapping[expected] = col
                    found = True
                    break
            if not found:
                return None, f"Missing {expected} column. Available columns: {list(df.columns)}"
        
        # Get basic stats using mapped columns
        time_col = col_mapping['time']
        price_col = col_mapping['price']
        volume_col = col_mapping['volume']

        stats = {
            'records': len(df),
            'first_time': df[time_col].iloc[0] if len(df) > 0 else None,
            'last_time': df[time_col].iloc[-1] if len(df) > 0 else None,
            'first_price': df[price_col].iloc[0] if len(df) > 0 else None,
            'last_price': df[price_col].iloc[-1] if len(df) > 0 else None,
            'min_price': df[price_col].min(),
            'max_price': df[price_col].max(),
            'total_volume': df[volume_col].sum(),
            'column_mapping': col_mapping
        }
        
        return stats, None
        
    except Exception as e:
        return None, str(e)

def import_single_file(file_info, ingestion):
    """Import a single file and return results"""
    logger.info(f"📁 Processing: {file_info['filename']}")
    
    # Validate file first
    stats, error = validate_file_data(file_info['path'])
    if error:
        logger.error(f"❌ File validation failed: {error}")
        return False, error, None
    
    logger.info(f"   📊 File stats: {stats['records']:,} records, "
               f"₹{stats['first_price']:.2f}-₹{stats['last_price']:.2f}, "
               f"Volume: {stats['total_volume']:,}")
    
    try:
        # Import the file
        result = ingestion.process_file(str(file_info['path']))
        
        if result:
            logger.info(f"   ✅ Imported successfully")
            return True, None, {
                'file_stats': stats,
                'import_result': {'success': True}
            }
        else:
            logger.error(f"   ❌ Import failed")
            return False, "Import failed", stats
            
    except Exception as e:
        logger.error(f"   ❌ Import exception: {e}")
        return False, str(e), stats

def verify_imported_data(storage, file_info, expected_stats):
    """Verify that imported data matches file data"""
    try:
        with storage.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT 
                    COUNT(*) as records,
                    MIN(price) as min_price,
                    MAX(price) as max_price,
                    MIN(timestamp) as first_time,
                    MAX(timestamp) as last_time,
                    SUM(volume) as total_volume
                FROM tick_data 
                WHERE index_name = :index_name 
                AND DATE(timestamp) = :date
            """), {
                'index_name': file_info['index_name'],
                'date': file_info['date']
            })
            
            row = result.fetchone()
            if row:
                db_stats = {
                    'records': row[0],
                    'min_price': row[1],
                    'max_price': row[2],
                    'first_time': row[3],
                    'last_time': row[4],
                    'total_volume': row[5]
                }
                
                # Compare with expected
                matches = (
                    db_stats['records'] == expected_stats['records'] and
                    abs(db_stats['min_price'] - expected_stats['min_price']) < 0.01 and
                    abs(db_stats['max_price'] - expected_stats['max_price']) < 0.01
                )
                
                return matches, db_stats
            else:
                return False, None
                
    except Exception as e:
        logger.error(f"Verification failed: {e}")
        return False, None

def main():
    """Main function"""
    logger.info("🚀 Starting Clean Data Import Process")
    
    # Setup database
    db_path = "clean_tickdata.db"
    connection_string = f"sqlite:///{db_path}"
    
    # Remove existing database
    if Path(db_path).exists():
        Path(db_path).unlink()
        logger.info(f"🗑️ Removed existing database: {db_path}")
    
    try:
        # Initialize components
        storage = TickDataStorage(connection_string)
        ingestion = TickDataIngestion(storage)
        
        # Clear database
        if not clear_database(storage):
            return False
        
        # Get all files
        logger.info("📂 Scanning sampledata directory...")
        all_files = get_all_files()
        
        if not all_files:
            logger.error("❌ No valid CSV files found")
            return False
        
        logger.info(f"📋 Found {len(all_files)} files to process")
        
        # Group by index for summary
        by_index = {}
        for file_info in all_files:
            index = file_info['index_name']
            if index not in by_index:
                by_index[index] = []
            by_index[index].append(file_info)
        
        logger.info("📊 Files by index:")
        for index, files in by_index.items():
            dates = [f['date'] for f in files]
            logger.info(f"  {index}: {len(files)} files ({min(dates)} to {max(dates)})")
        
        # Import all files
        logger.info("\n🔄 Starting import process...")
        
        success_count = 0
        failure_count = 0
        import_summary = []
        
        for i, file_info in enumerate(all_files, 1):
            logger.info(f"\n[{i}/{len(all_files)}] {file_info['index_name']} - {file_info['date']}")
            
            success, error, stats = import_single_file(file_info, ingestion)
            
            if success:
                # Verify imported data
                matches, db_stats = verify_imported_data(storage, file_info, stats['file_stats'])
                
                if matches:
                    logger.info("   ✅ Data verification passed")
                    success_count += 1
                    import_summary.append({
                        'file': file_info['filename'],
                        'index': file_info['index_name'],
                        'date': file_info['date'],
                        'status': 'SUCCESS',
                        'records': stats['file_stats']['records'],
                        'last_price': stats['file_stats']['last_price']
                    })
                else:
                    logger.warning("   ⚠️ Data verification failed - counts don't match")
                    failure_count += 1
                    import_summary.append({
                        'file': file_info['filename'],
                        'index': file_info['index_name'],
                        'date': file_info['date'],
                        'status': 'VERIFICATION_FAILED',
                        'error': 'Data counts mismatch'
                    })
            else:
                failure_count += 1
                import_summary.append({
                    'file': file_info['filename'],
                    'index': file_info['index_name'],
                    'date': file_info['date'],
                    'status': 'FAILED',
                    'error': error
                })
        
        # Final summary
        logger.info("\n📋 IMPORT SUMMARY:")
        logger.info("=" * 60)
        logger.info(f"Total files: {len(all_files)}")
        logger.info(f"Successful: {success_count}")
        logger.info(f"Failed: {failure_count}")
        logger.info(f"Success rate: {success_count/len(all_files)*100:.1f}%")
        
        # Show final database state
        logger.info("\n📊 Final Database State:")
        with storage.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT 
                    index_name,
                    COUNT(*) as total_records,
                    MIN(DATE(timestamp)) as start_date,
                    MAX(DATE(timestamp)) as end_date,
                    COUNT(DISTINCT DATE(timestamp)) as trading_days,
                    MIN(price) as min_price,
                    MAX(price) as max_price
                FROM tick_data 
                GROUP BY index_name
                ORDER BY index_name
            """))
            
            for row in result.fetchall():
                logger.info(f"  {row[0]}: {row[1]:,} records, {row[4]} days "
                           f"({row[2]} to {row[3]}), ₹{row[5]:,.2f}-₹{row[6]:,.2f}")
        
        # Check specific Nifty July 14 data
        logger.info("\n🔍 Checking Nifty July 14 data:")
        with storage.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT timestamp, price, volume
                FROM tick_data 
                WHERE index_name = 'nifty' AND DATE(timestamp) = '2025-07-14'
                ORDER BY timestamp DESC
                LIMIT 5
            """))
            
            rows = result.fetchall()
            if rows:
                logger.info("  Last 5 Nifty ticks on July 14:")
                for row in rows:
                    logger.info(f"    {row[0]}: ₹{row[1]:,.2f} (Vol: {row[2]:,})")
            else:
                logger.error("  ❌ No Nifty data found for July 14!")
        
        # Save summary
        summary_df = pd.DataFrame(import_summary)
        summary_file = f"import_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        summary_df.to_csv(summary_file, index=False)
        logger.info(f"\n💾 Import summary saved to: {summary_file}")
        
        logger.info(f"\n🎉 Clean import completed! Database: {db_path}")
        
        return success_count > 0
        
    except Exception as e:
        logger.error(f"❌ Import process failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
