# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_DATABASE=tickdata
DB_USERNAME=postgres
DB_PASSWORD=your_password_here
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=0
REDIS_PASSWORD=
REDIS_MAX_CONNECTIONS=50

# Machine Learning Configuration
ML_LSTM_HIDDEN_DIM=128
ML_LSTM_NUM_LAYERS=2
ML_LSTM_DROPOUT=0.2
ML_SEQUENCE_LENGTH=100
ML_BATCH_SIZE=32
ML_LEARNING_RATE=0.001
ML_WEIGHT_DECAY=0.00001
ML_EPOCHS=100
ML_EARLY_STOPPING_PATIENCE=10
ML_CONFIDENCE_THRESHOLD=0.7
ML_MAGNITUDE_THRESHOLD=0.3

# Data Processing Configuration
DATA_CHUNK_SIZE=10000
DATA_MAX_FILE_SIZE_MB=500
DATA_PARALLEL_WORKERS=4
DATA_MAX_PRICE_CHANGE_PERCENT=10.0
DATA_MIN_VOLUME=1
DATA_MAX_VOLUME=1000000

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=false
API_SECRET_KEY=your-secret-key-change-in-production
API_ACCESS_TOKEN_EXPIRE_MINUTES=30
API_RATE_LIMIT_PER_MINUTE=100

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=true
LOG_MAX_FILE_SIZE_MB=100
LOG_BACKUP_COUNT=5
