{"tests/test_import_manager.py::TestDataImportManager::test_scan_directory": true, "tests/test_import_manager.py::TestDataImportManager::test_create_daily_import_job": true, "tests/test_import_manager.py::TestDataImportManager::test_create_bulk_import_job": true, "tests/test_import_manager.py::TestDataImportManager::test_execute_daily_job": true, "tests/test_import_manager.py::TestDataImportManager::test_execute_bulk_job": true, "tests/test_import_manager.py::TestDataImportManager::test_job_history_persistence": true, "tests/test_import_manager.py::TestDataImportManager::test_list_jobs": true, "tests/test_import_manager.py::TestDataImportManager::test_cancel_job": true, "tests/test_import_manager.py::TestDataImportManager::test_cleanup_old_jobs": true, "tests/test_import_manager.py::TestDataImportManager::test_date_range_filtering": true, "tests/test_import_manager.py::TestDataImportManager::test_error_handling": true, "tests/test_import_manager.py::TestFileInfoDataClass::test_file_info_creation": true, "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_analyze_market_structure": true, "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_generate_trading_signals": true, "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_volume_breakout_analysis": true, "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_support_resistance_identification": true, "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_order_flow_analysis": true, "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_market_character_assessment": true, "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_risk_reward_calculation": true, "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_confidence_scoring": true, "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_no_data_handling": true, "tests/test_trading_analysis.py::TestProfessionalTradingStrategy::test_multiple_indices": true, "tests/test_trading_analysis.py::TestFeatureEngineering::test_extract_all_features": true, "tests/test_integration.py::TestCompleteWorkflows::test_complete_daily_workflow": true, "tests/test_integration.py::TestCompleteWorkflows::test_bulk_import_and_analysis_workflow": true, "tests/test_integration.py::TestCompleteWorkflows::test_error_recovery_workflow": true, "tests/test_interfaces.py::TestCLIInterface": true, "tests/test_interfaces.py::TestAPIEndpoints": true, "tests/test_interfaces.py::TestWebDashboard": true, "tests/test_interfaces.py::TestPlatformManager": true, "tests/test_interfaces.py::TestIntegrationWorkflows": true, "tests/test_interfaces.py::TestCLIInterface::test_cli_help_command": true, "tests/test_interfaces.py::TestCLIInterface::test_cli_db_commands": true, "tests/test_interfaces.py::TestCLIInterface::test_cli_data_commands": true, "tests/test_interfaces.py::TestCLIInterface::test_cli_trading_commands": true, "tests/test_interfaces.py::TestCLIInterface::test_cli_system_commands": true, "tests/test_interfaces.py::TestAPIEndpoints::test_health_endpoint": true, "tests/test_interfaces.py::TestAPIEndpoints::test_system_status_endpoint": true, "tests/test_interfaces.py::TestAPIEndpoints::test_analysis_endpoint": true, "tests/test_interfaces.py::TestAPIEndpoints::test_signals_endpoint": true, "tests/test_interfaces.py::TestAPIEndpoints::test_data_export_endpoint": true, "tests/test_interfaces.py::TestAPIEndpoints::test_import_jobs_endpoint": true, "tests/test_interfaces.py::TestAPIEndpoints::test_api_error_handling": true, "tests/test_interfaces.py::TestWebDashboard::test_dashboard_main_page": true, "tests/test_interfaces.py::TestWebDashboard::test_dashboard_api_status": true, "tests/test_interfaces.py::TestWebDashboard::test_dashboard_import_jobs": true, "tests/test_interfaces.py::TestWebDashboard::test_dashboard_latest_data": true, "tests/test_interfaces.py::TestIntegrationWorkflows::test_cli_to_api_workflow": true, "tests/test_integration.py::TestCompleteWorkflows::test_performance_workflow": true, "tests/test_integration.py::TestCompleteWorkflows::test_concurrent_operations": true, "tests/test_integration.py::TestCompleteWorkflows::test_data_consistency_workflow": true}