#!/usr/bin/env python3
"""
Professional Trading Analysis Script
Implements real day trading experience with tick-level data analysis
"""
import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
from typing import Dict, List, Any

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.data.storage import TickDataStorage
from src.strategy.professional_trader import ProfessionalTradingStrategy
from src.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

def analyze_trading_day(index_name: str, date: str, storage: TickDataStorage) -> Dict[str, Any]:
    """
    Comprehensive trading day analysis
    """
    strategy = ProfessionalTradingStrategy(storage)
    
    try:
        # Get complete market structure analysis
        analysis = strategy.analyze_market_structure(index_name, date)
        
        # Generate professional trading insights
        insights = generate_professional_insights(analysis)
        
        # Create actionable trading plan
        trading_plan = create_trading_plan(analysis, insights)
        
        return {
            'analysis': analysis,
            'insights': insights,
            'trading_plan': trading_plan
        }
        
    except Exception as e:
        logger.error(f"Analysis failed for {index_name} on {date}: {e}")
        return None

def generate_professional_insights(analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate professional trading insights based on analysis
    """
    insights = {
        'market_character': determine_market_character(analysis),
        'key_observations': [],
        'risk_assessment': assess_market_risk(analysis),
        'opportunity_rating': rate_trading_opportunities(analysis),
        'institutional_activity': detect_institutional_activity(analysis)
    }
    
    # Key observations based on professional experience
    volume_analysis = analysis['volume_analysis']
    order_flow = analysis['order_flow']
    key_levels = analysis['key_levels']
    
    # Volume insights
    if volume_analysis['current_volume_ratio'] > 2.0:
        insights['key_observations'].append({
            'type': 'volume_spike',
            'observation': f"Exceptional volume: {volume_analysis['current_volume_ratio']:.1f}x average",
            'implication': 'Institutional activity or news-driven movement',
            'action': 'Monitor for continuation or reversal patterns'
        })
    
    # Order flow insights
    if abs(order_flow['order_flow_imbalance']) > 0.3:
        direction = 'buying' if order_flow['order_flow_imbalance'] > 0 else 'selling'
        insights['key_observations'].append({
            'type': 'order_flow_imbalance',
            'observation': f"Strong {direction} pressure: {abs(order_flow['order_flow_imbalance']):.1%}",
            'implication': f'Sustained {direction} interest from market participants',
            'action': f'Look for {direction} continuation or exhaustion signals'
        })
    
    # Level insights
    current_price = key_levels['current_price']
    nearby_resistance = key_levels['resistance']
    nearby_support = key_levels['support']
    
    if nearby_resistance:
        distance_to_resistance = (nearby_resistance[0] - current_price) / current_price
        if distance_to_resistance < 0.005:  # Within 0.5%
            insights['key_observations'].append({
                'type': 'resistance_proximity',
                'observation': f"Price near key resistance at ₹{nearby_resistance[0]:.2f}",
                'implication': 'Potential reversal or breakout zone',
                'action': 'Watch for volume confirmation on any breakout attempt'
            })
    
    if nearby_support:
        distance_to_support = (current_price - nearby_support[0]) / current_price
        if distance_to_support < 0.005:  # Within 0.5%
            insights['key_observations'].append({
                'type': 'support_proximity',
                'observation': f"Price near key support at ₹{nearby_support[0]:.2f}",
                'implication': 'Potential bounce or breakdown zone',
                'action': 'Monitor for volume and momentum confirmation'
            })
    
    return insights

def determine_market_character(analysis: Dict[str, Any]) -> Dict[str, str]:
    """
    Determine overall market character for the day
    """
    volume_analysis = analysis['volume_analysis']
    order_flow = analysis['order_flow']
    momentum_shifts = analysis['momentum_shifts']
    
    # Volatility assessment
    price_range = analysis['price_range']
    daily_range_pct = (price_range['high'] - price_range['low']) / price_range['open']
    
    if daily_range_pct > 0.02:
        volatility = 'high'
    elif daily_range_pct > 0.01:
        volatility = 'moderate'
    else:
        volatility = 'low'
    
    # Trend assessment
    price_change_pct = (price_range['close'] - price_range['open']) / price_range['open']
    
    if price_change_pct > 0.005:
        trend = 'bullish'
    elif price_change_pct < -0.005:
        trend = 'bearish'
    else:
        trend = 'sideways'
    
    # Volume character
    if volume_analysis['volume_trend'] == 'increasing':
        volume_character = 'expanding'
    elif volume_analysis['volume_trend'] == 'decreasing':
        volume_character = 'contracting'
    else:
        volume_character = 'stable'
    
    # Market phase
    if len(momentum_shifts) > 5:
        market_phase = 'choppy'
    elif abs(order_flow['order_flow_imbalance']) > 0.2:
        market_phase = 'trending'
    else:
        market_phase = 'consolidating'
    
    return {
        'volatility': volatility,
        'trend': trend,
        'volume_character': volume_character,
        'market_phase': market_phase,
        'overall_character': f"{volatility} volatility, {trend} trend, {market_phase} market"
    }

def assess_market_risk(analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    Assess current market risk levels
    """
    volume_analysis = analysis['volume_analysis']
    exhaustion_patterns = analysis['exhaustion_patterns']
    momentum_shifts = analysis['momentum_shifts']
    
    risk_factors = []
    risk_score = 0  # 0-10 scale
    
    # Volume risk factors
    if volume_analysis['volume_volatility'] > 2.0:
        risk_factors.append("High volume volatility indicates uncertain market conditions")
        risk_score += 2
    
    # Exhaustion risk
    if len(exhaustion_patterns) > 2:
        risk_factors.append("Multiple exhaustion patterns suggest potential reversal")
        risk_score += 2
    
    # Momentum instability
    if len(momentum_shifts) > 3:
        risk_factors.append("Frequent momentum shifts indicate choppy conditions")
        risk_score += 1
    
    # Position sizing recommendation
    if risk_score <= 3:
        position_size = "Normal (2-3% of capital)"
        risk_level = "Low"
    elif risk_score <= 6:
        position_size = "Reduced (1-2% of capital)"
        risk_level = "Moderate"
    else:
        position_size = "Minimal (0.5-1% of capital)"
        risk_level = "High"
    
    return {
        'risk_score': risk_score,
        'risk_level': risk_level,
        'risk_factors': risk_factors,
        'position_size_recommendation': position_size,
        'stop_loss_recommendation': "Tight stops recommended" if risk_score > 5 else "Normal stops acceptable"
    }

def rate_trading_opportunities(analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    Rate current trading opportunities
    """
    signals = analysis['trading_signals']
    volume_analysis = analysis['volume_analysis']
    key_levels = analysis['key_levels']
    
    opportunity_score = 0  # 0-10 scale
    opportunities = []
    
    # Signal quality
    high_confidence_signals = [s for s in signals if s['confidence'] > 0.75]
    if high_confidence_signals:
        opportunity_score += len(high_confidence_signals) * 2
        opportunities.extend([f"High confidence {s['type']} signal" for s in high_confidence_signals])
    
    # Volume confirmation
    if volume_analysis['current_volume_ratio'] > 1.5:
        opportunity_score += 2
        opportunities.append("Strong volume supporting current move")
    
    # Clear levels
    if len(key_levels['resistance']) > 0 and len(key_levels['support']) > 0:
        opportunity_score += 1
        opportunities.append("Clear support/resistance levels for risk management")
    
    # Rate overall opportunity
    if opportunity_score >= 7:
        rating = "Excellent"
    elif opportunity_score >= 5:
        rating = "Good"
    elif opportunity_score >= 3:
        rating = "Fair"
    else:
        rating = "Poor"
    
    return {
        'opportunity_score': min(opportunity_score, 10),
        'rating': rating,
        'opportunities': opportunities,
        'best_signals': high_confidence_signals[:3]  # Top 3 signals
    }

def detect_institutional_activity(analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    Detect signs of institutional activity
    """
    volume_analysis = analysis['volume_analysis']
    order_flow = analysis['order_flow']
    
    institutional_signs = []
    activity_score = 0
    
    # Large volume spikes
    volume_spikes = volume_analysis.get('volume_spikes', [])
    large_spikes = [s for s in volume_spikes if s['volume_ratio'] > 3.0]
    
    if large_spikes:
        activity_score += len(large_spikes)
        institutional_signs.append(f"{len(large_spikes)} large volume spikes detected")
    
    # Sustained order flow imbalance
    if abs(order_flow['order_flow_imbalance']) > 0.25:
        activity_score += 2
        direction = 'accumulation' if order_flow['order_flow_imbalance'] > 0 else 'distribution'
        institutional_signs.append(f"Sustained {direction} pattern in order flow")
    
    # Volume efficiency (price movement per unit volume)
    total_volume = order_flow['total_volume']
    price_range = analysis['price_range']
    price_change = abs(price_range['close'] - price_range['open'])
    
    if total_volume > 0:
        volume_efficiency = price_change / (total_volume / 1000000)  # Price change per million volume
        if volume_efficiency < 0.5:  # Low efficiency = absorption
            activity_score += 1
            institutional_signs.append("Low volume efficiency suggests absorption")
    
    return {
        'activity_score': activity_score,
        'activity_level': 'High' if activity_score >= 4 else 'Moderate' if activity_score >= 2 else 'Low',
        'signs': institutional_signs
    }

def create_trading_plan(analysis: Dict[str, Any], insights: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create actionable trading plan
    """
    signals = analysis['trading_signals']
    risk_assessment = insights['risk_assessment']
    opportunity_rating = insights['opportunity_rating']
    
    # Filter signals by confidence and risk
    viable_signals = []
    for signal in signals:
        if (signal['confidence'] >= 0.7 and 
            risk_assessment['risk_level'] in ['Low', 'Moderate']):
            viable_signals.append(signal)
    
    # Create trading plan
    plan = {
        'market_bias': determine_market_bias(analysis),
        'primary_strategy': select_primary_strategy(insights),
        'entry_signals': viable_signals[:3],  # Top 3 signals
        'risk_management': {
            'position_size': risk_assessment['position_size_recommendation'],
            'stop_loss_style': risk_assessment['stop_loss_recommendation'],
            'max_risk_per_trade': '1% of capital' if risk_assessment['risk_level'] == 'High' else '2% of capital'
        },
        'key_levels_to_watch': {
            'resistance': analysis['key_levels']['resistance'][:2],
            'support': analysis['key_levels']['support'][:2]
        },
        'session_outlook': generate_session_outlook(analysis, insights)
    }
    
    return plan

def determine_market_bias(analysis: Dict[str, Any]) -> str:
    """Determine overall market bias"""
    order_flow = analysis['order_flow']
    timeframe_analysis = analysis.get('timeframe_analysis', {})
    
    # Order flow bias
    if order_flow['order_flow_imbalance'] > 0.1:
        return 'bullish'
    elif order_flow['order_flow_imbalance'] < -0.1:
        return 'bearish'
    else:
        return 'neutral'

def select_primary_strategy(insights: Dict[str, Any]) -> str:
    """Select primary trading strategy based on market conditions"""
    market_character = insights['market_character']
    opportunity_rating = insights['opportunity_rating']
    
    if market_character['market_phase'] == 'trending' and opportunity_rating['rating'] in ['Excellent', 'Good']:
        return 'trend_following'
    elif market_character['volatility'] == 'high':
        return 'range_trading'
    elif market_character['market_phase'] == 'consolidating':
        return 'breakout_trading'
    else:
        return 'wait_for_setup'

def generate_session_outlook(analysis: Dict[str, Any], insights: Dict[str, Any]) -> str:
    """Generate session outlook summary"""
    market_character = insights['market_character']['overall_character']
    opportunity_rating = insights['opportunity_rating']['rating']
    risk_level = insights['risk_assessment']['risk_level']
    
    outlook = f"Market showing {market_character}. "
    outlook += f"Trading opportunities rated as {opportunity_rating} with {risk_level} risk. "
    
    if opportunity_rating in ['Excellent', 'Good'] and risk_level in ['Low', 'Moderate']:
        outlook += "Favorable conditions for active trading."
    elif risk_level == 'High':
        outlook += "Exercise caution and reduce position sizes."
    else:
        outlook += "Wait for clearer setups to develop."
    
    return outlook

def main():
    """Main analysis function"""
    logger.info("🎯 Professional Trading Analysis")
    
    # Use the corrected database
    db_path = "clean_tickdata.db"
    connection_string = f"sqlite:///{db_path}"
    
    if not Path(db_path).exists():
        logger.error(f"Database {db_path} not found")
        return False
    
    try:
        storage = TickDataStorage(connection_string)
        
        # Analyze the latest trading day (July 14, 2025)
        analysis_date = "2025-07-14"
        indices = ['nifty', 'bank_nifty', 'fin_nifty', 'midcap_nifty', 'nifty_next']
        
        all_analyses = {}
        
        for index_name in indices:
            logger.info(f"\n📈 Analyzing {index_name.upper()} for {analysis_date}")
            
            result = analyze_trading_day(index_name, analysis_date, storage)
            
            if result:
                all_analyses[index_name] = result
                
                # Display key insights
                insights = result['insights']
                trading_plan = result['trading_plan']
                
                logger.info(f"Market Character: {insights['market_character']['overall_character']}")
                logger.info(f"Opportunity Rating: {insights['opportunity_rating']['rating']}")
                logger.info(f"Risk Level: {insights['risk_assessment']['risk_level']}")
                logger.info(f"Market Bias: {trading_plan['market_bias']}")
                logger.info(f"Primary Strategy: {trading_plan['primary_strategy']}")
                
                # Show top trading signals
                if trading_plan['entry_signals']:
                    logger.info("🎯 Top Trading Signals:")
                    for i, signal in enumerate(trading_plan['entry_signals'], 1):
                        logger.info(f"  {i}. {signal['type']}: Entry ₹{signal['entry_price']:.2f}, "
                                  f"Target ₹{signal['target']:.2f}, "
                                  f"Confidence {signal['confidence']:.1%}")
                
                logger.info(f"Session Outlook: {trading_plan['session_outlook']}")
            else:
                logger.error(f"Failed to analyze {index_name}")
        
        # Save comprehensive analysis
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        analysis_file = f"professional_analysis_{analysis_date}_{timestamp}.json"
        
        with open(analysis_file, 'w') as f:
            json.dump(all_analyses, f, indent=2, default=str)
        
        logger.info(f"\n💾 Complete analysis saved to: {analysis_file}")
        
        # Generate next-day predictions based on professional analysis
        logger.info(f"\n🔮 PROFESSIONAL PREDICTIONS FOR JULY 15, 2025:")
        logger.info("=" * 60)
        
        for index_name, result in all_analyses.items():
            trading_plan = result['trading_plan']
            current_price = result['analysis']['key_levels']['current_price']
            
            logger.info(f"\n{index_name.upper()}:")
            logger.info(f"  Current Price: ₹{current_price:,.2f}")
            logger.info(f"  Market Bias: {trading_plan['market_bias']}")
            logger.info(f"  Strategy: {trading_plan['primary_strategy']}")
            
            if trading_plan['entry_signals']:
                best_signal = trading_plan['entry_signals'][0]
                logger.info(f"  Best Setup: {best_signal['type']} at ₹{best_signal['entry_price']:.2f}")
                logger.info(f"  Target: ₹{best_signal['target']:.2f}")
                logger.info(f"  Risk: ₹{best_signal['stop_loss']:.2f}")
            
            # Key levels
            resistance = trading_plan['key_levels_to_watch']['resistance']
            support = trading_plan['key_levels_to_watch']['support']
            
            if resistance:
                logger.info(f"  Key Resistance: ₹{resistance[0]:.2f}")
            if support:
                logger.info(f"  Key Support: ₹{support[0]:.2f}")
        
        logger.info("\n🎉 Professional Trading Analysis Complete!")
        
        return True
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
