#!/usr/bin/env python3
"""
MVP Validation Test Runner
Comprehensive testing and validation of the Indian Market Tick Data Analysis Platform
"""
import sys
import subprocess
import time
import json
from pathlib import Path
from datetime import datetime
import tempfile
import shutil
import pandas as pd

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from src.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

class MVPValidator:
    """MVP validation and testing coordinator"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'UNKNOWN',
            'test_results': {},
            'component_status': {},
            'performance_metrics': {},
            'recommendations': []
        }
        
        self.temp_dir = None
    
    def setup_test_environment(self):
        """Setup test environment"""
        logger.info("Setting up test environment...")
        
        # Create temporary directory
        self.temp_dir = Path(tempfile.mkdtemp(prefix='mvp_test_'))
        logger.info(f"Test directory: {self.temp_dir}")
        
        # Create sample data files
        self._create_sample_data()
        
        return True
    
    def _create_sample_data(self):
        """Create sample data files for testing"""
        logger.info("Creating sample data files...")
        
        # Create realistic sample data
        base_data = {
            'Time': ['14-07-2025 09:15:00', '14-07-2025 09:15:02', '14-07-2025 09:15:04'],
            'Last Rate': [25170.0, 25171.5, 25172.0],
            'Volume': [100, 150, 200]
        }
        df = pd.DataFrame(base_data)
        
        # Create files for different indices
        indices = ['Nifty', 'Bank Nifty', 'Fin Nifty']
        for index in indices:
            filename = f"{index} Ticklist ********.csv"
            file_path = self.temp_dir / filename
            df.to_csv(file_path, index=False)
        
        logger.info(f"Created {len(indices)} sample data files")
    
    def run_unit_tests(self):
        """Run unit tests"""
        logger.info("Running unit tests...")
        
        test_files = [
            'tests/test_import_manager.py',
            'tests/test_trading_analysis.py',
            'tests/test_interfaces.py'
        ]
        
        results = {}
        
        for test_file in test_files:
            if Path(test_file).exists():
                logger.info(f"Running {test_file}...")
                
                try:
                    result = subprocess.run([
                        sys.executable, '-m', 'pytest', test_file, '-v', '--tb=short'
                    ], capture_output=True, text=True, timeout=300)
                    
                    results[test_file] = {
                        'returncode': result.returncode,
                        'passed': result.returncode == 0,
                        'stdout': result.stdout,
                        'stderr': result.stderr
                    }
                    
                    if result.returncode == 0:
                        logger.info(f"✅ {test_file} passed")
                    else:
                        logger.error(f"❌ {test_file} failed")
                        
                except subprocess.TimeoutExpired:
                    logger.error(f"⏰ {test_file} timed out")
                    results[test_file] = {
                        'returncode': -1,
                        'passed': False,
                        'error': 'Test timed out'
                    }
                except Exception as e:
                    logger.error(f"💥 {test_file} error: {e}")
                    results[test_file] = {
                        'returncode': -1,
                        'passed': False,
                        'error': str(e)
                    }
            else:
                logger.warning(f"⚠️  Test file not found: {test_file}")
                results[test_file] = {
                    'returncode': -1,
                    'passed': False,
                    'error': 'File not found'
                }
        
        self.results['test_results']['unit_tests'] = results
        
        # Calculate overall unit test status
        passed_tests = sum(1 for r in results.values() if r.get('passed', False))
        total_tests = len(results)
        
        logger.info(f"Unit tests: {passed_tests}/{total_tests} passed")
        return passed_tests == total_tests
    
    def run_integration_tests(self):
        """Run integration tests"""
        logger.info("Running integration tests...")
        
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pytest', 'tests/test_integration.py', '-v', '--tb=short'
            ], capture_output=True, text=True, timeout=600)
            
            integration_result = {
                'returncode': result.returncode,
                'passed': result.returncode == 0,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            self.results['test_results']['integration_tests'] = integration_result
            
            if result.returncode == 0:
                logger.info("✅ Integration tests passed")
                return True
            else:
                logger.error("❌ Integration tests failed")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("⏰ Integration tests timed out")
            self.results['test_results']['integration_tests'] = {
                'returncode': -1,
                'passed': False,
                'error': 'Tests timed out'
            }
            return False
        except Exception as e:
            logger.error(f"💥 Integration tests error: {e}")
            self.results['test_results']['integration_tests'] = {
                'returncode': -1,
                'passed': False,
                'error': str(e)
            }
            return False
    
    def test_cli_functionality(self):
        """Test CLI functionality"""
        logger.info("Testing CLI functionality...")
        
        cli_tests = [
            (['python', 'cli.py', '--help'], 'CLI help'),
            (['python', 'cli.py', 'db', '--help'], 'Database commands'),
            (['python', 'cli.py', 'data', '--help'], 'Data commands'),
            (['python', 'cli.py', 'trading', '--help'], 'Trading commands'),
            (['python', 'cli.py', 'system', '--help'], 'System commands')
        ]
        
        results = {}
        
        for cmd, description in cli_tests:
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                results[description] = {
                    'returncode': result.returncode,
                    'passed': result.returncode == 0,
                    'stdout': result.stdout[:500],  # Truncate output
                    'stderr': result.stderr[:500]
                }
                
                if result.returncode == 0:
                    logger.info(f"✅ {description} works")
                else:
                    logger.error(f"❌ {description} failed")
                    
            except Exception as e:
                logger.error(f"💥 {description} error: {e}")
                results[description] = {
                    'returncode': -1,
                    'passed': False,
                    'error': str(e)
                }
        
        self.results['test_results']['cli_tests'] = results
        
        passed_tests = sum(1 for r in results.values() if r.get('passed', False))
        total_tests = len(results)
        
        logger.info(f"CLI tests: {passed_tests}/{total_tests} passed")
        return passed_tests == total_tests
    
    def test_component_imports(self):
        """Test that all components can be imported"""
        logger.info("Testing component imports...")
        
        components = [
            ('src.data.storage', 'TickDataStorage'),
            ('src.data.import_manager', 'DataImportManager'),
            ('src.strategy.professional_trader', 'ProfessionalTradingStrategy'),
            ('src.ml.features', 'FeatureEngineering'),
            ('src.api.endpoints', 'create_api_app'),
            ('src.web.dashboard', 'TradingDashboard'),
            ('config', 'get_config')
        ]
        
        results = {}
        
        for module_name, class_name in components:
            try:
                module = __import__(module_name, fromlist=[class_name])
                component = getattr(module, class_name)
                
                results[f"{module_name}.{class_name}"] = {
                    'imported': True,
                    'error': None
                }
                
                logger.info(f"✅ {module_name}.{class_name} imported successfully")
                
            except Exception as e:
                logger.error(f"❌ Failed to import {module_name}.{class_name}: {e}")
                results[f"{module_name}.{class_name}"] = {
                    'imported': False,
                    'error': str(e)
                }
        
        self.results['component_status'] = results
        
        successful_imports = sum(1 for r in results.values() if r.get('imported', False))
        total_imports = len(results)
        
        logger.info(f"Component imports: {successful_imports}/{total_imports} successful")
        return successful_imports == total_imports
    
    def test_basic_functionality(self):
        """Test basic functionality with sample data"""
        logger.info("Testing basic functionality...")
        
        try:
            # Test database creation and data import
            from src.data.storage import TickDataStorage
            from src.data.import_manager import DataImportManager
            from config import get_config
            
            config = get_config()
            db_path = self.temp_dir / "test_basic.db"
            storage = TickDataStorage(f"sqlite:///{db_path}")
            storage.init_database()
            
            # Test import manager
            import_manager = DataImportManager(storage, config)
            job_id = import_manager.create_daily_import_job('2025-07-14', self.temp_dir)
            success = import_manager.execute_job(job_id)
            
            if success:
                logger.info("✅ Basic data import works")
                
                # Test analysis
                from src.strategy.professional_trader import ProfessionalTradingStrategy
                strategy = ProfessionalTradingStrategy(storage)
                analysis = strategy.analyze_market_structure('nifty', '2025-07-14')
                
                if analysis:
                    logger.info("✅ Basic analysis works")
                    
                    # Test signal generation
                    signals = strategy.generate_trading_signals(analysis)
                    logger.info(f"✅ Signal generation works (generated {len(signals) if signals else 0} signals)")
                    
                    self.results['test_results']['basic_functionality'] = {
                        'passed': True,
                        'import_success': True,
                        'analysis_success': True,
                        'signals_generated': len(signals) if signals else 0
                    }
                    
                    return True
                else:
                    logger.warning("⚠️  Analysis returned no results")
                    self.results['test_results']['basic_functionality'] = {
                        'passed': False,
                        'import_success': True,
                        'analysis_success': False,
                        'error': 'Analysis returned no results'
                    }
                    return False
            else:
                logger.error("❌ Basic data import failed")
                self.results['test_results']['basic_functionality'] = {
                    'passed': False,
                    'import_success': False,
                    'error': 'Data import failed'
                }
                return False
                
        except Exception as e:
            logger.error(f"💥 Basic functionality test error: {e}")
            self.results['test_results']['basic_functionality'] = {
                'passed': False,
                'error': str(e)
            }
            return False
    
    def measure_performance(self):
        """Measure basic performance metrics"""
        logger.info("Measuring performance...")
        
        try:
            from src.data.storage import TickDataStorage
            from src.data.import_manager import DataImportManager
            from config import get_config
            
            config = get_config()
            db_path = self.temp_dir / "test_performance.db"
            storage = TickDataStorage(f"sqlite:///{db_path}")
            storage.init_database()
            
            # Measure import performance
            start_time = time.time()
            import_manager = DataImportManager(storage, config)
            job_id = import_manager.create_daily_import_job('2025-07-14', self.temp_dir)
            import_manager.execute_job(job_id)
            import_time = time.time() - start_time
            
            # Measure analysis performance
            from src.strategy.professional_trader import ProfessionalTradingStrategy
            strategy = ProfessionalTradingStrategy(storage)
            
            start_time = time.time()
            analysis = strategy.analyze_market_structure('nifty', '2025-07-14')
            analysis_time = time.time() - start_time
            
            self.results['performance_metrics'] = {
                'import_time_seconds': round(import_time, 2),
                'analysis_time_seconds': round(analysis_time, 2),
                'total_time_seconds': round(import_time + analysis_time, 2)
            }
            
            logger.info(f"Performance: Import {import_time:.2f}s, Analysis {analysis_time:.2f}s")
            
            return True
            
        except Exception as e:
            logger.error(f"💥 Performance measurement error: {e}")
            self.results['performance_metrics'] = {'error': str(e)}
            return False
    
    def generate_recommendations(self):
        """Generate MVP readiness recommendations"""
        logger.info("Generating recommendations...")
        
        recommendations = []
        
        # Check test results
        unit_tests = self.results['test_results'].get('unit_tests', {})
        unit_test_success = all(r.get('passed', False) for r in unit_tests.values())
        
        if not unit_test_success:
            recommendations.append({
                'priority': 'HIGH',
                'category': 'Testing',
                'issue': 'Unit tests failing',
                'recommendation': 'Fix failing unit tests before MVP release'
            })
        
        # Check component imports
        component_status = self.results.get('component_status', {})
        import_success = all(r.get('imported', False) for r in component_status.values())
        
        if not import_success:
            recommendations.append({
                'priority': 'CRITICAL',
                'category': 'Dependencies',
                'issue': 'Component import failures',
                'recommendation': 'Fix import errors - missing dependencies or syntax errors'
            })
        
        # Check basic functionality
        basic_func = self.results['test_results'].get('basic_functionality', {})
        if not basic_func.get('passed', False):
            recommendations.append({
                'priority': 'CRITICAL',
                'category': 'Core Functionality',
                'issue': 'Basic functionality not working',
                'recommendation': 'Fix core data import and analysis functionality'
            })
        
        # Check performance
        perf_metrics = self.results.get('performance_metrics', {})
        if perf_metrics.get('import_time_seconds', 0) > 30:
            recommendations.append({
                'priority': 'MEDIUM',
                'category': 'Performance',
                'issue': 'Slow data import',
                'recommendation': 'Optimize data import performance for production use'
            })
        
        # MVP readiness assessment
        critical_issues = [r for r in recommendations if r['priority'] == 'CRITICAL']
        high_issues = [r for r in recommendations if r['priority'] == 'HIGH']
        
        if len(critical_issues) == 0 and len(high_issues) == 0:
            self.results['overall_status'] = 'MVP_READY'
            recommendations.append({
                'priority': 'INFO',
                'category': 'MVP Status',
                'issue': 'System ready for MVP',
                'recommendation': 'Platform is ready for MVP deployment'
            })
        elif len(critical_issues) == 0:
            self.results['overall_status'] = 'MVP_READY_WITH_WARNINGS'
            recommendations.append({
                'priority': 'INFO',
                'category': 'MVP Status',
                'issue': 'MVP ready with minor issues',
                'recommendation': 'Platform is ready for MVP with some improvements needed'
            })
        else:
            self.results['overall_status'] = 'NOT_MVP_READY'
            recommendations.append({
                'priority': 'CRITICAL',
                'category': 'MVP Status',
                'issue': 'Critical issues prevent MVP',
                'recommendation': 'Fix critical issues before MVP deployment'
            })
        
        self.results['recommendations'] = recommendations
    
    def cleanup(self):
        """Cleanup test environment"""
        if self.temp_dir and self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            logger.info("Test environment cleaned up")
    
    def run_full_validation(self):
        """Run complete MVP validation"""
        logger.info("🚀 Starting MVP Validation...")
        
        try:
            # Setup
            self.setup_test_environment()
            
            # Run all tests
            self.test_component_imports()
            self.test_basic_functionality()
            self.test_cli_functionality()
            self.run_unit_tests()
            self.run_integration_tests()
            self.measure_performance()
            
            # Generate recommendations
            self.generate_recommendations()
            
            # Save results
            results_file = Path('mvp_validation_results.json')
            with open(results_file, 'w') as f:
                json.dump(self.results, f, indent=2)
            
            logger.info(f"📊 Results saved to: {results_file}")
            
            # Print summary
            self.print_summary()
            
            return self.results['overall_status'] in ['MVP_READY', 'MVP_READY_WITH_WARNINGS']
            
        except Exception as e:
            logger.error(f"💥 Validation error: {e}")
            self.results['overall_status'] = 'VALIDATION_ERROR'
            self.results['error'] = str(e)
            return False
        
        finally:
            self.cleanup()
    
    def print_summary(self):
        """Print validation summary"""
        print("\n" + "="*80)
        print("🎯 MVP VALIDATION SUMMARY")
        print("="*80)
        
        status = self.results['overall_status']
        if status == 'MVP_READY':
            print("✅ STATUS: MVP READY")
        elif status == 'MVP_READY_WITH_WARNINGS':
            print("⚠️  STATUS: MVP READY WITH WARNINGS")
        elif status == 'NOT_MVP_READY':
            print("❌ STATUS: NOT MVP READY")
        else:
            print(f"💥 STATUS: {status}")
        
        print(f"📅 Validation Date: {self.results['timestamp']}")
        
        # Test summary
        print("\n📊 TEST RESULTS:")
        test_results = self.results.get('test_results', {})
        for test_name, result in test_results.items():
            if isinstance(result, dict) and 'passed' in result:
                status_icon = "✅" if result['passed'] else "❌"
                print(f"  {status_icon} {test_name}")
        
        # Performance summary
        perf = self.results.get('performance_metrics', {})
        if perf and 'error' not in perf:
            print(f"\n⚡ PERFORMANCE:")
            print(f"  Import Time: {perf.get('import_time_seconds', 'N/A')}s")
            print(f"  Analysis Time: {perf.get('analysis_time_seconds', 'N/A')}s")
        
        # Recommendations
        recommendations = self.results.get('recommendations', [])
        critical = [r for r in recommendations if r['priority'] == 'CRITICAL']
        high = [r for r in recommendations if r['priority'] == 'HIGH']
        
        if critical:
            print(f"\n🚨 CRITICAL ISSUES ({len(critical)}):")
            for rec in critical:
                print(f"  • {rec['issue']}: {rec['recommendation']}")
        
        if high:
            print(f"\n⚠️  HIGH PRIORITY ({len(high)}):")
            for rec in high:
                print(f"  • {rec['issue']}: {rec['recommendation']}")
        
        print("\n" + "="*80)

if __name__ == '__main__':
    validator = MVPValidator()
    success = validator.run_full_validation()
    
    sys.exit(0 if success else 1)
