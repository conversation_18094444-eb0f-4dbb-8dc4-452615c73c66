"""
Professional Day Trading Strategy based on real trading experience
Incorporates volume-price analysis, level formation, and multi-timeframe analysis
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.data.storage import TickDataStorage
from src.utils.logging import get_logger

logger = get_logger(__name__)

class ProfessionalTradingStrategy:
    """
    Professional day trading strategy based on real trading experience
    """
    
    def __init__(self, storage: TickDataStorage):
        self.storage = storage
        
        # Trading parameters based on experience
        self.volume_exhaustion_threshold = 2.0  # 2x average volume
        self.momentum_confirmation_ticks = 5    # 5 consecutive ticks
        self.support_resistance_strength = 3    # Minimum touches
        self.breakout_volume_multiplier = 1.5   # Volume confirmation
        
    def analyze_market_structure(self, index_name: str, date: str) -> Dict[str, Any]:
        """
        Analyze complete market structure for the day
        
        Args:
            index_name: Index to analyze
            date: Trading date
            
        Returns:
            Complete market structure analysis
        """
        logger.info(f"Analyzing market structure for {index_name} on {date}")
        
        # Get full day data
        tick_data = self.storage.get_tick_data(index_name, date, date)
        
        if len(tick_data) < 100:
            raise ValueError(f"Insufficient data: {len(tick_data)} ticks")
        
        # 1. Identify key levels (support/resistance)
        key_levels = self.identify_key_levels(tick_data)
        
        # 2. Analyze volume patterns
        volume_analysis = self.analyze_volume_patterns(tick_data)
        
        # 3. Detect momentum shifts
        momentum_shifts = self.detect_momentum_shifts(tick_data)
        
        # 4. Find exhaustion patterns
        exhaustion_patterns = self.find_exhaustion_patterns(tick_data)
        
        # 5. Analyze order flow
        order_flow = self.analyze_order_flow(tick_data)
        
        # 6. Multi-timeframe analysis
        timeframe_analysis = self.multi_timeframe_analysis(tick_data)
        
        return {
            'index_name': index_name,
            'date': date,
            'total_ticks': len(tick_data),
            'price_range': {
                'high': tick_data['price'].max(),
                'low': tick_data['price'].min(),
                'open': tick_data['price'].iloc[0],
                'close': tick_data['price'].iloc[-1]
            },
            'key_levels': key_levels,
            'volume_analysis': volume_analysis,
            'momentum_shifts': momentum_shifts,
            'exhaustion_patterns': exhaustion_patterns,
            'order_flow': order_flow,
            'timeframe_analysis': timeframe_analysis,
            'trading_signals': self.generate_trading_signals(
                tick_data, key_levels, volume_analysis, momentum_shifts
            )
        }
    
    def identify_key_levels(self, tick_data: pd.DataFrame) -> Dict[str, List[float]]:
        """
        Identify key support and resistance levels based on price action
        """
        prices = tick_data['price'].values
        volumes = tick_data['volume'].values
        
        # Find swing highs and lows
        swing_highs = []
        swing_lows = []
        
        window = 20  # 20-tick window for swing identification
        
        for i in range(window, len(prices) - window):
            # Swing high: highest in window
            if prices[i] == max(prices[i-window:i+window+1]):
                swing_highs.append(prices[i])
            
            # Swing low: lowest in window
            if prices[i] == min(prices[i-window:i+window+1]):
                swing_lows.append(prices[i])
        
        # Cluster levels (levels within 0.1% are considered same)
        def cluster_levels(levels, tolerance=0.001):
            if not levels:
                return []
            
            levels = sorted(levels)
            clusters = []
            current_cluster = [levels[0]]
            
            for level in levels[1:]:
                if abs(level - current_cluster[-1]) / current_cluster[-1] <= tolerance:
                    current_cluster.append(level)
                else:
                    clusters.append(np.mean(current_cluster))
                    current_cluster = [level]
            
            clusters.append(np.mean(current_cluster))
            return clusters
        
        # Find volume-weighted levels (where high volume occurred)
        volume_levels = []
        volume_threshold = np.percentile(volumes, 90)  # Top 10% volume
        
        for i, (price, volume) in enumerate(zip(prices, volumes)):
            if volume >= volume_threshold:
                volume_levels.append(price)
        
        resistance_levels = cluster_levels(swing_highs + 
                                         [p for p in volume_levels if p > np.median(prices)])
        support_levels = cluster_levels(swing_lows + 
                                      [p for p in volume_levels if p < np.median(prices)])
        
        # Current price for context
        current_price = prices[-1]
        
        # Filter levels close to current price (within 2%)
        nearby_resistance = [r for r in resistance_levels if r > current_price and (r - current_price) / current_price <= 0.02]
        nearby_support = [s for s in support_levels if s < current_price and (current_price - s) / current_price <= 0.02]
        
        return {
            'resistance': sorted(nearby_resistance)[:3],  # Top 3 resistance
            'support': sorted(nearby_support, reverse=True)[:3],  # Top 3 support
            'all_resistance': sorted(resistance_levels, reverse=True)[:5],
            'all_support': sorted(support_levels, reverse=True)[:5],
            'current_price': current_price
        }
    
    def analyze_volume_patterns(self, tick_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze volume patterns for trading insights
        """
        volumes = tick_data['volume'].values
        prices = tick_data['price'].values
        
        # Volume statistics
        avg_volume = np.mean(volumes)
        volume_std = np.std(volumes)
        
        # Volume spikes (>2 std above mean)
        volume_spikes = []
        for i, (price, volume) in enumerate(zip(prices, volumes)):
            if volume > avg_volume + 2 * volume_std:
                volume_spikes.append({
                    'index': i,
                    'price': price,
                    'volume': volume,
                    'volume_ratio': volume / avg_volume
                })
        
        # Volume exhaustion patterns
        exhaustion_signals = []
        window = 10
        
        for i in range(window, len(volumes) - window):
            recent_volumes = volumes[i-window:i+window]
            recent_prices = prices[i-window:i+window]
            
            # High volume but minimal price movement
            volume_avg = np.mean(recent_volumes)
            price_range = max(recent_prices) - min(recent_prices)
            price_change = abs(recent_prices[-1] - recent_prices[0])
            
            if (volume_avg > avg_volume * 1.5 and 
                price_change < price_range * 0.3):
                exhaustion_signals.append({
                    'index': i,
                    'price': prices[i],
                    'volume_ratio': volume_avg / avg_volume,
                    'price_efficiency': price_change / price_range
                })
        
        # Volume trend analysis
        volume_trend = self.calculate_volume_trend(volumes)
        
        return {
            'average_volume': avg_volume,
            'volume_volatility': volume_std / avg_volume,
            'volume_spikes': volume_spikes[-5:],  # Last 5 spikes
            'exhaustion_signals': exhaustion_signals[-3:],  # Last 3 signals
            'volume_trend': volume_trend,
            'current_volume_ratio': volumes[-1] / avg_volume if len(volumes) > 0 else 0
        }
    
    def detect_momentum_shifts(self, tick_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Detect momentum shifts in price action
        """
        prices = tick_data['price'].values
        volumes = tick_data['volume'].values
        timestamps = tick_data['timestamp'].values
        
        momentum_shifts = []
        
        # Calculate price momentum (rate of change)
        momentum_window = 5
        for i in range(momentum_window, len(prices) - momentum_window):
            # Before and after momentum
            before_momentum = (prices[i] - prices[i-momentum_window]) / prices[i-momentum_window]
            after_momentum = (prices[i+momentum_window] - prices[i]) / prices[i]
            
            # Momentum shift detection
            if (before_momentum > 0.001 and after_momentum < -0.001) or \
               (before_momentum < -0.001 and after_momentum > 0.001):
                
                # Confirm with volume
                volume_confirmation = volumes[i] > np.mean(volumes[i-momentum_window:i+momentum_window])
                
                momentum_shifts.append({
                    'timestamp': timestamps[i],
                    'price': prices[i],
                    'before_momentum': before_momentum,
                    'after_momentum': after_momentum,
                    'volume_confirmation': volume_confirmation,
                    'shift_type': 'bullish_to_bearish' if before_momentum > 0 else 'bearish_to_bullish'
                })
        
        return momentum_shifts[-10:]  # Last 10 shifts
    
    def find_exhaustion_patterns(self, tick_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Find volume exhaustion patterns that signal reversals
        """
        prices = tick_data['price'].values
        volumes = tick_data['volume'].values
        
        exhaustion_patterns = []
        window = 15
        
        for i in range(window, len(prices) - window):
            # Get window data
            window_prices = prices[i-window:i+window]
            window_volumes = volumes[i-window:i+window]
            
            # Pattern 1: Climax volume with reversal
            max_volume_idx = np.argmax(window_volumes)
            max_volume = window_volumes[max_volume_idx]
            avg_volume = np.mean(window_volumes)
            
            if max_volume > avg_volume * 2:  # Volume spike
                # Check for price reversal after volume climax
                price_before = window_prices[max_volume_idx]
                prices_after = window_prices[max_volume_idx+1:]
                
                if len(prices_after) >= 5:
                    # Reversal pattern
                    if (price_before == max(window_prices) and 
                        np.mean(prices_after[-3:]) < price_before * 0.999):
                        
                        exhaustion_patterns.append({
                            'type': 'selling_exhaustion',
                            'price': price_before,
                            'volume_ratio': max_volume / avg_volume,
                            'reversal_strength': (price_before - np.mean(prices_after[-3:])) / price_before
                        })
                    
                    elif (price_before == min(window_prices) and 
                          np.mean(prices_after[-3:]) > price_before * 1.001):
                        
                        exhaustion_patterns.append({
                            'type': 'buying_exhaustion',
                            'price': price_before,
                            'volume_ratio': max_volume / avg_volume,
                            'reversal_strength': (np.mean(prices_after[-3:]) - price_before) / price_before
                        })
        
        return exhaustion_patterns[-5:]  # Last 5 patterns
    
    def analyze_order_flow(self, tick_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze order flow patterns
        """
        prices = tick_data['price'].values
        volumes = tick_data['volume'].values
        
        # Calculate tick direction (up/down/neutral)
        tick_directions = []
        for i in range(1, len(prices)):
            if prices[i] > prices[i-1]:
                tick_directions.append(1)  # Up tick
            elif prices[i] < prices[i-1]:
                tick_directions.append(-1)  # Down tick
            else:
                tick_directions.append(0)  # Neutral tick
        
        # Volume-weighted order flow
        buying_volume = sum(volumes[i+1] for i, direction in enumerate(tick_directions) if direction == 1)
        selling_volume = sum(volumes[i+1] for i, direction in enumerate(tick_directions) if direction == -1)
        neutral_volume = sum(volumes[i+1] for i, direction in enumerate(tick_directions) if direction == 0)
        
        total_volume = buying_volume + selling_volume + neutral_volume
        
        # Order flow imbalance
        if total_volume > 0:
            buying_pressure = buying_volume / total_volume
            selling_pressure = selling_volume / total_volume
            order_flow_imbalance = buying_pressure - selling_pressure
        else:
            buying_pressure = selling_pressure = order_flow_imbalance = 0
        
        # Recent order flow (last 50 ticks)
        recent_directions = tick_directions[-50:] if len(tick_directions) >= 50 else tick_directions
        recent_momentum = sum(recent_directions) / len(recent_directions) if recent_directions else 0
        
        return {
            'buying_pressure': buying_pressure,
            'selling_pressure': selling_pressure,
            'order_flow_imbalance': order_flow_imbalance,
            'recent_momentum': recent_momentum,
            'total_volume': total_volume,
            'flow_strength': abs(order_flow_imbalance)
        }
    
    def multi_timeframe_analysis(self, tick_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Multi-timeframe analysis for different trading horizons
        """
        # Resample to different timeframes
        tick_data['timestamp'] = pd.to_datetime(tick_data['timestamp'])
        tick_data.set_index('timestamp', inplace=True)
        
        timeframes = {
            '1min': tick_data.resample('1T').agg({
                'price': ['first', 'max', 'min', 'last'],
                'volume': 'sum'
            }).dropna(),
            '5min': tick_data.resample('5T').agg({
                'price': ['first', 'max', 'min', 'last'],
                'volume': 'sum'
            }).dropna(),
            '15min': tick_data.resample('15T').agg({
                'price': ['first', 'max', 'min', 'last'],
                'volume': 'sum'
            }).dropna()
        }
        
        analysis = {}
        
        for tf_name, tf_data in timeframes.items():
            if len(tf_data) > 0:
                # Flatten column names
                tf_data.columns = ['_'.join(col).strip() for col in tf_data.columns.values]
                
                # Calculate trend
                closes = tf_data['price_last'].values
                if len(closes) >= 3:
                    short_trend = (closes[-1] - closes[-3]) / closes[-3] if closes[-3] != 0 else 0
                    trend_direction = 'bullish' if short_trend > 0.001 else 'bearish' if short_trend < -0.001 else 'neutral'
                else:
                    short_trend = 0
                    trend_direction = 'neutral'
                
                analysis[tf_name] = {
                    'trend_direction': trend_direction,
                    'trend_strength': abs(short_trend),
                    'bars_count': len(tf_data),
                    'current_price': closes[-1] if len(closes) > 0 else 0
                }
        
        return analysis
    
    def calculate_volume_trend(self, volumes: np.ndarray, window: int = 20) -> str:
        """Calculate volume trend over specified window"""
        if len(volumes) < window * 2:
            return 'insufficient_data'
        
        recent_avg = np.mean(volumes[-window:])
        previous_avg = np.mean(volumes[-window*2:-window])
        
        if recent_avg > previous_avg * 1.1:
            return 'increasing'
        elif recent_avg < previous_avg * 0.9:
            return 'decreasing'
        else:
            return 'stable'
    
    def generate_trading_signals(self, tick_data: pd.DataFrame, key_levels: Dict, 
                                volume_analysis: Dict, momentum_shifts: List) -> List[Dict[str, Any]]:
        """
        Generate actionable trading signals based on analysis
        """
        signals = []
        current_price = tick_data['price'].iloc[-1]
        current_volume_ratio = volume_analysis['current_volume_ratio']
        
        # Signal 1: Support/Resistance breakout with volume confirmation
        for resistance in key_levels['resistance']:
            if (current_price > resistance * 1.001 and  # Above resistance
                current_volume_ratio > 1.5):  # With volume
                signals.append({
                    'type': 'breakout_long',
                    'entry_price': current_price,
                    'target': resistance * 1.005,
                    'stop_loss': resistance * 0.998,
                    'confidence': 0.8,
                    'reason': f'Breakout above resistance {resistance:.2f} with volume confirmation'
                })
        
        for support in key_levels['support']:
            if (current_price < support * 0.999 and  # Below support
                current_volume_ratio > 1.5):  # With volume
                signals.append({
                    'type': 'breakdown_short',
                    'entry_price': current_price,
                    'target': support * 0.995,
                    'stop_loss': support * 1.002,
                    'confidence': 0.8,
                    'reason': f'Breakdown below support {support:.2f} with volume confirmation'
                })
        
        # Signal 2: Volume exhaustion reversal
        if volume_analysis['exhaustion_signals']:
            latest_exhaustion = volume_analysis['exhaustion_signals'][-1]
            if latest_exhaustion['price_efficiency'] < 0.3:  # Low price efficiency
                signals.append({
                    'type': 'exhaustion_reversal',
                    'entry_price': current_price,
                    'target': current_price * (1.003 if current_price < latest_exhaustion['price'] else 0.997),
                    'stop_loss': current_price * (0.998 if current_price < latest_exhaustion['price'] else 1.002),
                    'confidence': 0.7,
                    'reason': 'Volume exhaustion pattern detected'
                })
        
        # Signal 3: Momentum shift confirmation
        if momentum_shifts:
            latest_shift = momentum_shifts[-1]
            if latest_shift['volume_confirmation']:
                direction = 1 if latest_shift['shift_type'] == 'bearish_to_bullish' else -1
                signals.append({
                    'type': 'momentum_shift',
                    'entry_price': current_price,
                    'target': current_price * (1.004 if direction > 0 else 0.996),
                    'stop_loss': current_price * (0.997 if direction > 0 else 1.003),
                    'confidence': 0.75,
                    'reason': f'Momentum shift: {latest_shift["shift_type"]} with volume confirmation'
                })
        
        return signals
