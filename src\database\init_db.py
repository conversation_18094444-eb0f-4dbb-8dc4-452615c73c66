"""
Database initialization and management utilities
"""
import logging
import sys
from pathlib import Path
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from config import get_config
from src.data.storage import TickDataStorage

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages database initialization and maintenance"""
    
    def __init__(self):
        """Initialize database manager"""
        self.config = get_config()
        self.connection_string = self.config.database.connection_string
        
    def check_connection(self) -> bool:
        """
        Check if database connection is working
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            engine = create_engine(self.connection_string)
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                logger.info("Database connection successful")
                return True
        except SQLAlchemyError as e:
            logger.error(f"Database connection failed: {str(e)}")
            return False
    
    def check_timescaledb_extension(self) -> bool:
        """
        Check if TimescaleDB extension is available
        
        Returns:
            bool: True if TimescaleDB is available, False otherwise
        """
        try:
            engine = create_engine(self.connection_string)
            with engine.connect() as conn:
                # Check if TimescaleDB extension exists
                result = conn.execute(text("""
                    SELECT EXISTS(
                        SELECT 1 FROM pg_extension WHERE extname = 'timescaledb'
                    );
                """))
                
                extension_exists = result.scalar()
                
                if extension_exists:
                    logger.info("TimescaleDB extension is available")
                    return True
                else:
                    logger.warning("TimescaleDB extension is not installed")
                    return False
                    
        except SQLAlchemyError as e:
            logger.error(f"Error checking TimescaleDB extension: {str(e)}")
            return False
    
    def initialize_database(self) -> bool:
        """
        Initialize the database with required schema
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            # Create storage instance and initialize schema
            storage = TickDataStorage(self.connection_string)
            
            logger.info("Creating database schema...")
            if storage.create_schema():
                logger.info("Database schema created successfully")
                
                logger.info("Optimizing database...")
                if storage.optimize_database():
                    logger.info("Database optimization completed")
                    return True
                else:
                    logger.warning("Database optimization failed, but schema was created")
                    return True
            else:
                logger.error("Failed to create database schema")
                return False
                
        except Exception as e:
            logger.error(f"Database initialization failed: {str(e)}")
            return False
    
    def create_test_data(self) -> bool:
        """
        Create some test data for validation
        
        Returns:
            bool: True if test data created successfully, False otherwise
        """
        try:
            import pandas as pd
            from datetime import datetime, timedelta
            import numpy as np
            
            # Create sample tick data
            start_time = datetime.now().replace(hour=9, minute=15, second=0, microsecond=0)
            timestamps = [start_time + timedelta(seconds=i*2) for i in range(100)]
            
            test_data = pd.DataFrame({
                'timestamp': timestamps,
                'index_name': 'test_nifty',
                'price': np.random.normal(19500, 50, 100),
                'volume': np.random.randint(100, 1000, 100)
            })
            
            # Store test data
            storage = TickDataStorage(self.connection_string)
            records_added = storage.store_tick_data(test_data)
            
            logger.info(f"Created {records_added} test records")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create test data: {str(e)}")
            return False
    
    def validate_database(self) -> bool:
        """
        Validate database setup and functionality
        
        Returns:
            bool: True if validation successful, False otherwise
        """
        try:
            storage = TickDataStorage(self.connection_string)
            
            # Test basic query
            engine = create_engine(self.connection_string)
            with engine.connect() as conn:
                # Check if tables exist
                tables_query = text("""
                    SELECT table_name FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name IN ('tick_data', 'derived_features', 'predictions')
                """)
                
                result = conn.execute(tables_query)
                tables = [row[0] for row in result]
                
                expected_tables = ['tick_data', 'derived_features', 'predictions']
                missing_tables = set(expected_tables) - set(tables)
                
                if missing_tables:
                    logger.error(f"Missing tables: {missing_tables}")
                    return False
                
                # Check if tick_data is a hypertable
                hypertable_query = text("""
                    SELECT hypertable_name FROM timescaledb_information.hypertables 
                    WHERE hypertable_name = 'tick_data'
                """)
                
                result = conn.execute(hypertable_query)
                hypertables = [row[0] for row in result]
                
                if 'tick_data' not in hypertables:
                    logger.error("tick_data is not configured as a hypertable")
                    return False
                
                logger.info("Database validation successful")
                return True
                
        except Exception as e:
            logger.error(f"Database validation failed: {str(e)}")
            return False
    
    def get_database_stats(self) -> dict:
        """
        Get database statistics
        
        Returns:
            dict: Database statistics
        """
        try:
            engine = create_engine(self.connection_string)
            with engine.connect() as conn:
                # Get table sizes
                size_query = text("""
                    SELECT 
                        schemaname,
                        tablename,
                        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                    FROM pg_tables 
                    WHERE schemaname = 'public'
                    AND tablename IN ('tick_data', 'derived_features', 'predictions')
                """)
                
                result = conn.execute(size_query)
                table_sizes = {row[1]: row[2] for row in result}
                
                # Get record counts
                count_queries = {
                    'tick_data': "SELECT COUNT(*) FROM tick_data",
                    'derived_features': "SELECT COUNT(*) FROM derived_features", 
                    'predictions': "SELECT COUNT(*) FROM predictions"
                }
                
                record_counts = {}
                for table, query in count_queries.items():
                    try:
                        result = conn.execute(text(query))
                        record_counts[table] = result.scalar()
                    except:
                        record_counts[table] = 0
                
                return {
                    'table_sizes': table_sizes,
                    'record_counts': record_counts,
                    'connection_string': self.connection_string.replace(self.config.database.password, '***')
                }
                
        except Exception as e:
            logger.error(f"Failed to get database stats: {str(e)}")
            return {}

def main():
    """Main function for database initialization"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Database initialization and management')
    parser.add_argument('--init', action='store_true', help='Initialize database schema')
    parser.add_argument('--check', action='store_true', help='Check database connection')
    parser.add_argument('--validate', action='store_true', help='Validate database setup')
    parser.add_argument('--stats', action='store_true', help='Show database statistics')
    parser.add_argument('--test-data', action='store_true', help='Create test data')
    
    args = parser.parse_args()
    
    db_manager = DatabaseManager()
    
    if args.check:
        if db_manager.check_connection():
            print("✅ Database connection successful")
            if db_manager.check_timescaledb_extension():
                print("✅ TimescaleDB extension available")
            else:
                print("❌ TimescaleDB extension not available")
        else:
            print("❌ Database connection failed")
            sys.exit(1)
    
    if args.init:
        print("Initializing database...")
        if db_manager.initialize_database():
            print("✅ Database initialized successfully")
        else:
            print("❌ Database initialization failed")
            sys.exit(1)
    
    if args.validate:
        if db_manager.validate_database():
            print("✅ Database validation successful")
        else:
            print("❌ Database validation failed")
            sys.exit(1)
    
    if args.test_data:
        if db_manager.create_test_data():
            print("✅ Test data created successfully")
        else:
            print("❌ Failed to create test data")
            sys.exit(1)
    
    if args.stats:
        stats = db_manager.get_database_stats()
        if stats:
            print("\n📊 Database Statistics:")
            print(f"Connection: {stats['connection_string']}")
            print("\nTable Sizes:")
            for table, size in stats['table_sizes'].items():
                print(f"  {table}: {size}")
            print("\nRecord Counts:")
            for table, count in stats['record_counts'].items():
                print(f"  {table}: {count:,}")
        else:
            print("❌ Failed to get database statistics")

if __name__ == "__main__":
    main()
